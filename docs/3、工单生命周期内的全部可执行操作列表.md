
---

### **工单生命周期内的全部可执行操作列表**

#### **阶段一：创建与分派 (工单的诞生)**
*此阶段的主要状态是【待处理】*

*   **客户 (Customer)**:
    *   `发起请求`: 通过外部渠道提出需求，是所有操作的源头。
*   **客服人员 (Customer Service Representative)**:
    *   `新建工单`: 从无到有创建一张工单。
    *   `录入信息`: 填写所有自定义字段。
    *   `预约服务时间`: 为线下服务设定预约时间。
    *   `应用标签`: 为工单打上分类标签。
    *   `添加抄送人`: 让相关人员知晓此事。
    *   `工单合并`: 在发现重复请求时，将多个工单合并为一个。
    *   `指派工单`: 将工单分配给处理人或团队，这是此阶段的结束动作。
    *   `撤回操作`: 在指派后且对方未处理前，撤回错误的指派。
*   **工单处理人 (Handler)**:
    *   `接收/抢单`: 将工单状态从【待处理】变为【处理中】，标志着下一阶段的开始。
    *   `退回工单`: 因信息不足或分配错误，将【待处理】的工单退回给客服。
    *   `转办工单`: 将【待处理】的工单直接转给更合适的处理人。

#### **阶段二：处理与协作 (工单的核心价值实现)**
*此阶段的主要状态是【处理中】*

*   **工单处理人 (Handler)**:
    *   `工单补记`: 记录所有处理步骤和沟通内容（核心操作）。
    *   `修改标签`: 根据实际情况调整工单标签。
    *   `联系客户`: 主动与客户沟通以获取信息或同步进展。
    *   `邀请协办`: 邀请其他同事或部门共同处理。
    *   `转办工单`: 将自己无法处理的工单转交给他人。
    *   `申请延期`: 在预计无法按时完成时，向管理者申请延长处理时间。
    *   `申请挂起`: 在因等待外部资源而无法继续时，向管理者申请暂停计时。
    *   `申请申诉`: 对工单的分配或某些要求有异议时，发起申诉。
    *   `解挂/恢复处理`: 在挂起条件消失后，恢复工单的处理。
    *   `触发支付/签名`: 对于有偿或需确认的服务，启动相应流程。
    *   `办结工单`: 完成所有处理工作，将工单推向下一阶段（待审核或待回访）。
*   **协办人 (Collaborator)**:
    *   `接收协办任务`: 确认参与协作。
    *   `工单补记`: 记录自己负责部分的处理过程。
    *   `退回协办任务`: 拒绝或退回无法处理的协办请求。
*   **管理者 (Manager)**:
    *   `批准/退回申请`: 对处理人提交的延期、挂起、申诉等申请进行裁决。
    *   `改派工单`: 在处理过程中，强制改变工单的处理人。
    *   `添加/修改抄送人`: 在处理中调整信息知晓范围。

#### **阶段三：内部审核 (第一道质量关)**
*此阶段的主要状态是【待审核】*

*   **管理者 (Manager)**:
    *   `审核通过`: 认可处理人的工作，将工单推向下一阶段（待回访）。
    *   `审核退回`: 不认可处理人的工作，将工单退回至【处理中】状态，要求返工。

#### **阶段四：客户回访与最终关闭 (第二道质量关)**
*此阶段的主要状态是【待回访】*

*   **回访员 (Revisit Agent)**:
    *   `执行回访`: 主动联系客户。
    *   `录入回访结果`: 详细记录客户的反馈。
    *   `录入满意度评价`: 将客户的评分录入系统。
    *   `关闭工单`: 在客户确认满意后，将工单的生命周期彻底终结，状态变为【已关闭】。
    *   `重启工单`: 在客户表示不满意时，重新激活工单，使其回到【处理中】状态。

#### **贯穿全程的全局/特殊操作**

这些操作不严格属于某个阶段，而是可以在多个阶段由特定权限的角色执行。

*   **所有参与角色 (客服, 处理人, 协办人, 回访员, 管理者)**:
    *   `访问“我的工单”视图`: 查看与自己相关的任务列表。
    *   `查看工单时间轴`: 追溯工单的完整历史记录。
    *   `查看工单积分`: 了解与工单相关的绩效得分情况。
*   **客服人员 / 管理者**:
    *   `重启工单`: 在工单【已关闭】后，如客户反馈问题复现，可由客服或管理者找到历史工单并重新激活。
*   **管理者**:
    *   `废除工单`: 将无效工单作废，使其进入【已废除】状态。
    *   `置顶工单`: 提高某个工单的显示优先级。
    *   `批量处理`: 同时对多个工单执行相同的操作（如批量关闭、批量指派）。
    *   `查看自定义报表`: 从宏观层面分析所有工单数据。
    *   `监控地图轨迹/超时工单`: 实时监督服务过程和SLA状态。

---

### **最终覆盖性核对清单**

| **基准清单中的操作项** | **在“生命周期列表”中的位置** | **覆盖状态** |
| :--- | :--- | :---: |
| **1. 客服人员** | | |
| `新建工单` | 阶段一：创建与分派 | ✅ |
| `指派工单` | 阶段一：创建与分派 | ✅ |
| `预约服务时间` | 阶段一：创建与分派 | ✅ |
| `应用标签` | 阶段一：创建与分派 | ✅ |
| `工单合并` | 阶段一：创建与分派 | ✅ |
| `工单重启` | 全局/特殊操作 | ✅ |
| `接收并处理被退回的工单` | 暗含在“退回工单”操作的后果中 | ✅ |
| `撤回操作` | 全局/特殊操作 | ✅ |
| `抄送工单` | 阶段一：创建与分派 | ✅ |
| `访问“我的工单”视图` | 全局/特殊操作 | ✅ |
| `工单进度查询` | 暗含在“查看工单时间轴”中 | ✅ |
| `工单补记` | 阶段一：创建与分派 (录入信息) | ✅ |
| `使用自定义回复模板` | 这是执行沟通时的工具，未列为独立操作，但概念已覆盖 | ✅ |
| `录入工单评价` | 阶段四：客户回访与最终关闭 | ✅ |
| **2. 工单处理人** | | |
| `访问“我的工单”视图` | 全局/特殊操作 | ✅ |
| `接收/抢单` | 阶段一：创建与分派 | ✅ |
| `办结工单` | 阶段二：处理与协作 | ✅ |
| `退回工单` | 阶段一：创建与分派 | ✅ |
| `撤回操作` | 贯穿于其负责的多个阶段 | ✅ |
| `转办工单` | 阶段一 & 阶段二 | ✅ |
| `邀请协办` | 阶段二：处理与协作 | ✅ |
| `申请延期/挂起/申诉` | 阶段二：处理与协作 | ✅ |
| `抄送工单` | 阶段二：处理与协作 | ✅ |
| `回访客户` | 阶段二：处理与协作 (联系客户) | ✅ |
| `触发支付/签名` | 阶段二：处理与协作 | ✅ |
| `工单补记` | 阶段二：处理与协作 | ✅ |
| `应用/修改标签` | 阶段二：处理与协作 | ✅ |
| `查看工单积分` | 全局/特殊操作 | ✅ |
| **3. 协办人** | | |
| `访问“我的工单”视图` | 全局/特殊操作 | ✅ |
| `工单补记` | 阶段二：处理与协作 | ✅ |
| `拒绝/退回协办任务` | 阶段二：处理与协作 | ✅ |
| **4. 回访员** | | |
| `访问“我的工单”/“待回访队列”` | 全局/特殊操作 & 阶段四 | ✅ |
| `执行回访` | 阶段四：客户回访与最终关闭 | ✅ |
| `录入回访结果/满意度评价` | 阶段四：客户回访与最终关闭 | ✅ |
| `关闭工单 (最终)` | 阶段四：客户回访与最终关闭 | ✅ |
| `重启工单` | 阶段四：客户回访与最终关闭 | ✅ |
| `查看工单时间轴/补记` | 全局/特殊操作 | ✅ |
| `应用标签` | 这是回访员在记录时可用的功能 | ✅ |
| **5. 管理者** | | |
| `审核（通过/退回）` | 阶段三：内部审核 | ✅ |
| `审批（批准/退回）` | 阶段二：处理与协作 | ✅ |
| `指派/改派/废除/置顶/批量处理` | 全局/特殊操作 & 多个阶段 | ✅ |
| `查看自定义报表/监控` | 全局/特殊操作 | ✅ |
| **6. 系统管理员** | | |
| `后台配置` | 这是系统运行的前提，非生命周期内的具体操作，概念已覆盖 | ✅ |
| **7. 客户** | | |
| `发起请求` | 阶段一：创建与分派 | ✅ |
| `配合处理/确认与反馈` | 贯穿全程的外部交互 | ✅ |

---