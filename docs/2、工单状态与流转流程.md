
---

### **工单状态与流转流程终极版报告**

#### **第一部分：工单状态详解**

工单的状态是其生命周期的核心标记，它决定了工单当前所处的阶段、责任人以及可执行的操作。本系统包含以下一套完整、逻辑严谨的状态。

##### **一、 核心生命周期状态**
这些状态构成了工单从创建到最终关闭的主干流程。

1.  **待处理 (Pending / Open)**
    *   **定义**: 工单已被成功创建并指派给处理人，但对方尚未开始处理。
    *   **触发**: 客服人员创建并指派工单后。
    *   **负责人**: 被指派的工单处理人。

2.  **处理中 (In Progress / Processing)**
    *   **定义**: 工单处理人已接收任务，并正在着手解决问题。这是核心工作阶段。
    *   **触发**: 处理人接收“待处理”的工单。
    *   **负责人**: 工单处理人（主办方）。

3.  **待审核 (Pending Review)**
    *   **定义**: 处理人已完成处理并提交了解决方案，工单等待其主管或质检部门进行内部审核。
    *   **触发**: 处理人办结“处理中”的工单（在配置了审核的流程中）。
    *   **负责人**: 被指定的审核人（通常是管理者）。

4.  **待回访 (Pending Revisit)**
    *   **定义**: 工单已通过内部审核（或无需审核），等待回访员联系客户进行最终的满意度确认。
    *   **触发**: 管理者“审核通过”，或在无需审核的流程中由处理人直接“办结”。
    *   **负责人**: 回访员团队。

5.  **已关闭 (Closed)**
    *   **定义**: 工单已得到最终确认，问题解决，客户满意。工单生命周期结束，归档为历史记录。
    *   **触发**: 回访员确认客户满意后执行“关闭工单”。
    *   **负责人**: 无（已归档）。

##### **二、 特殊与过渡状态**
这些状态用于处理流程中的各种异常、暂停和审批情况。

6.  **已挂起 (On-Hold / Suspended)**
    *   **定义**: 因等待外部资源（如客户回复），工单处理暂停，SLA计时器也随之暂停。
    *   **触发**: 管理者批准了处理人提交的“挂起申请”。
    *   **负责人**: 工单处理人（处于等待状态）。

7.  **待审批 (Pending Approval)**
    *   **定义**: 工单中的某个申请（如延期、挂起）需要管理者批准。
    *   **触发**: 处理人提交需要审批的申请。
    *   **负责人**: 被指定的审批人（管理者）。

8.  **已退回 (Returned / Rejected)**
    *   **定义**: 工单因故被当前节点退还给上一节点，要求修正。
    *   **触发**: 处理人/协办人/管理者执行“退回”操作（例如，管理者“审核退回”）。
    *   **负责人**: 被退回到的节点负责人（如客服、处理人）。

9.  **已撤回 (Withdrawn)**
    *   **定义**: 由发起人主动收回自己刚提交但对方尚未处理的操作，用于即时纠错。
    *   **触发**: 发起人执行“撤回”操作。
    *   **负责人**: 负责人恢复为操作前的角色。

10. **已废除 (Cancelled / Voided)**
    *   **定义**: 工单因错误创建或客户取消等原因被作废。
    *   **触发**: 管理者或有权限的角色执行“废除”操作。
    *   **负责人**: 无（已归档）。

---

### **第二部分：系统运行流程详解 (完整版)**

#### **阶段一：工单的创建与分派**
1.  **触发与创建**: 客户提出请求，**客服人员**在系统中**新建工单**，录入所有必要信息（包括预约时间、标签、抄送人等）。
2.  **分派**: 客服根据系统规则或手动操作，将工单**指派**给处理人。工单进入**【待处理】**状态。

#### **阶段二：工单的处理与协作**
3.  **接收**: **处理人**在其工作台中接收工单，工单进入**【处理中】**状态。
4.  **执行**: 处理人解决问题，并通过**工单补记**、**邀请协办**、**转办**等方式进行工作和协作。
5.  **处理中特殊情况**:
    *   如需暂停，可**申请挂起**，工单进入**【待审批】**，批准后进入**【已挂起】**，恢复后回到**【处理中】**。
    *   如发现信息不足或分配错误，可**退回**工单，工单进入**【已退回】**状态。
6.  **办结**: 处理人完成工作后，点击“办结”。

#### **阶段三：内部审核 (质量控制第一关)**
7.  **进入审核**: 对于配置了审核的流程，工单在办结后自动进入**【待审核】**状态。
8.  **管理者审核**: **管理者**审查处理人的工作。
    *   **场景A：审核通过**: 管理者点击“通过”，工单进入下一阶段。
    *   **场景B：审核不通过**: 管理者点击“**退回**”，工单进入**【已退回】**状态，并重新流转给原处理人返工（回到步骤4）。

#### **阶段四：客户回访与最终关闭 (质量控制第二关)**
9.  **进入回访队列**: 工单通过审核后（或在无需审核的流程中办结后），进入**【待回访】**状态。
10. **执行回访**: **回访员**联系客户，确认服务结果和满意度。
11. **最终关闭**:
    *   **场景A：客户满意**: 回访员执行“关闭工单”，工单进入最终的**【已关闭】**状态，生命周期结束。
    *   **场景B：客户不满意**: 回访员执行“**重启工单**”，工单被重新激活，回到**【处理中】**状态，交由处理人进行二次处理（回到步骤4）。

---

### **第三部分：状态流转图 (完整版)**

```mermaid
graph TD
    subgraph "主流程"
        A(新建工单) --> B[待处理];
        B --> C{处理中};
        C -- 办结 --> K[待审核];
        K -- 审核通过 --> D[待回访];
        D -- 客户满意 --> E[已关闭];
    end

    subgraph "逆向/闭环流程"
        K -- 审核退回 --> C;
        D -- 客户不满意 --> F((重启));
        F --> C;
        E -- 问题复现 --> F;
    end
    
    subgraph "旁路/可选流程"
        C -- 无需审核的流程 --> D;
        B -- 处理人退回 --> A;
    end
    
    subgraph "特殊状态变化"
        C -- 申请挂起 --> G[待审批];
        G -- 批准 --> H[已挂起];
        H -- 解挂 --> C;
        G -- 拒绝 --> C;
    end

```

[![](https://mermaid.ink/img/pako:eNqFk1lP20AQx7-KtU-JFA7n8FWpEiQcEupbn4r74NaGRCJxZGypbRTJqULSBELSiyDaIqBU7QsJKqLOIZUvk_XxLbrebYTBlbpPuzu_mfnPzG4JPFdlBQhgU5OKWepxRixQaG0bz8iFCCaDsX1dcX7sioDY_LUQsQ8u4XgErW-w9TFKzcw8pBbX4e8deF51OrWnD27RRWxMl4hlMrgoB4xpZKRg89gZv8fYGo7RO7VPBsEYaxjD15555N7UMZzB8Kdjt3cThDMEPrPfWPb41K62Mby0Dq2fcOfK615MYaUgi4VQvZ5Zg523c4hz9vvhyu9IMVF2Ut6_808GrYCE5UjEq7dgpx-NBvDlUIAlP4DX7Xlnh_C85exfEu-gan8TUm53K67Vn4Ptvmc2wspxr-3uiffZJPqdoyqhSDfvzYz6O7DRKFDnwn9FOI2h3Ws6zV-2WYHtQ7h3ENLgfLhy-5a999q9tnDUlenUG8PgIFew4MYQ1msYW_VHSNyC2KqPud-_IkuolSTE7jtn_OWODekHMfTmczIQdM1QYiCvaHnJP4KSj4hAzyp5RQQC2srKhmRs6X4hZeRWlApPVDU_9dRUYzMLhA1paxudjKIs6UomJ6GG3CIon6KlVaOgA4FJ4BBAKIEXQKB5dpZJpnguzvJJjk3STAy8BEIyMcvSXJzn6CSbolNMOQZe4ZzzszzDxRk-kYjTXIqdZ-gYUOScrmqPyEfG_7n8B7Pnc5I?type=png)](https://mermaid-live.nodejs.cn/edit#pako:eNqFk1lP20AQx7-KtU-JFA7n8FWpEiQcEupbn4r74NaGRCJxZGypbRTJqULSBELSiyDaIqBU7QsJKqLOIZUvk_XxLbrebYTBlbpPuzu_mfnPzG4JPFdlBQhgU5OKWepxRixQaG0bz8iFCCaDsX1dcX7sioDY_LUQsQ8u4XgErW-w9TFKzcw8pBbX4e8deF51OrWnD27RRWxMl4hlMrgoB4xpZKRg89gZv8fYGo7RO7VPBsEYaxjD15555N7UMZzB8Kdjt3cThDMEPrPfWPb41K62Mby0Dq2fcOfK615MYaUgi4VQvZ5Zg523c4hz9vvhyu9IMVF2Ut6_808GrYCE5UjEq7dgpx-NBvDlUIAlP4DX7Xlnh_C85exfEu-gan8TUm53K67Vn4Ptvmc2wspxr-3uiffZJPqdoyqhSDfvzYz6O7DRKFDnwn9FOI2h3Ws6zV-2WYHtQ7h3ENLgfLhy-5a999q9tnDUlenUG8PgIFew4MYQ1msYW_VHSNyC2KqPud-_IkuolSTE7jtn_OWODekHMfTmczIQdM1QYiCvaHnJP4KSj4hAzyp5RQQC2srKhmRs6X4hZeRWlApPVDU_9dRUYzMLhA1paxudjKIs6UomJ6GG3CIon6KlVaOgA4FJ4BBAKIEXQKB5dpZJpnguzvJJjk3STAy8BEIyMcvSXJzn6CSbolNMOQZe4ZzzszzDxRk-kYjTXIqdZ-gYUOScrmqPyEfG_7n8B7Pnc5I)