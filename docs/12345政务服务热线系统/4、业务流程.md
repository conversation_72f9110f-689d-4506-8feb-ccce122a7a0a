
---

### **"12345热线"工单系统完整业务流程**

#### **阶段一：市级统一受理与顶层战略分派**

1.  **受理创建**:
    *   **市民**通过12345热线提出诉求。
    *   **市级12345中心话务员**接听电话，在系统中**新建工单**，录入所有标准化信息。

2.  **一级分派决策 (市级层面的"条块"战略选择)**:
    *   **市级话务员/分派员**进行首次、也是最关键的判断：**"此诉求的责任主体是市级直属部门（条），还是应下沉至区级属地管理（块）？"**
    
    *   **场景A：即时办结 (第一效率出口)**
        *   对于"政策咨询"、"信息查询"、"简单告知"等诉求，话务员利用知识库直接答复市民。
        *   在市民表示理解或问题解决后，话务员选择"**即时办结**"，填写办结说明。
        *   工单**跳过所有下派环节**，直接进入**【已关闭】**状态。
    
    *   **场景B：派发至市级职能部门 (条线处理)**
        *   **适用情况**: 诉求涉及全市范围的宏观政策、跨区域的重大事件、或明确由市级部门垂直管理的事项（如全市性的交通设施问题、市级审批的企业投诉等）。
        *   **操作**: 市级分派员将工单直接派发至对应的**【市级职能部门】**。工单随即进入**市级部门内部**的流转、处理与审核流程。
    
    *   **场景C：派发至区/县级总口 (块线处理)**
        *   **适用情况**: 绝大部分涉及具体地点、具体民生、需要属地化管理的诉求。
        *   **操作**: 市级分派员根据**地域属性**，将工单派发至对应的**【区/县级12345分中心】**。
    
    *   **场景D：市级层面的多部门协同**
        *   **适用情况**: 需要市级部门与区级政府协同的重大复杂问题。
        *   **操作**: 市级分派员选择多个承办部门，指定一个**主办部门**（负责总协调和主要责任），其余为**协办部门**（提供专业支持或配合处理）。
        *   工单被一次性派发给主办部门和所有协办部门，启动协同处理流程。

#### **阶段二：工单进入不同轨道的后续流转**

##### **轨道一：市级职能部门内部的流转与处理**

3.  **市级部门接收与分派**:
    *   **市级职能部门**的总口接收工单。
    *   判断能否**即时办结**。如果能，则直接办结，**跳至阶段四**。
    *   如果不能，则根据职责，将工单下派至**【内部具体的业务处室/科室】**。

4.  **市级科室处理与执行**:
    *   **市级业务科室**接收工单。
    *   判断能否**即时办结**。如果能，则直接办结，**跳至阶段四**。
    *   如果不能，则指派给**【具体的工作人员】**进行处理。

5.  **市级工作人员执行**:
    *   工作人员执行任务（如调查、出具报告、联系相关企业等），并在系统中**补记**过程。
    *   任务完成后，点击"**办结**"，**进入阶段四**。

##### **轨道二：下沉至区/县后的复杂流转与处理**

3.  **区级总口接收与分派**:
    *   **区/县12345分中心**接收来自市级的工单。
    *   判断能否**即时办结**。如果能，则直接办结，**跳至阶段四**。
    *   如果不能，则进行**区内二次分派 (路径选择)**:
        *   **A. 派发至街/镇 (属地路线)**: 将工单派发至**【街/镇级总承办单位】**。
        *   **B. 派发至区职能部门 (职能路线)**: 将工单派发至**【区级职能部门】**。
        *   **C. 构建区级多部门协同**: 派发给街/镇和区职能部门，指定主办和协办部门。

4.  **区级下沉后的处理与执行**:
    *   **在属地路线**:
        *   **街/镇总承办单位**接收工单，判断能否**即时办结**。
        *   如不能，则下派至**【街道内设部门】**或**【社区/村委会】**。
        *   **社区/村委会**接收后，判断能否**即时办结**。
        *   如不能，则最终指派给**【网格员】**。
        *   **网格员**执行现场任务，完成后点击"**办结**"，**进入阶段四**。
    *   **在职能路线**:
        *   **区级职能部门**接收工单，判断能否**即时办结**。
        *   如不能，则下派至**【业务科室】**。
        *   **业务科室**接收后，判断能否**即时办结**。
        *   如不能，则最终指派给**【一线工作人员】**。
        *   **工作人员**执行任务，完成后点击"**办结**"，**进入阶段四**。

#### **阶段三：工单的逐级下沉与多重办结机会**

5.  **多轨道并行下派与逐级判断**:
    *   工单沿着各自的轨道（无论是市级部门内部，还是区级下沉路线）下派。**每一个接收到工单的层级**，在进行下一步分派前，都**必须先进行"能否即时办结"的判断**。
    *   只有在确认本级无法独立解决时，才会继续**向下派发**。

6.  **最终执行与办结**:
    *   若工单最终下沉至**最终执行者**（网格员/工作人员），则由其前往现场处理并"**办结**"，触发审核流程。

#### **阶段四：逐级审核反馈与闭环**

7.  **启动审核**: 任何一个层级的"执行办结"或"即时办结"操作完成后，工单都进入**【待审核】**状态。

8.  **逐级审核 (谁派发，谁审核)**:
    *   工单严格按照其"来时"的路径，**自下而上、原路返回**。每一级的管理者都需要对下一级提交的办结结果进行**审核**。审核不通过可**退回**。
    *   **核心原则**: 工单由谁派来，就由谁审核。审核路径是派发路径的精确逆过程。
    *   **审核路径示例1 (属地路线)**:
        *   `网格员`办结 -> 由`社区`审核 -> `社区`通过 -> 由`街道`审核 -> `街道`通过 -> 由`区级分中心`审核 -> `区级分中心`通过 -> **进入阶段五**。
    *   **审核路径示例2 (职能路线)**:
        *   `区工作人员`办结 -> 由`区业务科室`审核 -> `科室`通过 -> 由`区职能部门`审核 -> `部门`通过 -> 由`区级分中心`审核 -> `区级分中心`通过 -> **进入阶段五**。
    *   **审核路径示例3 (市级路线)**:
        *   `市工作人员`办结 -> 由`市业务处室`审核 -> `处室`通过 -> 由`市职能部门`审核 -> `部门`通过 -> **进入阶段五**。
    *   在任何一级审核中，审核人都可以执行"**退回**"，将工单打回给派发对象，要求返工或补充说明。

9.  **向市级平台反馈**:
    *   无论是从**市级职能部门**办结返回，还是从**区/县12345分中心**办结返回，最终都会向**市级平台**提交"**办结反馈**"。
    *   工单在市级平台的状态统一更新为**【待回访】**。

#### **阶段五：市级统一回访与最终关闭**

10. **独立回访**: **市级回访中心**对所有进入【待回访】状态的工单，统一进行回访。（注：即时办结的工单直接进入【已关闭】状态，跳过回访环节）

11. **最终闭环**:
    *   **市民满意**: 回访员将工单**【已关闭】**。
    *   **市民不满意**: 回访员**重启工单**，附上"**重办督办**"意见，退回至**【市级12345中心】**，启动新一轮的、更高级别的督办处理流程。

---

### **多部门协同流程详解**

#### **市级多部门协同流程**

**启动场景**: 跨区域、跨部门的重大复杂问题，需要多个市级部门和区级政府协同处理。

**示例场景**: 跨多个区域的河流污染问题，涉及环境监测、水务管理、属地处置等多个方面。

1.  **构建协同结构**:
    *   **确定主办部门**: 根据问题性质，选择最相关的市级职能部门作为主办部门，负责总协调和技术鉴定。
    *   **添加协办部门**: 将其他相关的市级部门和涉及区域的12345分中心添加为协办部门。

2.  **多方并行处理**:
    *   所有参与单位的"我的工单"中都会出现此任务，并有明确的"主办"或"协办"标识。
    *   **主办部门**负责专业调查、技术鉴定和总协调。
    *   **协办部门**各自承担相应的专业支持或现场配合工作。
    *   所有调查进展通过**工单补记**实时更新，供所有协办部门查看。

3.  **信息汇总与联合处置**:
    *   各方汇总信息，进行联合处置。
    *   协办部门完成任务后，在工单中更新自己的任务状态为"完成"。
    *   **主办部门**在确认所有问题均已解决、各方均已完成工作后，汇总所有处理情况，形成最终的办结报告，然后点击"**办结**"。

#### **区级多部门协同流程**

**启动场景**: 区级12345分中心接收工单后，判断需要"条块结合"处理。

**示例场景**: 建筑垃圾倾倒问题，需要属地管理和专业执法相结合。

1.  **构建区级协同结构**:
    *   **确定主办部门**: 将**【街道办事处】**设为主办部门，负责现场管理和总协调。
    *   **添加协办部门**: 将需要提供专业执法的区级部门（如区住建局、区生态环境局）添加为协办部门。

2.  **区级协同团队的并行处理**:
    *   **主办部门（街道办事处）**进行本级再分派，将任务下派给社区和街道城管科，同时与协办部门保持沟通。
    *   **协办部门（区级部门）**各自进行本级再分派，将任务下派给内部具体的业务科室和一线工作人员。
    *   **信息共享**: 所有单位都在同一个工单上进行补记，确保信息实时共享和透明。

#### **街/镇级多部门协同流程**

**启动场景**: 街道办事处接收工单后，判断需要内部多个单位协同作战。

**示例场景**: 流浪犬处理问题，需要城管、卫生、社区等多方协作。

1.  **构建基层协同结构**:
    *   **确定主办部门**: 街道办事处自任主办部门或指定街道综治办作为主办部门。
    *   **添加协办部门**: 将街道城管科、社区卫生服务中心、社区居委会添加为协办部门。

2.  **街道内多方并行处理**:
    *   **协办部门（社区居委会）**首先行动，进行宣传、安抚居民，收集线索。
    *   **协办部门（卫生服务中心）**制作宣传材料，准备防疫措施。
    *   **协办部门（街道城管科）**制定捕捉方案，准备专业工具。
    *   **主办部门**全程跟进各方进度，协调确定最佳执行时间。

#### **多部门协同模式下的审核流程**

多部门协同工单的审核，遵循核心原则：**协办部门对主办部门负责，主办部门对派单方负责。**

1.  **各参与单位内部审核**:
    *   每个参与单位（无论主协办）先在内部完成各自的"执行-审核"小闭环。

2.  **协办部门向主办部门提交确认**:
    *   各协办部门完成内部审核后，在主工单上执行"**提交协办意见**"或"**确认协办完成**"。

3.  **主办部门总办结**:
    *   主办部门在确认自己内部所有任务已完成，并且收到了所有协办部门的"协办完成"确认后，汇总所有信息，形成完整的最终办结报告。
    *   主办部门点击"**办结**"，工单状态变为**【待审核】**。

4.  **派单方最终审核**:
    *   最初的派单方对主办部门提交的、包含了所有协办部门工作成果的完整办结报告进行最终审核。
    *   审核通过后，工单进入**【待回访】**状态。
    *   审核退回时，整个工单退回给主办部门，要求重新协调处理。

---

### **业务流程总结**

本工单系统通过"条块结合"的分派策略和"多部门协同"的处理模式，实现了：

1.  **多层级的即时办结机会**，提高处理效率
2.  **严格的原路返回审核**，确保责任可溯
3.  **灵活的协同处理机制**，应对复杂问题
4.  **统一的回访关闭流程**，保证服务质量

整个流程既保证了工单处理的规范性和可追溯性，又具备了应对各种复杂情况的灵活性和协同能力。
