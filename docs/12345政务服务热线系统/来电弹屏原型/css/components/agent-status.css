/**
 * 坐席状态栏样式
 * 包含状态指示器、计时器、软电话控制等
 */

/* 坐席状态栏样式 */
.agent-status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    z-index: 1000;
    color: white;
    font-size: 13px;
}

.status-section {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-shrink: 0;
    min-width: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
    box-shadow: 0 0 12px rgba(40, 167, 69, 0.6);
    animation: statusPulse 2s infinite;
    flex-shrink: 0;
    display: block;
}

.status-dot.calling {
    background: #dc3545;
    box-shadow: 0 0 12px rgba(220, 53, 69, 0.6);
}

.status-dot.busy {
    background: #ffc107;
    box-shadow: 0 0 12px rgba(255, 193, 7, 0.6);
}

.status-dot.break {
    background: #6c757d;
    box-shadow: 0 0 12px rgba(108, 117, 125, 0.6);
}

.status-text {
    font-weight: 600;
    color: white;
    font-size: 14px;
}

.work-timer, .work-duration {
    display: flex;
    align-items: center;
    gap: 6px;
}

.timer-label, .duration-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

.timer-value, .duration-value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #ffd700;
    font-size: 14px;
    background: rgba(255, 215, 0, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.status-controls {
    display: flex;
    gap: 8px;
}

.btn-status {
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-status:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.btn-status.active {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* 模拟来电按钮样式 */
.btn-simulate-call {
    padding: 8px 16px;
    border: 1px solid rgba(34, 197, 94, 0.5);
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
}

.btn-simulate-call:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    border-color: rgba(34, 197, 94, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-simulate-call:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(34, 197, 94, 0.2);
}

.btn-simulate-call .icon {
    font-size: 16px;
    animation: pulse 2s infinite;
}

.btn-simulate-call .label {
    font-weight: 600;
}

/* 按钮禁用状态 */
.btn-simulate-call:disabled {
    background: rgba(107, 114, 128, 0.5);
    border-color: rgba(107, 114, 128, 0.3);
    color: rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-simulate-call:disabled .icon {
    animation: none;
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes statusPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 来电指示器样式 */
.incoming-call-indicator {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: 2px solid #ffffff;
    border-radius: 25px;
    padding: 8px 20px;
    box-shadow: 0 8px 24px rgba(40, 167, 69, 0.4);
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    animation: slideDown 0.5s ease-out;
    z-index: 998;
}

.call-pulse {
    width: 12px;
    height: 12px;
    background: #ffffff;
    border-radius: 50%;
    animation: callPulse 1.5s infinite;
}

.call-text {
    white-space: nowrap;
}

@keyframes callPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.7;
    }
}
