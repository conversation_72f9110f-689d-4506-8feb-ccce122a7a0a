/**
 * 通用按钮样式
 * 包含各种类型的按钮样式和状态
 */

/* 通用按钮样式 */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #545b62 0%, #3d4142 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #0f6674 100%);
}

.btn-link {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
    font-size: 12px;
    box-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    padding: 4px 8px;
}

.btn-link:hover {
    color: #0056b3;
    transform: none;
    box-shadow: none;
}

.btn-link::before {
    display: none;
}

/* 成功状态动画 */
.success-animation {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 紧急状态闪烁 */
.urgent-blink {
    animation: urgentBlink 1s infinite;
}

@keyframes urgentBlink {
    0%, 50% { opacity: 1; }
    25%, 75% { opacity: 0.5; }
}

/* 按钮禁用状态样式 */
.btn:disabled {
    background: #6c757d !important;
    color: rgba(255, 255, 255, 0.6) !important;
    cursor: not-allowed !important;
    opacity: 0.6;
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    pointer-events: none;
}

.btn:disabled::before {
    display: none;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 主要按钮禁用状态 */
.btn-primary:disabled {
    background: #6c757d !important;
}

/* 次要按钮禁用状态 */
.btn-secondary:disabled {
    background: #6c757d !important;
    border-color: #6c757d !important;
}

/* 波纹效果动画 */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
