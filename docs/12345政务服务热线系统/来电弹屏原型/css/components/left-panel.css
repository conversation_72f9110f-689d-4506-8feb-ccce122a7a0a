/**
 * 左栏客户信息相关样式
 * 包含客户信息卡片、地址信息、统计数据等
 */

.customer-info-card {
    margin-bottom: 24px;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.customer-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.customer-info-card h3 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
    position: relative;
    z-index: 1;
}

/* 客户信息占位符样式 */
.customer-info-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.customer-info-placeholder .placeholder-content {
    color: rgba(255, 255, 255, 0.8);
}

.customer-info-placeholder .placeholder-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.7;
}

.customer-info-placeholder .placeholder-text {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.9);
}

.customer-info-placeholder .placeholder-desc {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
}

/* 地址信息占位符样式 */
.address-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    text-align: center;
    margin: 16px 0;
}

.address-placeholder .placeholder-content {
    color: #6c757d;
}

.address-placeholder .placeholder-icon {
    font-size: 36px;
    margin-bottom: 12px;
    opacity: 0.6;
}

.address-placeholder .placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 6px;
    color: #495057;
}

.address-placeholder .placeholder-desc {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

/* 客户信息内容区域 - 左右布局 */
.customer-info-content {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: flex-start;
    position: relative;
    z-index: 1;
    min-height: 120px;
}

/* 左侧：市民基本信息区域 */
.customer-basic-section {
    flex: 1;
    min-width: 0;
    max-width: 50%;
}

.customer-basic {
    width: 100%;
}

/* 右侧：统计信息区域 */
.statistics-section {
    flex: 1;
    min-width: 0;
    max-width: 50%;
}

.statistics-summary {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 6px 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: fit-content;
}

.statistics-summary h4 {
    margin-bottom: 6px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    border-bottom: none;
    padding-bottom: 0;
    text-align: center;
    font-weight: 400;
    letter-spacing: 0;
}

.stats-grid {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.statistics-summary .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    padding: 4px 0;
    background: none;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.statistics-summary .stat-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.statistics-summary .stat-item .label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 11px;
    flex: 1;
    font-weight: 400;
    white-space: nowrap;
}

.statistics-summary .stat-item .value {
    color: white;
    font-weight: 500;
    font-size: 12px;
    text-align: right;
    white-space: nowrap;
}

.statistics-summary .stat-item .value.urgent {
    color: #ffeb3b;
    font-weight: 600;
}

.statistics-summary .stat-item .value.rating {
    color: #ffd700;
}



.customer-name {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    letter-spacing: 0.5px;
    text-align: left;
    line-height: 1.2;
}

.phone-number {
    font-size: 16px;
    font-weight: 600;
    color: #ffd700;
    margin-bottom: 6px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    background: rgba(255, 215, 0, 0.15);
    padding: 4px 8px;
    border-radius: 6px;
    display: block;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.3);
    backdrop-filter: blur(5px);
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

.location {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.location::before {
    content: '📍';
    font-size: 14px;
}

.tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    min-height: 40px;
}

.tag {
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    min-width: auto;
    text-align: center;
}

.tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.tag.vip {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    animation: glow 2s ease-in-out infinite alternate;
    position: relative;
    overflow: hidden;
}

.tag.vip::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shine 3s infinite;
}

.tag.love {
    background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);
}

.tag.frequent {
    background: linear-gradient(135deg, #54a0ff 0%, #2e86de 100%);
}

@keyframes glow {
    from { box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4); }
    to { box-shadow: 0 4px 16px rgba(255, 107, 107, 0.8); }
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.address-info, .statistics {
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 4px 16px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.address-info:hover, .statistics:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.address-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #007bff 0%, #0056b3 100%);
    border-radius: 0 2px 2px 0;
}

.address-info h4, .statistics h4 {
    margin-bottom: 10px;
    color: #495057;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: #007bff;
    font-weight: 600;
}

.address-info h4::before {
    content: '📍';
    font-size: 16px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.address-item {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 10px;
    border: 1px solid rgba(0, 123, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.05);
    transition: all 0.3s ease;
}

.address-item:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.15);
    border-color: rgba(0, 123, 255, 0.3);
}

.address-item label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 6px;
    font-size: 13px;
}

.address-item label::before {
    content: '🏠';
    font-size: 12px;
}

.address-link {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.1) 100%);
    cursor: pointer;
}

.address-link::before {
    content: '📍';
    font-size: 12px;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.address-link:hover {
    color: #0056b3;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.2) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.address-link:hover::before {
    transform: scale(1.2);
    opacity: 1;
}

.history-addresses {
    margin-top: 16px;
}

.history-addresses label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    font-size: 13px;
}

.history-addresses label::before {
    content: '📋';
    font-size: 12px;
}

.history-addresses ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.history-addresses li {
    margin: 0;
    padding: 10px 12px;
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-radius: 8px;
    border-left: 3px solid #ff9800;
    transition: all 0.3s ease;
    position: relative;
}

.history-addresses li:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 12px rgba(255, 152, 0, 0.2);
    background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
}

.history-addresses li::before {
    content: '🕒';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    background: #ff9800;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
}

.history-addresses .address-link {
    background: rgba(255, 255, 255, 0.7);
    color: #e65100;
    font-size: 13px;
    padding: 2px 6px;
    margin: 0;
}

.history-addresses .address-link:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #bf360c;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
}

.stat-item .label {
    color: #6c757d;
}

.stat-item .value.urgent {
    color: #dc3545;
    font-weight: bold;
}

.stat-item .value.rating {
    color: #ffc107;
}

/* 转人工信息卡片样式 */
.transfer-info {
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: 0 4px 16px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.transfer-info:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.transfer-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #28a745 0%, #1e7e34 100%);
    border-radius: 0 2px 2px 0;
}

.transfer-info h4 {
    margin-bottom: 16px;
    color: #28a745;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 转人工占位符样式 */
.transfer-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80px;
    text-align: center;
}

.transfer-placeholder .placeholder-content {
    color: #6c757d;
}

.transfer-placeholder .placeholder-icon {
    font-size: 24px;
    margin-bottom: 8px;
    opacity: 0.7;
}

.transfer-placeholder .placeholder-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #495057;
}

.transfer-placeholder .placeholder-desc {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

/* IVR路径样式 */
.ivr-path {
    margin-bottom: 16px;
}

.ivr-path label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.path-chain {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
}

.path-step {
    padding: 4px 8px;
    background: #e9ecef;
    border-radius: 6px;
    font-size: 11px;
    color: #495057;
    white-space: nowrap;
}

.path-step.current {
    background: #28a745;
    color: white;
    font-weight: 600;
}

.path-arrow {
    color: #6c757d;
    font-size: 12px;
    margin: 0 2px;
}

/* 排队信息样式 */
.queue-info {
    margin-bottom: 16px;
}

.queue-info label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.queue-stats {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    border: 1px solid rgba(0,0,0,0.05);
}

.queue-label {
    font-size: 11px;
    color: #6c757d;
}

.queue-value {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
}

/* 智能对话摘要样式 */
.conversation-summary {
    margin-bottom: 16px;
}

.conversation-summary label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    border: 1px solid rgba(0,0,0,0.05);
}

.summary-label {
    font-size: 11px;
    color: #6c757d;
    flex-shrink: 0;
}

.summary-value {
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    text-align: right;
}

.summary-value.emotion-calm {
    color: #28a745;
}

.summary-value.emotion-angry {
    color: #dc3545;
}

.summary-value.emotion-worried {
    color: #ffc107;
}

.summary-value.priority-high {
    color: #dc3545;
}

.summary-value.priority-medium {
    color: #ffc107;
}

.summary-value.priority-low {
    color: #28a745;
}

/* AI建议样式 */
.ai-recommendation label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.recommendation-content {
    padding: 12px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
    border: 1px solid rgba(63, 81, 181, 0.1);
}

.recommendation-content p {
    margin: 0;
    font-size: 12px;
    color: #495057;
    line-height: 1.4;
}