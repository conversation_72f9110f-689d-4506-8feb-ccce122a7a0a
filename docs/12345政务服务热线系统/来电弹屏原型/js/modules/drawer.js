/**
 * 抽屉组件管理模块
 * 负责管理新建工单抽屉的显示、隐藏和交互
 */

class DrawerManager {
    constructor() {
        this.isOpen = false;
        this.overlay = null;
        this.drawer = null;
        this.voiceTranscriptContent = null;
        this.summaryContent = null;
        this.transcriptTimer = null;
        this.summaryTimer = null;
        this.init();
    }

    /**
     * 初始化抽屉组件
     */
    init() {
        this.createDrawerHTML();
        this.bindEvents();
    }

    /**
     * 创建抽屉的HTML结构
     */
    createDrawerHTML() {
        const drawerHTML = `
            <!-- 抽屉遮罩层 -->
            <div class="drawer-overlay" id="drawerOverlay">
                <!-- 抽屉容器 -->
                <div class="drawer" id="ticketDrawer">
                    <!-- 抽屉标题栏 -->
                    <div class="drawer-header">
                        <h2 class="drawer-title">新建工单</h2>
                        <button class="drawer-close" id="drawerCloseBtn">×</button>
                    </div>

                    <!-- 抽屉主体内容 -->
                    <div class="drawer-body">
                        <!-- 左侧：实时信息区域 -->
                        <div class="drawer-realtime-section">
                            <!-- 实时语音转写 -->
                            <div class="drawer-voice-transcript">
                                <div class="transcript-header">
                                    <h4>实时语音转写</h4>
                                    <div class="status-indicator" id="drawerTranscriptStatus"></div>
                                </div>
                                <div class="transcript-content" id="drawerTranscriptContent">
                                    <div style="color: #6c757d; text-align: center; padding: 40px 20px;">
                                        <div style="font-size: 48px; margin-bottom: 16px;">🎤</div>
                                        <div>等待语音输入...</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 实时对话摘要 -->
                            <div class="drawer-conversation-summary">
                                <div class="summary-header">
                                    <h4>实时对话摘要</h4>
                                    <div class="status-indicator" id="drawerSummaryStatus"></div>
                                </div>
                                <div class="summary-content" id="drawerSummaryContent">
                                    <div style="color: #6c757d; text-align: center; padding: 40px 20px;">
                                        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
                                        <div>等待对话分析...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：工单表单区域 -->
                        <div class="drawer-form-section">
                            <h3>工单信息</h3>
                            <div class="drawer-ticket-form">
                                <!-- 基础信息区 -->
                                <div class="form-section">
                                    <h4 class="section-title">基础信息</h4>

                                    <div class="drawer-form-group">
                                        <label for="drawerTicketTitle">工单标题：</label>
                                        <input type="text" id="drawerTicketTitle" placeholder="系统将根据诉求自动生成标题">
                                    </div>

                                    <div class="form-row">
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerCitizenName">市民姓名：</label>
                                            <input type="text" id="drawerCitizenName" placeholder="张女士" readonly>
                                        </div>
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerCitizenPhone">联系电话：</label>
                                            <input type="text" id="drawerCitizenPhone" placeholder="138****1234" readonly>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerReceptionChannel">受理渠道：</label>
                                            <select id="drawerReceptionChannel">
                                                <option value="电话" selected>电话</option>
                                                <option value="网站">网站</option>
                                                <option value="APP">APP</option>
                                                <option value="微信">微信</option>
                                                <option value="领导转办">领导转办</option>
                                            </select>
                                        </div>
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerReceptionAgent">受理客服：</label>
                                            <input type="text" id="drawerReceptionAgent" placeholder="当前登录客服" readonly>
                                        </div>
                                    </div>
                                </div>

                                <!-- 事发位置区 -->
                                <div class="form-section">
                                    <h4 class="section-title">事发位置</h4>

                                    <div class="form-row">
                                        <div class="drawer-form-group third-width">
                                            <label for="drawerProvince">省份：</label>
                                            <select id="drawerProvince">
                                                <option value="广东省" selected>广东省</option>
                                            </select>
                                        </div>
                                        <div class="drawer-form-group third-width">
                                            <label for="drawerCity">城市：</label>
                                            <select id="drawerCity">
                                                <option value="广州市" selected>广州市</option>
                                            </select>
                                        </div>
                                        <div class="drawer-form-group third-width">
                                            <label for="drawerDistrict">区县：</label>
                                            <select id="drawerDistrict">
                                                <option value="">请选择区县</option>
                                                <option value="天河区">天河区</option>
                                                <option value="越秀区">越秀区</option>
                                                <option value="海珠区">海珠区</option>
                                                <option value="荔湾区">荔湾区</option>
                                                <option value="白云区">白云区</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="drawer-form-group">
                                        <label for="drawerDetailAddress">详细地址：</label>
                                        <input type="text" id="drawerDetailAddress" placeholder="请输入具体地址">
                                    </div>
                                </div>

                                <!-- 核心诉求区 -->
                                <div class="form-section">
                                    <h4 class="section-title">核心诉求</h4>

                                    <div class="drawer-form-group">
                                        <label for="drawerOriginalDescription">原始诉求描述：</label>
                                        <textarea id="drawerOriginalDescription" placeholder="详细记录市民的原始诉求" rows="3"></textarea>
                                    </div>

                                    <div class="drawer-form-group">
                                        <label for="drawerTicketSummary">工单摘要：</label>
                                        <textarea id="drawerTicketSummary" placeholder="系统将自动生成摘要" rows="2"></textarea>
                                    </div>

                                    <div class="form-row">
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerPrimaryCategory">一级分类：</label>
                                            <select id="drawerPrimaryCategory">
                                                <option value="">请选择一级分类</option>
                                                <option value="城市管理">城市管理</option>
                                                <option value="公共服务">公共服务</option>
                                                <option value="环境保护">环境保护</option>
                                                <option value="政策咨询">政策咨询</option>
                                                <option value="其他">其他</option>
                                            </select>
                                        </div>
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerSecondaryCategory">二级分类：</label>
                                            <select id="drawerSecondaryCategory">
                                                <option value="">请选择二级分类</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerPriority">紧急程度：</label>
                                            <select id="drawerPriority">
                                                <option value="P4-低">P4-低</option>
                                                <option value="P3-普通" selected>P3-普通</option>
                                                <option value="P2-高">P2-高</option>
                                                <option value="P1-紧急">P1-紧急</option>
                                            </select>
                                        </div>
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerExpectedTime">期望解决时间：</label>
                                            <select id="drawerExpectedTime">
                                                <option value="立即">立即</option>
                                                <option value="1天内">1天内</option>
                                                <option value="3天内" selected>3天内</option>
                                                <option value="1周内">1周内</option>
                                                <option value="1个月内">1个月内</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="drawer-form-group">
                                        <label for="drawerTags">自定义标签：</label>
                                        <div class="tag-input-container">
                                            <input type="text" id="drawerTags" placeholder="输入标签，用逗号分隔">
                                            <div class="suggested-tags">
                                                <span class="tag-suggestion" data-tag="紧急">紧急</span>
                                                <span class="tag-suggestion" data-tag="VIP">VIP</span>
                                                <span class="tag-suggestion" data-tag="投诉">投诉</span>
                                                <span class="tag-suggestion" data-tag="媒体关注">媒体关注</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 流转处理区 -->
                                <div class="form-section">
                                    <h4 class="section-title">流转处理</h4>

                                    <!-- 多部门协同信息 -->
                                    <div class="collaboration-section">
                                        <h5 class="subsection-title">多部门协同信息</h5>

                                        <div class="drawer-form-group">
                                            <label for="drawerPrimaryDepartment">主办部门：</label>
                                            <div class="department-input-container">
                                                <input type="text" id="drawerPrimaryDepartment" placeholder="系统将自动推荐主办单位" readonly>
                                                <button type="button" class="btn-select-department" id="btnSelectPrimary">选择</button>
                                            </div>
                                        </div>

                                        <div class="drawer-form-group">
                                            <label for="drawerCollaborativeDepartments">协办部门：</label>
                                            <div class="collaborative-departments-container">
                                                <div class="selected-departments" id="selectedDepartments">
                                                    <div class="no-departments">暂无协办部门</div>
                                                </div>
                                                <button type="button" class="btn-add-department" id="btnAddCollaborative">+ 添加协办部门</button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 处理策略 -->
                                    <div class="form-row">
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerSlaPolicy">SLA策略：</label>
                                            <select id="drawerSlaPolicy">
                                                <option value="标准处理">标准处理</option>
                                                <option value="加急处理">加急处理</option>
                                                <option value="特殊处理">特殊处理</option>
                                            </select>
                                        </div>
                                        <div class="drawer-form-group half-width">
                                            <label for="drawerProcessingMode">处理模式：</label>
                                            <select id="drawerProcessingMode">
                                                <option value="单独处理">单独处理</option>
                                                <option value="协同处理">协同处理</option>
                                                <option value="分工处理">分工处理</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="drawer-form-group">
                                        <label for="drawerProcessingNotes">处理说明：</label>
                                        <textarea id="drawerProcessingNotes" placeholder="填写指派说明、协办要求或特殊处理要求" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 抽屉底部操作按钮 -->
                    <div class="drawer-actions">
                        <button class="btn btn-secondary" id="drawerSaveDraftBtn">暂存草稿</button>
                        <button class="btn btn-warning" id="drawerResetBtn">重新填写</button>
                        <button class="btn btn-success" id="drawerInstantCloseBtn">即时办结</button>
                        <button class="btn btn-primary" id="drawerSubmitBtn">确认派发</button>
                    </div>
                </div>
            </div>
        `;

        // 将抽屉HTML添加到body末尾
        document.body.insertAdjacentHTML('beforeend', drawerHTML);

        // 获取DOM元素引用
        this.overlay = document.getElementById('drawerOverlay');
        this.drawer = document.getElementById('ticketDrawer');
        this.voiceTranscriptContent = document.getElementById('drawerTranscriptContent');
        this.summaryContent = document.getElementById('drawerSummaryContent');

        // 初始化多部门协同相关数据
        this.selectedDepartments = [];
        this.departmentModal = null;

        // 创建部门选择弹窗
        this.createDepartmentModal();
    }

    /**
     * 创建部门选择弹窗
     */
    createDepartmentModal() {
        const modalHTML = `
            <div class="department-modal" id="departmentModal">
                <div class="department-modal-content">
                    <div class="department-modal-header">
                        <h3 class="department-modal-title">选择部门</h3>
                        <button class="department-modal-close" id="departmentModalClose">×</button>
                    </div>
                    <div class="department-search">
                        <input type="text" id="departmentSearch" placeholder="搜索部门名称...">
                    </div>
                    <div class="department-list" id="departmentList">
                        <!-- 部门列表将动态生成 -->
                    </div>
                    <div style="margin-top: 16px; text-align: right;">
                        <button class="btn btn-secondary" id="departmentModalCancel">取消</button>
                        <button class="btn btn-primary" id="departmentModalConfirm" style="margin-left: 8px;">确认</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.departmentModal = document.getElementById('departmentModal');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 关闭按钮事件
        const closeBtn = document.getElementById('drawerCloseBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }

        // 遮罩层点击关闭
        if (this.overlay) {
            this.overlay.addEventListener('click', (e) => {
                if (e.target === this.overlay) {
                    this.close();
                }
            });
        }

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });

        // 表单操作按钮事件
        this.bindFormActions();

        // 分类联动事件
        this.bindCategoryChange();

        // 多部门协同相关事件
        this.bindCollaborationEvents();
    }

    /**
     * 绑定表单操作按钮事件
     */
    bindFormActions() {
        const saveDraftBtn = document.getElementById('drawerSaveDraftBtn');
        const resetBtn = document.getElementById('drawerResetBtn');
        const instantCloseBtn = document.getElementById('drawerInstantCloseBtn');
        const submitBtn = document.getElementById('drawerSubmitBtn');

        if (saveDraftBtn) {
            saveDraftBtn.addEventListener('click', () => this.saveDraft());
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetForm());
        }

        if (instantCloseBtn) {
            instantCloseBtn.addEventListener('click', () => this.instantClose());
        }

        if (submitBtn) {
            submitBtn.addEventListener('click', () => this.submitTicket());
        }
    }

    /**
     * 绑定分类联动事件
     */
    bindCategoryChange() {
        // 一级分类联动
        const primaryCategorySelect = document.getElementById('drawerPrimaryCategory');
        const secondaryCategorySelect = document.getElementById('drawerSecondaryCategory');

        if (primaryCategorySelect && secondaryCategorySelect) {
            primaryCategorySelect.addEventListener('change', (e) => {
                const category = e.target.value;
                this.updateSecondaryCategories(category, secondaryCategorySelect);
            });
        }

        // 区县联动
        const districtSelect = document.getElementById('drawerDistrict');
        if (districtSelect) {
            districtSelect.addEventListener('change', (e) => {
                // 可以在这里添加街道/镇的联动逻辑
            });
        }

        // 标签建议点击事件
        const tagSuggestions = document.querySelectorAll('.tag-suggestion');
        tagSuggestions.forEach(tag => {
            tag.addEventListener('click', (e) => {
                this.addTag(e.target.dataset.tag);
                e.target.classList.toggle('selected');
            });
        });
    }

    /**
     * 更新二级分类选项
     */
    updateSecondaryCategories(category, secondaryCategorySelect) {
        const secondaryCategories = {
            '城市管理': ['占道经营', '违章搭建', '市容环境', '公共设施', '其他'],
            '公共服务': ['供水供电', '燃气服务', '通信服务', '公共交通', '其他'],
            '环境保护': ['噪音污染', '空气污染', '水污染', '垃圾处理', '其他'],
            '政策咨询': ['社保政策', '教育政策', '住房政策', '就业政策', '其他'],
            '其他': ['投诉建议', '咨询求助', '其他']
        };

        // 清空现有选项
        secondaryCategorySelect.innerHTML = '<option value="">请选择二级分类</option>';

        // 添加新选项
        if (secondaryCategories[category]) {
            secondaryCategories[category].forEach(subCat => {
                const option = document.createElement('option');
                option.value = subCat;
                option.textContent = subCat;
                secondaryCategorySelect.appendChild(option);
            });
        }
    }

    /**
     * 添加标签
     */
    addTag(tagText) {
        const tagsInput = document.getElementById('drawerTags');
        if (tagsInput) {
            const currentTags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);
            if (!currentTags.includes(tagText)) {
                currentTags.push(tagText);
                tagsInput.value = currentTags.join(', ');
            }
        }
    }

    /**
     * 绑定多部门协同相关事件
     */
    bindCollaborationEvents() {
        // 选择主办部门按钮
        const btnSelectPrimary = document.getElementById('btnSelectPrimary');
        if (btnSelectPrimary) {
            btnSelectPrimary.addEventListener('click', () => {
                this.openDepartmentModal('primary');
            });
        }

        // 添加协办部门按钮
        const btnAddCollaborative = document.getElementById('btnAddCollaborative');
        if (btnAddCollaborative) {
            btnAddCollaborative.addEventListener('click', () => {
                this.openDepartmentModal('collaborative');
            });
        }

        // 部门弹窗相关事件
        const departmentModalClose = document.getElementById('departmentModalClose');
        const departmentModalCancel = document.getElementById('departmentModalCancel');
        const departmentModalConfirm = document.getElementById('departmentModalConfirm');

        if (departmentModalClose) {
            departmentModalClose.addEventListener('click', () => this.closeDepartmentModal());
        }

        if (departmentModalCancel) {
            departmentModalCancel.addEventListener('click', () => this.closeDepartmentModal());
        }

        if (departmentModalConfirm) {
            departmentModalConfirm.addEventListener('click', () => this.confirmDepartmentSelection());
        }

        // 部门搜索
        const departmentSearch = document.getElementById('departmentSearch');
        if (departmentSearch) {
            departmentSearch.addEventListener('input', (e) => {
                this.filterDepartments(e.target.value);
            });
        }
    }

    /**
     * 打开抽屉
     */
    open() {
        if (this.isOpen) return;

        this.isOpen = true;
        
        // 显示遮罩层和抽屉
        if (this.overlay) {
            this.overlay.classList.add('active');
        }
        
        if (this.drawer) {
            this.drawer.classList.add('active');
        }

        // 开始实时功能
        this.startRealTimeFeatures();

        // 通知
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('新建工单抽屉已打开', 'info');
        }
    }

    /**
     * 关闭抽屉
     */
    close() {
        if (!this.isOpen) return;

        this.isOpen = false;

        // 隐藏抽屉和遮罩层
        if (this.drawer) {
            this.drawer.classList.remove('active');
        }
        
        if (this.overlay) {
            this.overlay.classList.remove('active');
        }

        // 停止实时功能
        this.stopRealTimeFeatures();

        // 通知
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('新建工单抽屉已关闭', 'info');
        }
    }

    /**
     * 开始实时功能（语音转写和对话摘要）
     */
    startRealTimeFeatures() {
        this.displayCompleteTranscript();
        this.displayCompleteSummary();
    }

    /**
     * 停止实时功能
     */
    stopRealTimeFeatures() {
        if (this.transcriptTimer) {
            clearTimeout(this.transcriptTimer);
            this.transcriptTimer = null;
        }
        
        if (this.summaryTimer) {
            clearTimeout(this.summaryTimer);
            this.summaryTimer = null;
        }
    }

    /**
     * 显示完整的语音转写内容
     */
    displayCompleteTranscript() {
        const statusIndicator = document.getElementById('drawerTranscriptStatus');
        if (statusIndicator) {
            statusIndicator.classList.add('active');
        }

        // 完整的语音转写内容
        const messages = [
            { speaker: 'agent', text: '您好，这里是12345政务服务热线，请问有什么可以帮助您的？' },
            { speaker: 'citizen', text: '你好，我想反映一个问题，就是我们小区门口的积水问题。' },
            { speaker: 'agent', text: '好的，请您详细说明一下具体情况。' },
            { speaker: 'citizen', text: '就是阳光小区南门那里，每次下雨就积水很严重，都没法走路了。' },
            { speaker: 'agent', text: '我了解了，请问积水大概有多深？是否影响到了居民楼？' },
            { speaker: 'citizen', text: '有二三十厘米深吧，主要是影响出行，希望能尽快处理。' },
            { speaker: 'agent', text: '好的，我已经记录下您的情况，我们会立即为您处理这个问题。' }
        ];

        // 清空占位符内容
        if (this.voiceTranscriptContent) {
            this.voiceTranscriptContent.innerHTML = '';

            // 添加所有消息
            messages.forEach(message => {
                this.addTranscriptMessage(message.speaker, message.text);
            });
        }
    }



    /**
     * 添加单条转写消息
     */
    addTranscriptMessage(speaker, text) {
        if (!this.voiceTranscriptContent) return;

        // 如果是第一条消息，清空占位符
        if (this.voiceTranscriptContent.children.length === 1 &&
            this.voiceTranscriptContent.children[0].style.color === 'rgb(108, 117, 125)') {
            this.voiceTranscriptContent.innerHTML = '';
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `transcript-message ${speaker}`;
        messageDiv.style.cssText = `
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            ${speaker === 'agent' ?
                'background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); margin-left: 0; margin-right: 20px;' :
                'background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%); margin-left: 20px; margin-right: 0;'
            }
        `;

        const speakerLabel = document.createElement('div');
        speakerLabel.style.cssText = 'font-weight: 600; font-size: 12px; margin-bottom: 4px; color: #495057;';
        speakerLabel.textContent = speaker === 'agent' ? '坐席' : '市民';

        const messageText = document.createElement('div');
        messageText.textContent = text;

        messageDiv.appendChild(speakerLabel);
        messageDiv.appendChild(messageText);
        this.voiceTranscriptContent.appendChild(messageDiv);

        // 滚动到顶部
        this.voiceTranscriptContent.scrollTop = 0;
    }

    /**
     * 显示完整的对话摘要内容
     */
    displayCompleteSummary() {
        const statusIndicator = document.getElementById('drawerSummaryStatus');
        if (statusIndicator) {
            statusIndicator.classList.add('active');
        }

        // 完整的对话摘要内容
        const summaryContent = {
            '核心诉求': '反映小区门口积水问题',
            '情绪状态': '理性沟通，配合度高',
            '问题详情': '阳光小区南门积水严重，深度约20-30厘米',
            '紧急程度': '中等 - 影响出行但无安全隐患',
            '建议分类': '市政设施 -> 排水设施',
            '推荐承办': '市政排水管理处',
            '关键信息': '雨后积水、持续性问题、需现场勘查',
            '决策建议': '建议立即派单，安排现场勘查，预计处理时间3-5个工作日'
        };

        // 直接显示完整摘要
        this.renderSummaryContent(summaryContent);

        // 延迟1秒后自动填充表单
        setTimeout(() => {
            this.autoFillForm(summaryContent);
        }, 1000);
    }



    /**
     * 渲染摘要内容
     */
    renderSummaryContent(content) {
        if (!this.summaryContent) return;

        // 如果是第一次更新，清空占位符
        if (this.summaryContent.children.length === 1 && 
            this.summaryContent.children[0].style.color === 'rgb(108, 117, 125)') {
            this.summaryContent.innerHTML = '';
        }

        this.summaryContent.innerHTML = '';

        Object.entries(content).forEach(([key, value]) => {
            const itemDiv = document.createElement('div');
            itemDiv.style.cssText = 'margin-bottom: 12px; padding: 8px 12px; background: rgba(255,255,255,0.7); border-radius: 6px; border-left: 3px solid #17a2b8;';
            
            const keySpan = document.createElement('span');
            keySpan.style.cssText = 'font-weight: 600; color: #17a2b8; display: block; margin-bottom: 4px;';
            keySpan.textContent = key + '：';
            
            const valueSpan = document.createElement('span');
            valueSpan.textContent = value;
            
            itemDiv.appendChild(keySpan);
            itemDiv.appendChild(valueSpan);
            this.summaryContent.appendChild(itemDiv);
        });
    }

    /**
     * 自动填充表单
     */
    autoFillForm(summaryData) {
        // 填充工单标题
        const titleInput = document.getElementById('drawerTicketTitle');
        if (titleInput && summaryData['核心诉求']) {
            titleInput.value = summaryData['核心诉求'];
            titleInput.classList.add('auto-filled');
        }

        // 填充市民信息（从系统获取）
        const citizenNameInput = document.getElementById('drawerCitizenName');
        const citizenPhoneInput = document.getElementById('drawerCitizenPhone');
        if (citizenNameInput) {
            citizenNameInput.value = '张女士';
        }
        if (citizenPhoneInput) {
            citizenPhoneInput.value = '138****1234';
        }

        // 填充受理客服
        const receptionAgentInput = document.getElementById('drawerReceptionAgent');
        if (receptionAgentInput) {
            receptionAgentInput.value = '客服001';
        }

        // 填充地址信息
        if (summaryData['问题详情'] && summaryData['问题详情'].includes('阳光小区南门')) {
            const districtSelect = document.getElementById('drawerDistrict');
            const detailAddressInput = document.getElementById('drawerDetailAddress');

            if (districtSelect) {
                districtSelect.value = '天河区';
                districtSelect.classList.add('auto-filled');
            }

            if (detailAddressInput) {
                detailAddressInput.value = '阳光小区南门';
                detailAddressInput.classList.add('auto-filled');
            }
        }

        // 填充原始诉求描述
        const originalDescInput = document.getElementById('drawerOriginalDescription');
        if (originalDescInput && summaryData['问题详情']) {
            originalDescInput.value = summaryData['问题详情'] + '，影响居民出行，希望能尽快处理';
            originalDescInput.classList.add('auto-filled');
        }

        // 填充工单摘要
        const summaryInput = document.getElementById('drawerTicketSummary');
        if (summaryInput && summaryData['核心诉求']) {
            summaryInput.value = `市民反映${summaryData['核心诉求']}，${summaryData['紧急程度'] || '需要及时处理'}`;
            summaryInput.classList.add('auto-filled');
        }

        // 填充分类信息
        if (summaryData['建议分类']) {
            const [primaryCategory, secondaryCategory] = summaryData['建议分类'].split(' -> ');

            const primaryCategorySelect = document.getElementById('drawerPrimaryCategory');
            const secondaryCategorySelect = document.getElementById('drawerSecondaryCategory');

            if (primaryCategorySelect) {
                // 映射到新的分类体系
                const categoryMapping = {
                    '市政设施': '城市管理'
                };
                const mappedCategory = categoryMapping[primaryCategory] || primaryCategory;

                primaryCategorySelect.value = mappedCategory;
                primaryCategorySelect.classList.add('auto-filled');

                // 触发change事件更新二级分类
                primaryCategorySelect.dispatchEvent(new Event('change'));

                setTimeout(() => {
                    if (secondaryCategorySelect && secondaryCategory) {
                        // 映射二级分类
                        const subCategoryMapping = {
                            '排水设施': '公共设施'
                        };
                        const mappedSubCategory = subCategoryMapping[secondaryCategory] || secondaryCategory;

                        secondaryCategorySelect.value = mappedSubCategory;
                        secondaryCategorySelect.classList.add('auto-filled');
                    }
                }, 100);
            }
        }

        // 设置紧急程度
        const prioritySelect = document.getElementById('drawerPriority');
        if (prioritySelect && summaryData['紧急程度']) {
            if (summaryData['紧急程度'].includes('中等')) {
                prioritySelect.value = 'P3-普通';
            } else if (summaryData['紧急程度'].includes('高')) {
                prioritySelect.value = 'P2-高';
            } else if (summaryData['紧急程度'].includes('紧急')) {
                prioritySelect.value = 'P1-紧急';
            }
            prioritySelect.classList.add('auto-filled');
        }

        // 设置期望解决时间
        const expectedTimeSelect = document.getElementById('drawerExpectedTime');
        if (expectedTimeSelect) {
            expectedTimeSelect.value = '3天内';
            expectedTimeSelect.classList.add('auto-filled');
        }

        // 添加标签
        if (summaryData['情绪状态'] && summaryData['情绪状态'].includes('理性')) {
            this.addTag('配合度高');
        }

        // 填充主办部门信息
        const primaryDepartmentInput = document.getElementById('drawerPrimaryDepartment');
        if (primaryDepartmentInput && summaryData['推荐承办']) {
            primaryDepartmentInput.value = summaryData['推荐承办'];
            primaryDepartmentInput.classList.add('auto-filled');
        }

        // 根据问题类型自动添加协办部门
        if (summaryData['建议分类'] && summaryData['建议分类'].includes('排水设施')) {
            // 排水问题可能需要多部门协作
            const collaborativeDepts = [
                { id: 'dept_003', name: '环境保护局' },
                { id: 'dept_002', name: '城市管理执法局' }
            ];

            collaborativeDepts.forEach(dept => {
                this.selectedDepartments.push(dept);
            });
            this.updateCollaborativeDepartmentsDisplay();
        }

        // 填充处理说明
        const processingNotesInput = document.getElementById('drawerProcessingNotes');
        if (processingNotesInput && summaryData['决策建议']) {
            processingNotesInput.value = summaryData['决策建议'];
            processingNotesInput.classList.add('auto-filled');
        }

        // 显示自动填充完成通知
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('工单已智能填充完毕，请审核后确认派发', 'success');
        }
    }

    /**
     * 保存草稿
     */
    saveDraft() {
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('工单草稿已保存', 'success');
        }
    }

    /**
     * 重置表单
     */
    resetForm() {
        const form = document.querySelector('.drawer-ticket-form');
        if (form) {
            const inputs = form.querySelectorAll('input:not([readonly]), textarea, select');
            inputs.forEach(input => {
                if (input.type === 'select-one') {
                    input.selectedIndex = 0;
                } else if (!input.hasAttribute('readonly')) {
                    input.value = '';
                }
                input.classList.remove('auto-filled');
            });

            // 清空标签建议的选中状态
            const tagSuggestions = document.querySelectorAll('.tag-suggestion');
            tagSuggestions.forEach(tag => {
                tag.classList.remove('selected');
            });

            // 重置二级分类
            const secondaryCategorySelect = document.getElementById('drawerSecondaryCategory');
            if (secondaryCategorySelect) {
                secondaryCategorySelect.innerHTML = '<option value="">请选择二级分类</option>';
            }

            // 重置多部门协同信息
            const primaryDepartmentInput = document.getElementById('drawerPrimaryDepartment');
            if (primaryDepartmentInput) {
                primaryDepartmentInput.value = '';
                primaryDepartmentInput.classList.remove('auto-filled');
            }

            // 清空协办部门
            this.selectedDepartments = [];
            this.updateCollaborativeDepartmentsDisplay();
        }

        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('表单已重置', 'info');
        }
    }

    /**
     * 即时办结
     */
    instantClose() {
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('工单已即时办结', 'success');
        }
        this.close();
    }

    /**
     * 提交工单
     */
    submitTicket() {
        // 验证必填字段
        const requiredFields = [
            { id: 'drawerTicketTitle', name: '工单标题' },
            { id: 'drawerDetailAddress', name: '详细地址' },
            { id: 'drawerOriginalDescription', name: '原始诉求描述' },
            { id: 'drawerPrimaryCategory', name: '一级分类' },
            { id: 'drawerPrimaryDepartment', name: '主办部门' }
        ];

        const missingFields = [];
        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (!element || !element.value.trim()) {
                missingFields.push(field.name);
            }
        });

        if (missingFields.length > 0) {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show(
                    `请填写必要信息：${missingFields.join('、')}`,
                    'warning'
                );
            }
            return;
        }

        // 生成工单编号
        const ticketId = this.generateTicketId();

        // 提交工单
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show(
                `工单 ${ticketId} 已成功派发`,
                'success'
            );
        }

        // 关闭抽屉
        setTimeout(() => {
            this.close();
        }, 1500);
    }

    /**
     * 生成工单编号
     */
    generateTicketId() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const random = String(Math.floor(Math.random() * 10000)).padStart(4, '0');
        return `${year}${month}${day}${random}`;
    }

    /**
     * 打开部门选择弹窗
     */
    openDepartmentModal(type) {
        this.currentSelectionType = type;
        this.selectedDepartmentInModal = null;

        // 生成部门列表
        this.generateDepartmentList();

        // 显示弹窗
        if (this.departmentModal) {
            this.departmentModal.classList.add('active');
        }

        // 更新弹窗标题
        const title = document.querySelector('.department-modal-title');
        if (title) {
            title.textContent = type === 'primary' ? '选择主办部门' : '添加协办部门';
        }
    }

    /**
     * 关闭部门选择弹窗
     */
    closeDepartmentModal() {
        if (this.departmentModal) {
            this.departmentModal.classList.remove('active');
        }

        // 清空搜索
        const searchInput = document.getElementById('departmentSearch');
        if (searchInput) {
            searchInput.value = '';
        }

        this.currentSelectionType = null;
        this.selectedDepartmentInModal = null;
    }

    /**
     * 生成部门列表
     */
    generateDepartmentList() {
        const departments = [
            { id: 'dept_001', name: '市政排水管理处', desc: '负责城市排水设施的维护和管理' },
            { id: 'dept_002', name: '城市管理执法局', desc: '负责城市管理和执法工作' },
            { id: 'dept_003', name: '环境保护局', desc: '负责环境保护和污染治理' },
            { id: 'dept_004', name: '交通运输局', desc: '负责交通运输管理和道路维护' },
            { id: 'dept_005', name: '住房和城乡建设局', desc: '负责住房和城乡建设管理' },
            { id: 'dept_006', name: '水务局', desc: '负责水资源管理和供水服务' },
            { id: 'dept_007', name: '园林绿化局', desc: '负责城市绿化和园林管理' },
            { id: 'dept_008', name: '应急管理局', desc: '负责应急管理和安全生产' },
            { id: 'dept_009', name: '市场监督管理局', desc: '负责市场监管和质量安全' },
            { id: 'dept_010', name: '公安局', desc: '负责社会治安和交通管理' }
        ];

        const departmentList = document.getElementById('departmentList');
        if (departmentList) {
            departmentList.innerHTML = departments.map(dept => `
                <div class="department-item" data-dept-id="${dept.id}" data-dept-name="${dept.name}">
                    <div class="department-item-info">
                        <div class="department-item-name">${dept.name}</div>
                        <div class="department-item-desc">${dept.desc}</div>
                    </div>
                </div>
            `).join('');

            // 绑定点击事件
            departmentList.querySelectorAll('.department-item').forEach(item => {
                item.addEventListener('click', () => {
                    // 清除其他选中状态
                    departmentList.querySelectorAll('.department-item').forEach(i => i.classList.remove('selected'));

                    // 设置当前选中
                    item.classList.add('selected');
                    this.selectedDepartmentInModal = {
                        id: item.dataset.deptId,
                        name: item.dataset.deptName
                    };
                });
            });
        }
    }

    /**
     * 过滤部门列表
     */
    filterDepartments(searchText) {
        const departmentItems = document.querySelectorAll('.department-item');
        departmentItems.forEach(item => {
            const name = item.dataset.deptName.toLowerCase();
            const desc = item.querySelector('.department-item-desc').textContent.toLowerCase();
            const search = searchText.toLowerCase();

            if (name.includes(search) || desc.includes(search)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    /**
     * 确认部门选择
     */
    confirmDepartmentSelection() {
        if (!this.selectedDepartmentInModal) {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('请选择一个部门', 'warning');
            }
            return;
        }

        if (this.currentSelectionType === 'primary') {
            // 设置主办部门
            const primaryInput = document.getElementById('drawerPrimaryDepartment');
            if (primaryInput) {
                primaryInput.value = this.selectedDepartmentInModal.name;
                primaryInput.classList.add('auto-filled');
            }
        } else if (this.currentSelectionType === 'collaborative') {
            // 添加协办部门
            this.addCollaborativeDepartment(this.selectedDepartmentInModal);
        }

        this.closeDepartmentModal();
    }

    /**
     * 添加协办部门
     */
    addCollaborativeDepartment(department) {
        // 检查是否已存在
        if (this.selectedDepartments.find(d => d.id === department.id)) {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('该部门已在协办部门列表中', 'warning');
            }
            return;
        }

        // 检查是否与主办部门重复
        const primaryInput = document.getElementById('drawerPrimaryDepartment');
        if (primaryInput && primaryInput.value === department.name) {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('协办部门不能与主办部门相同', 'warning');
            }
            return;
        }

        this.selectedDepartments.push(department);
        this.updateCollaborativeDepartmentsDisplay();
    }

    /**
     * 移除协办部门
     */
    removeCollaborativeDepartment(departmentId) {
        this.selectedDepartments = this.selectedDepartments.filter(d => d.id !== departmentId);
        this.updateCollaborativeDepartmentsDisplay();
    }

    /**
     * 更新协办部门显示
     */
    updateCollaborativeDepartmentsDisplay() {
        const container = document.getElementById('selectedDepartments');
        if (!container) return;

        if (this.selectedDepartments.length === 0) {
            container.innerHTML = '<div class="no-departments">暂无协办部门</div>';
        } else {
            container.innerHTML = this.selectedDepartments.map(dept => `
                <div class="department-tag">
                    ${dept.name}
                    <button class="remove-department" onclick="window.incomingCallApp.getDrawerManager().removeCollaborativeDepartment('${dept.id}')">×</button>
                </div>
            `).join('');
        }
    }
}

// 导出模块
window.DrawerManager = DrawerManager;
