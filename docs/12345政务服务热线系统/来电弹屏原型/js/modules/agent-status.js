/**
 * 坐席状态管理模块
 * 负责管理坐席状态、计时器等功能
 */

class AgentStatusManager {
    constructor() {
        // 坐席状态管理
        this.agentStatus = 'available'; // available, busy, calling, break
        this.statusStartTime = Date.now();
        this.workStartTime = Date.now() - (1 * 60 * 60 * 1000 + 45 * 60 * 1000 + 32 * 1000); // 模拟已工作1小时45分32秒
        this.statusTimer = null;
        this.workTimer = null;
    }

    /**
     * 初始化坐席状态管理
     */
    init() {
        this.updateStatusDisplay();
        this.startStatusTimer();
        this.startWorkTimer();
        this.bindStatusEvents();
        this.updateButtonStates(); // 设置初始按钮状态
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        document.querySelectorAll('.btn-status').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.status === this.agentStatus) {
                btn.classList.add('active');
            }
        });
    }

    /**
     * 禁用状态切换按钮（通话中使用）
     */
    disableStatusButtons() {
        document.querySelectorAll('.btn-status').forEach(btn => {
            btn.disabled = true;
            btn.style.opacity = '0.5';
            btn.style.cursor = 'not-allowed';
        });
    }

    /**
     * 启用状态切换按钮
     */
    enableStatusButtons() {
        document.querySelectorAll('.btn-status').forEach(btn => {
            btn.disabled = false;
            btn.style.opacity = '1';
            btn.style.cursor = 'pointer';
        });
    }

    /**
     * 绑定状态栏事件
     */
    bindStatusEvents() {
        // 状态控制按钮
        document.querySelectorAll('.btn-status').forEach(btn => {
            btn.addEventListener('click', () => {
                const newStatus = btn.dataset.status;
                this.changeStatus(newStatus);
            });
        });
    }

    /**
     * 更改坐席状态
     * @param {string} newStatus - 新状态
     */
    changeStatus(newStatus) {
        if (this.agentStatus === newStatus) return;

        // 如果当前正在通话中，不允许切换到其他状态（除非是系统调用切换到calling状态）
        if (this.agentStatus === 'calling' && newStatus !== 'calling' && newStatus !== 'available') {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('通话中无法切换状态', 'warning');
            }
            return;
        }

        this.agentStatus = newStatus;
        this.statusStartTime = Date.now();
        this.updateStatusDisplay();

        // 更新按钮状态
        this.updateButtonStates();

        // 根据状态禁用/启用按钮
        if (newStatus === 'calling') {
            this.disableStatusButtons();
        } else {
            this.enableStatusButtons();
        }

        // 根据状态显示相应消息
        const statusMessages = {
            'available': '状态已切换为空闲',
            'busy': '状态已切换为忙碌',
            'break': '已开始小休'
        };

        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show(
                statusMessages[newStatus] || '状态已更新', 
                'info'
            );
        }
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay() {
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        if (!statusDot || !statusText) return;

        // 清除所有状态类
        statusDot.className = 'status-dot';

        const statusConfig = {
            'available': { text: '空闲', class: '' },
            'busy': { text: '忙碌', class: 'busy' },
            'calling': { text: '通话中', class: 'calling' },
            'break': { text: '小休', class: 'break' }
        };

        const config = statusConfig[this.agentStatus] || statusConfig.available;
        statusText.textContent = config.text;
        if (config.class) {
            statusDot.classList.add(config.class);
        }
    }

    /**
     * 启动状态计时器
     */
    startStatusTimer() {
        if (this.statusTimer) {
            clearInterval(this.statusTimer);
        }

        this.statusTimer = setInterval(() => {
            this.updateStatusTimer();
        }, 1000);
    }

    /**
     * 启动工作时长计时器
     */
    startWorkTimer() {
        if (this.workTimer) {
            clearInterval(this.workTimer);
        }

        this.workTimer = setInterval(() => {
            this.updateWorkTimer();
        }, 1000);
    }

    /**
     * 更新状态计时器显示
     */
    updateStatusTimer() {
        const timerElement = document.getElementById('statusTimer');
        if (!timerElement) return;

        const elapsed = Date.now() - this.statusStartTime;
        timerElement.textContent = this.formatTime(elapsed);
    }

    /**
     * 更新工作时长计时器显示
     */
    updateWorkTimer() {
        const durationElement = document.getElementById('workDuration');
        if (!durationElement) return;

        const elapsed = Date.now() - this.workStartTime;
        durationElement.textContent = this.formatTime(elapsed);
    }

    /**
     * 格式化时间显示
     * @param {number} milliseconds - 毫秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    /**
     * 获取当前状态
     */
    getCurrentStatus() {
        return this.agentStatus;
    }

    /**
     * 销毁计时器
     */
    destroy() {
        if (this.statusTimer) {
            clearInterval(this.statusTimer);
            this.statusTimer = null;
        }
        if (this.workTimer) {
            clearInterval(this.workTimer);
            this.workTimer = null;
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AgentStatusManager;
}
