/**
 * 软电话管理模块
 * 负责管理通话状态、控制按钮等功能
 */

class SoftphoneManager {
    constructor() {
        // 软电话状态管理
        this.callState = 'idle'; // idle, incoming, connected, hold
        this.callStartTime = null;
        this.callTimer = null;
        this.isMuted = false;
        this.isOnHold = false;
        this.isRecording = false;
    }

    /**
     * 初始化软电话功能
     */
    init() {
        this.bindSoftphoneEvents();
        this.updateUI();
    }

    /**
     * 绑定软电话事件
     */
    bindSoftphoneEvents() {
        // 接听按钮
        document.getElementById('answerBtn')?.addEventListener('click', () => {
            if (window.incomingCallApp) {
                window.incomingCallApp.answerCall();
            }
        });

        // 挂断按钮
        document.getElementById('hangupBtn')?.addEventListener('click', () => {
            if (window.incomingCallApp) {
                window.incomingCallApp.hangupCall();
            }
        });

        // 静音按钮
        document.getElementById('muteBtn')?.addEventListener('click', () => {
            this.toggleMute();
        });

        // 保持按钮
        document.getElementById('holdBtn')?.addEventListener('click', () => {
            this.toggleHold();
        });

        // 转接按钮
        document.getElementById('transferBtn')?.addEventListener('click', () => {
            this.transferCall();
        });

        // 录音按钮
        document.getElementById('recordBtn')?.addEventListener('click', () => {
            this.toggleRecording();
        });
    }

    /**
     * 设置通话状态
     * @param {string} state - 通话状态
     */
    setCallState(state) {
        this.callState = state;
        this.updateUI();
    }

    /**
     * 接听电话
     */
    answerCall() {
        this.callState = 'connected';
        this.callStartTime = Date.now();
        this.startCallTimer();
        this.updateUI();
    }

    /**
     * 挂断电话
     */
    hangupCall() {
        this.callState = 'idle';
        this.callStartTime = null;
        this.stopCallTimer();
        this.resetSoftphoneState();
        this.updateUI();
    }

    /**
     * 切换静音状态
     */
    toggleMute() {
        if (this.callState !== 'connected') {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('请先接通电话', 'warning');
            }
            return;
        }

        this.isMuted = !this.isMuted;
        this.updateUI();
        const message = this.isMuted ? '已静音' : '已取消静音';
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show(message, 'info');
        }
    }

    /**
     * 切换保持状态
     */
    toggleHold() {
        if (this.callState !== 'connected') {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('请先接通电话', 'warning');
            }
            return;
        }

        this.isOnHold = !this.isOnHold;
        this.callState = this.isOnHold ? 'hold' : 'connected';
        this.updateUI();
        const message = this.isOnHold ? '通话已保持' : '通话已恢复';
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show(message, 'info');
        }
    }

    /**
     * 转接电话
     */
    transferCall() {
        if (this.callState !== 'connected') {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('请先接通电话', 'warning');
            }
            return;
        }

        const transferTarget = prompt('请输入转接号码或坐席工号：');
        if (transferTarget) {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show(`正在转接至：${transferTarget}`, 'info');
            }
            // 这里可以实现实际的转接逻辑
            setTimeout(() => {
                if (window.incomingCallApp) {
                    window.incomingCallApp.getNotification().show('转接成功', 'success');
                    window.incomingCallApp.hangupCall();
                }
            }, 2000);
        }
    }

    /**
     * 切换录音状态
     */
    toggleRecording() {
        if (this.callState !== 'connected') {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('请先接通电话', 'warning');
            }
            return;
        }

        this.isRecording = !this.isRecording;
        this.updateUI();
        const message = this.isRecording ? '开始录音' : '停止录音';
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show(message, 'info');
        }
    }

    /**
     * 更新软电话UI状态
     */
    updateUI() {
        const answerBtn = document.getElementById('answerBtn');
        const hangupBtn = document.getElementById('hangupBtn');
        const muteBtn = document.getElementById('muteBtn');
        const holdBtn = document.getElementById('holdBtn');
        const transferBtn = document.getElementById('transferBtn');
        const recordBtn = document.getElementById('recordBtn');
        const callTimer = document.getElementById('callTimer');

        // 根据通话状态显示/隐藏按钮
        if (this.callState === 'incoming') {
            answerBtn.style.display = 'flex';
            hangupBtn.style.display = 'flex';
            muteBtn.style.display = 'none';
            holdBtn.style.display = 'none';
            transferBtn.style.display = 'none';
            recordBtn.style.display = 'none';
            callTimer.style.display = 'none';
        } else if (this.callState === 'connected' || this.callState === 'hold') {
            answerBtn.style.display = 'none';
            hangupBtn.style.display = 'flex';
            muteBtn.style.display = 'flex';
            holdBtn.style.display = 'flex';
            transferBtn.style.display = 'flex';
            recordBtn.style.display = 'flex';
            callTimer.style.display = 'flex';
        } else {
            answerBtn.style.display = 'flex';
            hangupBtn.style.display = 'none';
            muteBtn.style.display = 'none';
            holdBtn.style.display = 'none';
            transferBtn.style.display = 'none';
            recordBtn.style.display = 'none';
            callTimer.style.display = 'none';
        }

        // 更新按钮状态
        if (muteBtn) {
            muteBtn.classList.toggle('active', this.isMuted);
            muteBtn.querySelector('.icon').textContent = this.isMuted ? '🔇' : '🔊';
            muteBtn.querySelector('.label').textContent = this.isMuted ? '取消静音' : '静音';
        }

        if (holdBtn) {
            holdBtn.classList.toggle('active', this.isOnHold);
            holdBtn.querySelector('.icon').textContent = this.isOnHold ? '▶️' : '⏸️';
            holdBtn.querySelector('.label').textContent = this.isOnHold ? '恢复' : '保持';
        }

        if (recordBtn) {
            recordBtn.classList.toggle('active', this.isRecording);
            recordBtn.querySelector('.icon').textContent = this.isRecording ? '⏹️' : '🎙️';
            recordBtn.querySelector('.label').textContent = this.isRecording ? '停止录音' : '录音';
        }
    }

    /**
     * 启动通话计时器
     */
    startCallTimer() {
        if (this.callTimer) {
            clearInterval(this.callTimer);
        }

        this.callTimer = setInterval(() => {
            this.updateCallTimer();
        }, 1000);
    }

    /**
     * 停止通话计时器
     */
    stopCallTimer() {
        if (this.callTimer) {
            clearInterval(this.callTimer);
            this.callTimer = null;
        }
    }

    /**
     * 更新通话计时器显示
     */
    updateCallTimer() {
        if (!this.callStartTime) return;

        const elapsed = Date.now() - this.callStartTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);

        const timerText = document.getElementById('callTimerText');
        if (timerText) {
            timerText.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    /**
     * 重置软电话状态
     */
    resetSoftphoneState() {
        this.isMuted = false;
        this.isOnHold = false;
        this.isRecording = false;
    }

    /**
     * 销毁计时器
     */
    destroy() {
        this.stopCallTimer();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SoftphoneManager;
}
