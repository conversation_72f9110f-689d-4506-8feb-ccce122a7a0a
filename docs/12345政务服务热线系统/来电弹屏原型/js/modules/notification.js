/**
 * 通知管理模块
 * 负责显示各种类型的通知消息
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 4000;
    }

    /**
     * 初始化通知管理
     */
    init() {
        // 创建通知容器
        this.createNotificationContainer();
    }

    /**
     * 创建通知容器
     */
    createNotificationContainer() {
        if (document.getElementById('notificationContainer')) return;

        const container = document.createElement('div');
        container.id = 'notificationContainer';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            pointer-events: none;
        `;
        document.body.appendChild(container);
    }

    /**
     * 显示通知消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示持续时间
     */
    show(message, type = 'info', duration = this.defaultDuration) {
        // 限制通知数量
        if (this.notifications.length >= this.maxNotifications) {
            this.removeOldestNotification();
        }

        const notification = this.createNotification(message, type);
        this.addNotification(notification, duration);
    }

    /**
     * 创建通知元素
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     * @returns {Element} 通知元素
     */
    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        // 添加图标
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        notification.innerHTML = `
            <span class="notification-icon">${icons[type] || icons.info}</span>
            <span class="notification-message">${message}</span>
            <button class="notification-close" aria-label="关闭">×</button>
        `;
        
        // 添加样式
        Object.assign(notification.style, {
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            padding: '16px 20px',
            borderRadius: '12px',
            color: 'white',
            fontWeight: '600',
            fontSize: '14px',
            minWidth: '300px',
            maxWidth: '400px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.1)',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            pointerEvents: 'auto',
            position: 'relative'
        });
        
        // 根据类型设置背景色
        const colors = {
            success: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
            error: 'linear-gradient(135deg, #dc3545 0%, #e74c3c 100%)',
            warning: 'linear-gradient(135deg, #ffc107 0%, #f39c12 100%)',
            info: 'linear-gradient(135deg, #17a2b8 0%, #3498db 100%)'
        };
        notification.style.background = colors[type] || colors.info;

        // 设置图标样式
        const icon = notification.querySelector('.notification-icon');
        icon.style.cssText = `
            font-size: 18px;
            flex-shrink: 0;
        `;

        // 设置消息样式
        const messageEl = notification.querySelector('.notification-message');
        messageEl.style.cssText = `
            flex: 1;
            line-height: 1.4;
            word-break: break-word;
        `;

        // 设置关闭按钮样式
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s ease;
            flex-shrink: 0;
        `;

        // 关闭按钮悬停效果
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
        });

        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.backgroundColor = 'transparent';
        });

        // 绑定关闭事件
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.hide(notification);
        });

        // 点击通知本身也可以关闭
        notification.addEventListener('click', () => {
            this.hide(notification);
        });

        return notification;
    }

    /**
     * 添加通知到容器
     * @param {Element} notification - 通知元素
     * @param {number} duration - 显示持续时间
     */
    addNotification(notification, duration) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        // 添加到容器
        container.appendChild(notification);
        this.notifications.push(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hide(notification);
            }, duration);
        }
    }

    /**
     * 隐藏通知
     * @param {Element} notification - 通知元素
     */
    hide(notification) {
        if (!notification || !notification.parentNode) return;

        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            
            // 从数组中移除
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 400);
    }

    /**
     * 移除最旧的通知
     */
    removeOldestNotification() {
        if (this.notifications.length > 0) {
            this.hide(this.notifications[0]);
        }
    }

    /**
     * 清除所有通知
     */
    clearAll() {
        this.notifications.forEach(notification => {
            this.hide(notification);
        });
    }

    /**
     * 显示成功通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示持续时间
     */
    success(message, duration = this.defaultDuration) {
        this.show(message, 'success', duration);
    }

    /**
     * 显示错误通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示持续时间
     */
    error(message, duration = this.defaultDuration) {
        this.show(message, 'error', duration);
    }

    /**
     * 显示警告通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示持续时间
     */
    warning(message, duration = this.defaultDuration) {
        this.show(message, 'warning', duration);
    }

    /**
     * 显示信息通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示持续时间
     */
    info(message, duration = this.defaultDuration) {
        this.show(message, 'info', duration);
    }

    /**
     * 显示持久通知（不自动消失）
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    persistent(message, type = 'info') {
        this.show(message, type, 0);
    }

    /**
     * 获取当前通知数量
     */
    getCount() {
        return this.notifications.length;
    }

    /**
     * 销毁通知管理器
     */
    destroy() {
        this.clearAll();
        const container = document.getElementById('notificationContainer');
        if (container) {
            container.remove();
        }
        this.notifications = [];
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
