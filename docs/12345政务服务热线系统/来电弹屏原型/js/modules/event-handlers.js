/**
 * 事件处理模块
 * 负责绑定和管理各种页面事件
 */

class EventHandlersManager {
    constructor() {
        this.app = null;
        this.boundEvents = [];
    }

    /**
     * 初始化事件处理
     * @param {IncomingCallPopup} app - 主应用实例
     */
    init(app) {
        this.app = app;
        this.bindAllEvents();
    }

    /**
     * 绑定所有事件
     */
    bindAllEvents() {
        this.bindAddressEvents();
        this.bindKnowledgeBaseEvents();
        this.bindScriptEvents();
        this.bindFormEvents();
        this.bindKeyboardEvents();
        this.bindWindowEvents();
    }

    /**
     * 绑定地址相关事件
     */
    bindAddressEvents() {
        // 地址链接点击事件
        const addressLinks = document.querySelectorAll('.address-link');
        addressLinks.forEach(link => {
            const handler = (e) => {
                e.preventDefault();
                this.showAddressOnMap(link.textContent);
            };
            link.addEventListener('click', handler);
            this.boundEvents.push({ element: link, event: 'click', handler });
        });
    }

    /**
     * 绑定知识库相关事件
     */
    bindKnowledgeBaseEvents() {
        // 知识库项目点击
        const kbItems = document.querySelectorAll('.kb-item');
        kbItems.forEach(item => {
            const handler = () => {
                const title = item.querySelector('.kb-title')?.textContent || '知识库项目';
                this.openKnowledgeBase(title);
            };
            item.addEventListener('click', handler);
            this.boundEvents.push({ element: item, event: 'click', handler });
        });
    }

    /**
     * 绑定话术相关事件
     */
    bindScriptEvents() {
        // 话术复制功能
        const scriptTexts = document.querySelectorAll('.script-item p');
        scriptTexts.forEach(script => {
            const handler = () => {
                this.copyScript(script.textContent);
            };
            script.addEventListener('click', handler);
            this.boundEvents.push({ element: script, event: 'click', handler });
        });
    }

    /**
     * 绑定表单相关事件
     */
    bindFormEvents() {
        // 表单输入验证
        const formInputs = document.querySelectorAll('.form-group input, .form-group textarea, .form-group select');
        formInputs.forEach(input => {
            const focusHandler = () => {
                this.handleInputFocus(input);
            };
            const blurHandler = () => {
                this.handleInputBlur(input);
            };
            const changeHandler = () => {
                this.handleInputChange(input);
            };

            input.addEventListener('focus', focusHandler);
            input.addEventListener('blur', blurHandler);
            input.addEventListener('change', changeHandler);

            this.boundEvents.push(
                { element: input, event: 'focus', handler: focusHandler },
                { element: input, event: 'blur', handler: blurHandler },
                { element: input, event: 'change', handler: changeHandler }
            );
        });
    }

    /**
     * 绑定键盘事件
     */
    bindKeyboardEvents() {
        const keydownHandler = (e) => {
            this.handleKeyboardShortcuts(e);
        };
        document.addEventListener('keydown', keydownHandler);
        this.boundEvents.push({ element: document, event: 'keydown', handler: keydownHandler });
    }

    /**
     * 绑定窗口事件
     */
    bindWindowEvents() {
        const resizeHandler = () => {
            this.handleWindowResize();
        };
        const beforeUnloadHandler = (e) => {
            this.handleBeforeUnload(e);
        };

        window.addEventListener('resize', resizeHandler);
        window.addEventListener('beforeunload', beforeUnloadHandler);

        this.boundEvents.push(
            { element: window, event: 'resize', handler: resizeHandler },
            { element: window, event: 'beforeunload', handler: beforeUnloadHandler }
        );
    }

    /**
     * 在地图上显示地址
     * @param {string} address - 地址信息
     */
    showAddressOnMap(address) {
        if (this.app) {
            this.app.getNotification().show(`正在地图上定位：${address}`, 'info');
        }
        // 这里可以集成实际的地图API
        console.log('显示地址:', address);
    }

    /**
     * 打开知识库
     * @param {string} title - 知识库标题
     */
    openKnowledgeBase(title) {
        if (this.app) {
            this.app.getNotification().show(`正在打开知识库：${title}`, 'info');
        }
        // 这里可以打开知识库详情页面
        console.log('打开知识库:', title);
    }

    /**
     * 复制话术
     * @param {string} script - 话术内容
     */
    copyScript(script) {
        navigator.clipboard.writeText(script).then(() => {
            if (this.app) {
                this.app.getNotification().show('话术已复制到剪贴板', 'success');
            }
        }).catch(() => {
            if (this.app) {
                this.app.getNotification().show('复制失败，请手动复制', 'error');
            }
        });
    }

    /**
     * 处理输入框获得焦点
     * @param {Element} input - 输入框元素
     */
    handleInputFocus(input) {
        // 添加焦点样式
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            formGroup.classList.add('focused');
        }
    }

    /**
     * 处理输入框失去焦点
     * @param {Element} input - 输入框元素
     */
    handleInputBlur(input) {
        // 移除焦点样式
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            formGroup.classList.remove('focused');
        }

        // 验证输入
        this.validateInput(input);
    }

    /**
     * 处理输入框内容变化
     * @param {Element} input - 输入框元素
     */
    handleInputChange(input) {
        // 实时验证
        this.validateInput(input);
        
        // 如果是必填字段，检查是否已填写
        if (input.required) {
            const formGroup = input.closest('.form-group');
            if (formGroup) {
                if (input.value.trim()) {
                    formGroup.classList.add('filled');
                } else {
                    formGroup.classList.remove('filled');
                }
            }
        }
    }

    /**
     * 验证输入
     * @param {Element} input - 输入框元素
     */
    validateInput(input) {
        const value = input.value.trim();
        const formGroup = input.closest('.form-group');
        
        if (!formGroup) return;

        // 清除之前的错误状态
        formGroup.classList.remove('error');
        
        // 必填验证
        if (input.required && !value) {
            formGroup.classList.add('error');
            return false;
        }

        // 邮箱验证
        if (input.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                formGroup.classList.add('error');
                return false;
            }
        }

        // 电话验证
        if (input.type === 'tel' && value) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(value)) {
                formGroup.classList.add('error');
                return false;
            }
        }

        return true;
    }

    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + S: 保存草稿
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            if (this.app && this.app.getTicketManager()) {
                this.app.getTicketManager().saveDraft();
            }
        }

        // Ctrl/Cmd + Enter: 提交工单
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (this.app && this.app.getTicketManager()) {
                this.app.getTicketManager().submit();
            }
        }

        // Escape: 关闭模态框或返回
        if (e.key === 'Escape') {
            // 这里可以添加关闭模态框的逻辑
            console.log('Escape pressed');
        }

        // F1: 显示帮助
        if (e.key === 'F1') {
            e.preventDefault();
            if (this.app) {
                this.app.getNotification().show('快捷键：Ctrl+S保存，Ctrl+Enter提交，Esc返回', 'info');
            }
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 响应式布局调整
        const container = document.querySelector('.container');
        if (container && window.innerWidth < 1200) {
            container.classList.add('compact');
        } else if (container) {
            container.classList.remove('compact');
        }
    }

    /**
     * 处理页面卸载前事件
     * @param {BeforeUnloadEvent} e - 卸载事件
     */
    handleBeforeUnload(e) {
        // 检查是否有未保存的数据
        const hasUnsavedData = this.checkUnsavedData();
        
        if (hasUnsavedData) {
            e.preventDefault();
            e.returnValue = '您有未保存的数据，确定要离开吗？';
            return e.returnValue;
        }
    }

    /**
     * 检查是否有未保存的数据
     * @returns {boolean} 是否有未保存的数据
     */
    checkUnsavedData() {
        // 检查表单是否有修改
        const formInputs = document.querySelectorAll('.draft-form input, .draft-form textarea, .draft-form select');
        for (let input of formInputs) {
            if (input.value.trim() && !input.classList.contains('auto-filled')) {
                return true;
            }
        }
        return false;
    }

    /**
     * 动态绑定新元素的事件
     * @param {Element} element - 新元素
     */
    bindNewElement(element) {
        // 为新添加的元素绑定相应的事件
        if (element.classList.contains('address-link')) {
            const handler = (e) => {
                e.preventDefault();
                this.showAddressOnMap(element.textContent);
            };
            element.addEventListener('click', handler);
            this.boundEvents.push({ element, event: 'click', handler });
        }

        if (element.classList.contains('kb-item')) {
            const handler = () => {
                const title = element.querySelector('.kb-title')?.textContent || '知识库项目';
                this.openKnowledgeBase(title);
            };
            element.addEventListener('click', handler);
            this.boundEvents.push({ element, event: 'click', handler });
        }

        // 为新的按钮添加波纹效果
        if (element.classList.contains('btn') && this.app) {
            const handler = (e) => {
                this.app.getUIEffects().createRippleEffect(e, element);
            };
            element.addEventListener('click', handler);
            this.boundEvents.push({ element, event: 'click', handler });
        }
    }

    /**
     * 移除所有事件监听器
     */
    unbindAllEvents() {
        this.boundEvents.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.boundEvents = [];
    }

    /**
     * 销毁事件管理器
     */
    destroy() {
        this.unbindAllEvents();
        this.app = null;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EventHandlersManager;
}
