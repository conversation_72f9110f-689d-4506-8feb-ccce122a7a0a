/**
 * 工单管理模块
 * 负责工单创建、提交、管理等功能
 */

class TicketManager {
    constructor() {
        this.currentTicket = null;
    }

    /**
     * 初始化工单管理
     */
    init() {
        this.bindAllTicketEvents();
    }

    /**
     * 绑定所有工单相关事件
     */
    bindAllTicketEvents() {
        // 绑定具体的操作按钮
        document.getElementById('saveDraftBtn')?.addEventListener('click', () => this.saveDraft());
        document.getElementById('resetFormBtn')?.addEventListener('click', () => this.resetToSummary());
        document.getElementById('instantCloseBtn')?.addEventListener('click', () => this.instantClose());
        document.getElementById('confirmSubmitBtn')?.addEventListener('click', () => this.submit());

        // 工单操作按钮
        document.querySelectorAll('.ticket-actions .btn-link').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleTicketAction(e.target.textContent, e.target.closest('.ticket-card'));
            });
        });

        // 快捷操作按钮
        document.querySelector('.quick-actions')?.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn')) {
                this.handleQuickAction(e.target.textContent);
            }
        });

        // 表单操作按钮
        document.querySelector('.action-buttons')?.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn')) {
                this.handleFormAction(e.target.textContent);
            }
        });
    }

    /**
     * 创建新工单 - 核心功能（使用抽屉模式）
     */
    createNew() {
        if (!window.incomingCallApp || !window.incomingCallApp.isCallConnected) {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('请先接通电话后再创建工单', 'warning');
            }
            return;
        }

        // 打开新建工单抽屉
        if (window.incomingCallApp && window.incomingCallApp.getDrawerManager) {
            const drawerManager = window.incomingCallApp.getDrawerManager();
            if (drawerManager) {
                drawerManager.open();
            }
        }

        // 创建工单对象
        this.currentTicket = {
            id: this.generateTicketId(),
            status: 'draft',
            createdAt: new Date(),
            data: {}
        };
    }

    /**
     * 处理快捷操作
     * @param {string} action - 操作类型
     */
    handleQuickAction(action) {
        switch (action) {
            case '新建工单':
                this.createNew();
                break;
            case '关联历史工单':
                if (window.incomingCallApp) {
                    window.incomingCallApp.getNotification().show('正在查找相关历史工单...', 'info');
                }
                break;
            case '添加/修改用户标签':
                this.showTagEditor();
                break;
        }
    }

    /**
     * 处理工单操作
     * @param {string} action - 操作类型
     * @param {Element} ticketCard - 工单卡片元素
     */
    handleTicketAction(action, ticketCard) {
        const ticketId = ticketCard.querySelector('.ticket-id').textContent;
        
        switch (action) {
            case '查看详情':
                if (window.incomingCallApp) {
                    window.incomingCallApp.getNotification().show(`正在查看工单 ${ticketId} 详情...`, 'info');
                }
                break;
            case '一键重启此工单':
                if (window.incomingCallApp) {
                    window.incomingCallApp.getNotification().show(`正在重启工单 ${ticketId}...`, 'warning');
                }
                break;
        }
    }

    /**
     * 处理表单操作
     * @param {string} action - 操作类型
     */
    handleFormAction(action) {
        switch (action) {
            case '暂存草稿':
                this.saveDraft();
                break;
            case '重新填写':
                this.resetToSummary();
                break;
            case '即时办结':
                this.instantClose();
                break;
            case '确认派发':
                this.submit();
                break;
        }
    }

    /**
     * 保存草稿
     */
    saveDraft() {
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('工单草稿已保存', 'success');
        }
        
        if (this.currentTicket) {
            this.currentTicket.status = 'draft';
            this.currentTicket.data = this.getFormData();
        }
    }

    /**
     * 重新填写 - 返回双核信息呈现状态
     */
    resetToSummary() {
        const dualInfoDisplay = document.getElementById('dualInfoDisplay');
        const smartTicketDraft = document.getElementById('smartTicketDraft');
        const actionButtons = document.getElementById('actionButtons');

        if (smartTicketDraft) smartTicketDraft.style.display = 'none';
        if (actionButtons) actionButtons.style.display = 'none';
        if (dualInfoDisplay) dualInfoDisplay.style.display = 'grid';

        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('已返回双核信息呈现状态，可重新创建工单', 'info');
        }
    }

    /**
     * 即时办结
     */
    instantClose() {
        const reason = prompt('请输入即时办结原因：');
        if (reason) {
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show('工单已即时办结，进入回访阶段', 'success');
            }
            
            if (this.currentTicket) {
                this.currentTicket.status = 'closed';
                this.currentTicket.closeReason = reason;
                this.currentTicket.closedAt = new Date();
            }
        }
    }

    /**
     * 提交工单
     */
    submit() {
        // 模拟提交过程
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('正在提交工单...', 'info');
        }

        setTimeout(() => {
            const ticketId = this.currentTicket ? this.currentTicket.id : this.generateTicketId();
            if (window.incomingCallApp) {
                window.incomingCallApp.getNotification().show(`工单提交成功！工单号：${ticketId}`, 'success');
            }

            // 添加到历史工单列表
            this.addToHistoryList(ticketId);

            // 更新工单状态
            if (this.currentTicket) {
                this.currentTicket.status = 'submitted';
                this.currentTicket.submittedAt = new Date();
            }

            // 通话结束，状态改为空闲
            if (window.incomingCallApp) {
                window.incomingCallApp.getAgentStatus().changeStatus('available');
                window.incomingCallApp.isCallConnected = false;
            }
        }, 2000);
    }

    /**
     * 生成工单号
     * @returns {string} 工单号
     */
    generateTicketId() {
        const now = new Date();
        const dateStr = now.getFullYear().toString() + 
                       (now.getMonth() + 1).toString().padStart(2, '0') + 
                       now.getDate().toString().padStart(2, '0');
        const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return dateStr + randomNum;
    }

    /**
     * 添加到历史工单列表
     * @param {string} ticketId - 工单号
     */
    addToHistoryList(ticketId) {
        const ticketList = document.querySelector('.ticket-list');
        if (!ticketList) return;

        const newTicket = document.createElement('div');
        newTicket.className = 'ticket-card processing';
        newTicket.innerHTML = `
            <div class="ticket-header">
                <span class="ticket-id">${ticketId}</span>
                <span class="ticket-status">【处理中】</span>
            </div>
            <div class="ticket-category">市政设施 -> 排水设施</div>
            <div class="ticket-title">阳光小区南门积水问题</div>
            <div class="ticket-time">${new Date().toLocaleString('zh-CN')}</div>
            <div class="ticket-actions">
                <button class="btn-link">查看详情</button>
                <button class="btn-link">一键重启此工单</button>
            </div>
        `;
        
        // 插入到列表顶部
        ticketList.insertBefore(newTicket, ticketList.firstChild);
        
        // 重新绑定事件
        this.bindTicketEvents(newTicket);
    }

    /**
     * 为新工单绑定事件
     * @param {Element} ticketCard - 工单卡片元素
     */
    bindTicketEvents(ticketCard) {
        if (!ticketCard) return;

        ticketCard.querySelectorAll('.btn-link').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleTicketAction(e.target.textContent, ticketCard);
            });
        });
    }

    /**
     * 显示标签编辑器
     */
    showTagEditor() {
        // 这里可以实现一个模态框来编辑标签
        if (window.incomingCallApp) {
            window.incomingCallApp.getNotification().show('标签编辑功能开发中...', 'info');
        }
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        const formData = {};
        document.querySelectorAll('.draft-form input, .draft-form textarea, .draft-form select').forEach(input => {
            if (input.name) {
                formData[input.name] = input.value;
            }
        });
        return formData;
    }

    /**
     * 清空表单
     */
    clearForm() {
        document.querySelectorAll('.auto-filled').forEach(input => {
            input.value = '';
            input.classList.remove('auto-filled');
        });
    }

    /**
     * 获取当前工单
     */
    getCurrentTicket() {
        return this.currentTicket;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TicketManager;
}
