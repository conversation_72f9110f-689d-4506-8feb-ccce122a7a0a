/* 业务流程页面专用样式 */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 流程概览统计 */
.workflow-overview {
    padding: 3rem 0;
    background: white;
}

.workflow-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    min-width: 150px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* 流程阶段 */
.workflow-stages {
    padding: 2rem 0 4rem;
    background: #f8f9fa;
}

.stage-section {
    margin-bottom: 4rem;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.stage-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stage-number {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    flex-shrink: 0;
}

.stage-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.stage-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

.stage-content {
    padding: 2rem;
}

/* 流程步骤 */
.process-steps {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.process-step {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.step-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

.step-description {
    font-style: italic;
    color: #666;
    margin-bottom: 1rem;
}

.step-content ul {
    list-style: none;
    padding: 0;
}

.step-content li {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
    color: #555;
}

.step-content li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

/* 场景样式 */
.scenarios {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.scenario {
    padding: 1.5rem;
    border-radius: 12px;
    border-left: 4px solid;
}

.scenario.immediate {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.scenario.department {
    background: #d4edda;
    border-left-color: #28a745;
}

.scenario.district {
    background: #d1ecf1;
    border-left-color: #17a2b8;
}

.scenario.collaboration {
    background: #f8d7da;
    border-left-color: #dc3545;
}

.scenario h5 {
    margin-bottom: 1rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.scenario p {
    color: #555;
    line-height: 1.6;
    margin: 0;
}

/* 轨道样式 */
.tracks {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.track {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    border-left: 4px solid;
}

.track.municipal {
    border-left-color: #667eea;
}

.track.district {
    border-left-color: #28a745;
}

.track h4 {
    margin-bottom: 1.5rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.track-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.track-step {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.track-step .step-number {
    width: 30px;
    height: 30px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.step-info strong {
    color: #333;
    display: block;
    margin-bottom: 0.5rem;
}

.step-info p {
    color: #555;
    margin: 0;
    line-height: 1.5;
}

.sub-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.option {
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    color: #495057;
}

.parallel-paths {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.path {
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 5px;
    font-size: 0.9rem;
}

.path strong {
    color: #667eea;
}

/* 高亮框 */
.highlight-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff !important;
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.highlight-box p {
    color: #ffffff !important;
    margin: 0;
}

.highlight-box i {
    font-size: 1.2rem;
}

/* 审核路径 */
.audit-examples {
    margin-top: 1.5rem;
}

.audit-path {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.audit-path h5 {
    margin-bottom: 1rem;
    color: #333;
}

.path-flow {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.path-step {
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #333;
    border: 1px solid #dee2e6;
}

.path-flow i {
    color: #667eea;
}

/* 最终结果 */
.final-outcomes {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.outcome {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
}

.outcome.satisfied {
    background: #d4edda;
    border-left: 4px solid #28a745;
}

.outcome.unsatisfied {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
}

.outcome i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.outcome.satisfied i {
    color: #28a745;
}

.outcome.unsatisfied i {
    color: #dc3545;
}

.outcome strong {
    color: #333;
    display: block;
    margin-bottom: 0.5rem;
}

.outcome p {
    color: #555;
    margin: 0;
    line-height: 1.5;
}

/* 协同流程部分 */
.collaboration-section {
    padding: 4rem 0;
    background: white;
}

.collaboration-types {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.collaboration-type {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    border-left: 4px solid #667eea;
}

.collaboration-type h3 {
    margin-bottom: 1.5rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.scenario-info,
.example-case {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border-left: 3px solid #17a2b8;
}

.example-case {
    border-left-color: #28a745;
}

.collaboration-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.collab-step {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.step-num {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-detail h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.step-detail p {
    color: #555;
    line-height: 1.6;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .workflow-stats {
        gap: 2rem;
    }
    
    .stage-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .process-step {
        flex-direction: column;
        gap: 1rem;
    }
    
    .scenarios {
        grid-template-columns: 1fr;
    }
    
    .path-flow {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .path-flow i {
        transform: rotate(90deg);
    }
    
    .tracks {
        gap: 1.5rem;
    }
    
    .final-outcomes {
        gap: 1.5rem;
    }
    
    .outcome {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .stage-content {
        padding: 1rem;
    }
    
    .track {
        padding: 1rem;
    }
    
    .collaboration-type {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.stage-section {
    animation: slideInLeft 0.6s ease-out;
}

.stage-section:nth-child(even) {
    animation-delay: 0.1s;
}

/* 打印样式 */
@media print {
    .header,
    .footer {
        display: none;
    }
    
    .stage-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 2rem;
    }
    
    .page-header {
        background: none;
        color: #333;
    }
}
