/* AI智能赋能页面专用样式 */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* AI概览区域 */
.ai-overview {
    padding: 4rem 0;
    background: white;
}

.ai-intro {
    display: flex;
    align-items: center;
    gap: 3rem;
    margin-bottom: 3rem;
}

.intro-content {
    flex: 1;
}

.intro-content h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.intro-content p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

.intro-visual {
    flex: 0 0 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ai-brain {
    position: relative;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    animation: brainPulse 3s ease-in-out infinite;
}

.brain-connections {
    position: absolute;
    width: 100%;
    height: 100%;
}

.connection {
    position: absolute;
    width: 2px;
    height: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1px;
    animation: connectionPulse 2s ease-in-out infinite;
}

.connection:nth-child(1) {
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.connection:nth-child(2) {
    right: -15px;
    top: 50%;
    transform: translateY(-50%) rotate(90deg);
    animation-delay: 0.5s;
}

.connection:nth-child(3) {
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 1s;
}

.connection:nth-child(4) {
    left: -15px;
    top: 50%;
    transform: translateY(-50%) rotate(90deg);
    animation-delay: 1.5s;
}

@keyframes brainPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
    }
}

@keyframes connectionPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

.enhancement-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    min-width: 180px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.roles {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.processes {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-icon.operations {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* 赋能分类 */
.enhancement-categories {
    padding: 2rem 0 4rem;
    background: #f8f9fa;
}

.enhancement-category {
    margin-bottom: 4rem;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.category-icon {
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.category-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.category-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

.category-content {
    padding: 2rem;
}

/* 角色赋能 */
.role-enhancements {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.role-enhancement {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    border-left: 4px solid #667eea;
}

.role-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.role-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.role-info h4 {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
    color: #333;
}

.role-info p {
    color: #666;
    font-size: 0.9rem;
}

.enhancement-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1rem;
}

.feature-item {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.feature-content h5 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.feature-content p {
    color: #555;
    line-height: 1.5;
    margin: 0;
    font-size: 0.9rem;
}

/* 流程赋能 */
.process-enhancements {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.process-enhancement {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #28a745;
}

.process-enhancement.revolutionary {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff5f5 0%, #f8f9fa 100%);
}

.enhancement-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.enhancement-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.process-enhancement.revolutionary .enhancement-icon {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
}

.enhancement-header h4 {
    font-size: 1.1rem;
    color: #333;
    margin: 0;
}

.revolutionary-badge {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: auto;
}

.process-enhancement p {
    color: #555;
    line-height: 1.6;
    margin: 0;
}

/* 操作赋能 */
.operation-enhancements {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.operation-enhancement {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    border-left: 4px solid #ffc107;
}

.operation-enhancement .enhancement-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.enhancement-content h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.enhancement-content p {
    color: #555;
    line-height: 1.5;
    margin: 0;
    font-size: 0.9rem;
}

/* AI效果展示 */
.ai-benefits {
    padding: 4rem 0;
    background: white;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.benefit-item {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.benefit-item h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

.benefit-item p {
    color: #666;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-intro {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }
    
    .enhancement-stats {
        gap: 2rem;
        flex-direction: column;
        align-items: center;
    }
    
    .stat-card {
        width: 100%;
        max-width: 300px;
    }
    
    .category-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .enhancement-features {
        grid-template-columns: 1fr;
    }
    
    .operation-enhancements {
        grid-template-columns: 1fr;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .category-content {
        padding: 1rem;
    }
    
    .role-enhancement {
        padding: 1rem;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .operation-enhancement {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.feature-item,
.operation-enhancement,
.benefit-item {
    animation: fadeInScale 0.6s ease-out;
}

.feature-item:nth-child(even) {
    animation-delay: 0.1s;
}

/* 打印样式 */
@media print {
    .header,
    .footer {
        display: none;
    }
    
    .enhancement-category {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 2rem;
    }
    
    .page-header {
        background: none;
        color: #333;
    }
}
