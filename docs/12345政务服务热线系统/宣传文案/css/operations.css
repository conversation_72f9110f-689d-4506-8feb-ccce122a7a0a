/* 功能操作页面专用样式 */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 操作概览统计 */
.operations-overview {
    padding: 3rem 0;
    background: white;
}

.operations-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    min-width: 200px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.create {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-icon.process {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.audit {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stat-icon.global {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

/* 操作阶段 */
.operations-stages {
    padding: 2rem 0 4rem;
    background: #f8f9fa;
}

.stage-section {
    margin-bottom: 3rem;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.stage-header {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.stage-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stage-icon.create {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stage-icon.process {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stage-icon.audit {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stage-icon.revisit {
    background: linear-gradient(135deg, #17a2b8 0%, #6610f2 100%);
}

.stage-icon.global {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
}

.stage-title {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.stage-subtitle {
    color: #666;
    font-size: 1rem;
}

.stage-content {
    padding: 2rem;
}

/* 角色操作 */
.role-operations {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.role-group {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    border-left: 4px solid #667eea;
}

.role-title {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.role-title i {
    color: #667eea;
}

.operations-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1rem;
}

/* 操作项 */
.operation-item {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.operation-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.operation-item.primary {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.operation-item.highlight {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
}

.operation-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.operation-item.highlight .operation-icon {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.operation-content h5 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.operation-content p {
    color: #555;
    line-height: 1.5;
    margin: 0;
    font-size: 0.9rem;
}

.highlight-text {
    color: #856404 !important;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .operations-stats {
        gap: 2rem;
        flex-direction: column;
        align-items: center;
    }
    
    .stat-card {
        width: 100%;
        max-width: 300px;
    }
    
    .stage-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .operations-list {
        grid-template-columns: 1fr;
    }
    
    .operation-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .role-title {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .stage-content {
        padding: 1rem;
    }
    
    .role-group {
        padding: 1rem;
    }
    
    .operation-item {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.operation-item {
    animation: slideInRight 0.6s ease-out;
}

.operation-item:nth-child(even) {
    animation-delay: 0.1s;
}

.operation-item:nth-child(3n) {
    animation-delay: 0.2s;
}

/* 特殊效果 */
.operation-item.highlight::before {
    content: '⚡';
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ffc107;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    animation: pulse 2s infinite;
}

.operation-item {
    position: relative;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* 打印样式 */
@media print {
    .header,
    .footer {
        display: none;
    }
    
    .stage-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 2rem;
    }
    
    .page-header {
        background: none;
        color: #333;
    }
    
    .operation-item {
        break-inside: avoid;
    }
}
