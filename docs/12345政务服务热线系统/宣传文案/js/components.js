/**
 * 组件加载器
 * 用于加载和管理页面组件
 */

/**
 * 加载Header组件
 * @param {string} currentPage - 当前页面标识，用于设置导航active状态
 * @returns {Promise} - 返回Promise以便链式调用
 */
async function loadHeader(currentPage = '') {
    try {
        const response = await fetch('components/header.html');
        const headerHTML = await response.text();

        // 插入header到页面
        const headerContainer = document.getElementById('header-container');
        if (headerContainer) {
            headerContainer.innerHTML = headerHTML;

            // 设置当前页面的导航active状态
            setActiveNavigation(currentPage);
        }

        return Promise.resolve();
    } catch (error) {
        console.error('加载Header组件失败:', error);
        return Promise.reject(error);
    }
}

/**
 * 设置导航active状态
 * @param {string} currentPage - 当前页面标识
 */
function setActiveNavigation(currentPage) {
    // 移除所有active类
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // 根据当前页面设置active状态
    if (currentPage) {
        const activeLink = document.querySelector(`[data-page="${currentPage}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }
}

/**
 * 根据当前URL自动检测页面类型
 */
function getCurrentPageType() {
    const path = window.location.pathname;
    const filename = path.split('/').pop().replace('.html', '');
    
    // 处理index页面的特殊情况
    if (filename === 'index' || filename === '') {
        return 'index';
    }
    
    return filename;
}

/**
 * 加载Footer组件
 */
async function loadFooter() {
    try {
        const response = await fetch('components/footer.html');
        const footerHTML = await response.text();

        // 插入footer到页面
        const footerContainer = document.getElementById('footer-container');
        if (footerContainer) {
            footerContainer.innerHTML = footerHTML;
        }
    } catch (error) {
        console.error('加载Footer组件失败:', error);
    }
}

/**
 * 初始化移动端菜单
 */
function initializeMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav');
    const overlay = document.querySelector('.mobile-menu-overlay');

    if (mobileMenuBtn && navMenu) {
        // 切换菜单状态的函数
        function toggleMenu() {
            const isActive = mobileMenuBtn.classList.contains('active');

            if (isActive) {
                // 关闭菜单
                mobileMenuBtn.classList.remove('active');
                navMenu.classList.remove('active');
                if (overlay) overlay.classList.remove('active');
                document.body.style.overflow = '';
            } else {
                // 打开菜单
                mobileMenuBtn.classList.add('active');
                navMenu.classList.add('active');
                if (overlay) overlay.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        // 菜单按钮点击事件
        mobileMenuBtn.addEventListener('click', toggleMenu);

        // 遮罩层点击事件
        if (overlay) {
            overlay.addEventListener('click', toggleMenu);
        }

        // 点击导航链接时关闭菜单
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenuBtn.classList.remove('active');
                navMenu.classList.remove('active');
                if (overlay) overlay.classList.remove('active');
                document.body.style.overflow = '';
            });
        });

        // ESC键关闭菜单
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenuBtn.classList.contains('active')) {
                toggleMenu();
            }
        });
    }
}

/**
 * 初始化组件
 */
function initializeComponents() {
    const currentPage = getCurrentPageType();
    loadHeader(currentPage).then(() => {
        // Header加载完成后初始化移动端菜单
        initializeMobileMenu();
    });
    loadFooter();
}

// 页面加载完成后初始化组件
document.addEventListener('DOMContentLoaded', initializeComponents);
