/**
 * 系统架构页面专用脚本
 * 提供架构图交互和组件展示
 */

document.addEventListener('DOMContentLoaded', function() {
    initArchitecturePage();
});

/**
 * 初始化系统架构页面
 */
function initArchitecturePage() {
    // 初始化架构层级动画
    initLayerAnimations();
    
    // 初始化组件交互
    initComponentInteractions();
    
    // 初始化组织架构交互
    initOrgStructureInteractions();
    
    // 初始化特性展示
    initFeatureShowcase();
    
    // 初始化统计动画
    initStatsAnimation();
    
    // 初始化架构导航
    initArchitectureNavigation();
}

/**
 * 初始化架构层级动画
 */
function initLayerAnimations() {
    const techLayers = document.querySelectorAll('.tech-layer');
    const orgLevels = document.querySelectorAll('.org-level');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('animate-in');
                    animateLayerComponents(entry.target);
                }, index * 200);
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -100px 0px'
    });
    
    [...techLayers, ...orgLevels].forEach(layer => {
        observer.observe(layer);
    });
}

/**
 * 动画化层级组件
 */
function animateLayerComponents(layer) {
    const components = layer.querySelectorAll('.component, .org-unit');
    
    components.forEach((component, index) => {
        setTimeout(() => {
            component.style.opacity = '0';
            component.style.transform = 'translateY(20px)';
            component.style.transition = 'all 0.5s ease-out';
            
            setTimeout(() => {
                component.style.opacity = '1';
                component.style.transform = 'translateY(0)';
            }, 50);
        }, index * 100);
    });
}

/**
 * 初始化组件交互
 */
function initComponentInteractions() {
    const components = document.querySelectorAll('.component');
    
    components.forEach(component => {
        // 添加悬停效果
        component.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.15)';
            
            // 高亮相关组件
            highlightRelatedComponents(this);
        });
        
        component.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
            
            // 移除高亮
            clearComponentHighlights();
        });
        
        // 添加点击详情
        component.addEventListener('click', function() {
            showComponentDetails(this);
        });
        
        component.style.cursor = 'pointer';
        component.title = '点击查看组件详情';
    });
}

/**
 * 高亮相关组件
 */
function highlightRelatedComponents(component) {
    const componentName = component.querySelector('h4').textContent;
    const layer = component.closest('.tech-layer');
    
    // 根据组件类型高亮相关组件
    const relatedComponents = getRelatedComponents(componentName, layer);
    
    relatedComponents.forEach(related => {
        related.style.border = '2px solid #667eea';
        related.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%)';
    });
}

/**
 * 获取相关组件
 */
function getRelatedComponents(componentName, currentLayer) {
    const allComponents = document.querySelectorAll('.component');
    const related = [];
    
    // 简单的关联逻辑示例
    const relationships = {
        'Web管理平台': ['用户权限服务', '工单管理服务'],
        '移动端应用': ['消息通知服务', '文件存储'],
        '工单管理服务': ['关系型数据库', '缓存数据库'],
        '用户权限服务': ['关系型数据库', '缓存数据库'],
        '语音识别引擎': ['自然语言处理', '智能知识库'],
        '自然语言处理': ['智能分派引擎', '智能知识库']
    };
    
    const relatedNames = relationships[componentName] || [];
    
    allComponents.forEach(comp => {
        const compName = comp.querySelector('h4').textContent;
        if (relatedNames.includes(compName)) {
            related.push(comp);
        }
    });
    
    return related;
}

/**
 * 清除组件高亮
 */
function clearComponentHighlights() {
    const components = document.querySelectorAll('.component');
    components.forEach(comp => {
        comp.style.border = '';
        comp.style.background = '';
    });
}

/**
 * 显示组件详情
 */
function showComponentDetails(component) {
    const componentName = component.querySelector('h4').textContent;
    const componentDesc = component.querySelector('p').textContent;
    const layerName = component.closest('.tech-layer').querySelector('h3').textContent;
    
    // 创建详情模态框
    const modal = document.createElement('div');
    modal.className = 'component-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${componentName}</h3>
                <span class="layer-badge">${layerName}</span>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="component-description">
                    <h4>组件描述</h4>
                    <p>${componentDesc}</p>
                </div>
                <div class="component-features">
                    <h4>主要功能</h4>
                    <ul>
                        ${generateComponentFeatures(componentName)}
                    </ul>
                </div>
                <div class="component-tech">
                    <h4>技术栈</h4>
                    <div class="tech-tags">
                        ${generateTechStack(componentName)}
                    </div>
                </div>
                <div class="component-metrics">
                    <h4>性能指标</h4>
                    <div class="metrics-grid">
                        ${generatePerformanceMetrics(componentName)}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 绑定关闭事件
    const closeBtn = modal.querySelector('.close-btn');
    closeBtn.addEventListener('click', () => modal.remove());
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
    
    addComponentModalStyles();
}

/**
 * 生成组件功能
 */
function generateComponentFeatures(componentName) {
    const features = {
        'Web管理平台': [
            '工单管理和流转控制',
            '数据统计和报表分析',
            '用户权限和角色管理',
            '系统配置和参数设置'
        ],
        '移动端应用': [
            '现场工单处理',
            '语音录入和图片上传',
            'GPS定位和轨迹记录',
            '离线数据同步'
        ],
        '工单管理服务': [
            '工单生命周期管理',
            '流转规则引擎',
            'SLA监控和预警',
            '工单状态同步'
        ],
        '语音识别引擎': [
            '实时语音转文字',
            '多方言支持',
            '噪音过滤和增强',
            '语音质量评估'
        ],
        '关系型数据库': [
            '结构化数据存储',
            '事务一致性保证',
            '数据备份和恢复',
            '读写分离优化'
        ]
    };
    
    const defaultFeatures = [
        '核心业务功能',
        '高性能处理',
        '稳定可靠运行',
        '扩展性支持'
    ];
    
    const componentFeatures = features[componentName] || defaultFeatures;
    return componentFeatures.map(feature => `<li>${feature}</li>`).join('');
}

/**
 * 生成技术栈
 */
function generateTechStack(componentName) {
    const techStacks = {
        'Web管理平台': ['Vue.js', 'Element UI', 'Axios', 'Webpack'],
        '移动端应用': ['React Native', 'Redux', 'SQLite', 'WebRTC'],
        '工单管理服务': ['Spring Boot', 'MyBatis', 'Redis', 'RabbitMQ'],
        '语音识别引擎': ['Python', 'TensorFlow', 'WebSocket', 'FFmpeg'],
        '关系型数据库': ['MySQL 8.0', 'InnoDB', 'Binlog', 'MHA'],
        '缓存数据库': ['Redis Cluster', 'Sentinel', 'Lua Script', 'Pipeline'],
        '搜索引擎': ['Elasticsearch', 'Kibana', 'Logstash', 'IK分词器']
    };
    
    const defaultTech = ['Java', 'Spring', 'MySQL', 'Redis'];
    const componentTech = techStacks[componentName] || defaultTech;
    
    return componentTech.map(tech => `<span class="tech-tag">${tech}</span>`).join('');
}

/**
 * 生成性能指标
 */
function generatePerformanceMetrics(componentName) {
    const metrics = {
        'Web管理平台': [
            { label: '并发用户', value: '1000+', unit: '用户' },
            { label: '响应时间', value: '<200', unit: 'ms' },
            { label: '可用性', value: '99.9', unit: '%' }
        ],
        '移动端应用': [
            { label: '启动时间', value: '<3', unit: '秒' },
            { label: '内存占用', value: '<100', unit: 'MB' },
            { label: '电池优化', value: '95', unit: '%' }
        ],
        '工单管理服务': [
            { label: '处理能力', value: '10000', unit: 'TPS' },
            { label: '响应时间', value: '<50', unit: 'ms' },
            { label: '数据一致性', value: '100', unit: '%' }
        ],
        '语音识别引擎': [
            { label: '识别准确率', value: '95', unit: '%' },
            { label: '实时性', value: '<500', unit: 'ms' },
            { label: '支持语言', value: '10+', unit: '种' }
        ]
    };
    
    const defaultMetrics = [
        { label: '性能', value: '优秀', unit: '' },
        { label: '稳定性', value: '99.9', unit: '%' },
        { label: '扩展性', value: '良好', unit: '' }
    ];
    
    const componentMetrics = metrics[componentName] || defaultMetrics;
    
    return componentMetrics.map(metric => `
        <div class="metric-item">
            <div class="metric-value">${metric.value}<span class="metric-unit">${metric.unit}</span></div>
            <div class="metric-label">${metric.label}</div>
        </div>
    `).join('');
}

/**
 * 添加组件模态框样式
 */
function addComponentModalStyles() {
    if (document.querySelector('#component-modal-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'component-modal-styles';
    style.textContent = `
        .component-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }
        
        .component-modal .modal-content {
            background: white;
            border-radius: 15px;
            max-width: 700px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideInUp 0.3s ease;
        }
        
        .component-modal .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .layer-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .component-modal .modal-body {
            padding: 1.5rem;
        }
        
        .component-modal .modal-body h4 {
            margin: 1.5rem 0 1rem;
            color: #333;
            font-size: 1.1rem;
        }
        
        .component-modal .modal-body h4:first-child {
            margin-top: 0;
        }
        
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .tech-tag {
            background: #f8f9fa;
            color: #667eea;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            border: 1px solid #e9ecef;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }
        
        .metric-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .metric-unit {
            font-size: 0.8rem;
            color: #666;
            margin-left: 0.25rem;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 初始化组织架构交互
 */
function initOrgStructureInteractions() {
    const orgLevels = document.querySelectorAll('.org-level');
    
    orgLevels.forEach(level => {
        level.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.15)';
            
            // 高亮层级关系
            highlightLevelRelationships(this);
        });
        
        level.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
            
            clearLevelHighlights();
        });
    });
}

/**
 * 高亮层级关系
 */
function highlightLevelRelationships(currentLevel) {
    const levelNumber = Array.from(currentLevel.parentNode.children).indexOf(currentLevel);
    const allLevels = document.querySelectorAll('.org-level');
    
    allLevels.forEach((level, index) => {
        if (Math.abs(index - levelNumber) === 1) {
            level.style.border = '2px solid #667eea';
            level.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #f8f9fa 100%)';
        }
    });
}

/**
 * 清除层级高亮
 */
function clearLevelHighlights() {
    const orgLevels = document.querySelectorAll('.org-level');
    orgLevels.forEach(level => {
        level.style.border = '';
        level.style.background = '';
    });
}

/**
 * 初始化特性展示
 */
function initFeatureShowcase() {
    const featureCards = document.querySelectorAll('.feature-card');
    
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 15px 40px rgba(0,0,0,0.15)';
            
            // 动画化特性列表
            const listItems = this.querySelectorAll('li');
            listItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.transform = 'translateX(5px)';
                    item.style.color = '#667eea';
                }, index * 100);
            });
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
            
            const listItems = this.querySelectorAll('li');
            listItems.forEach(item => {
                item.style.transform = '';
                item.style.color = '';
            });
        });
    });
}

/**
 * 初始化架构导航
 */
function initArchitectureNavigation() {
    createArchitectureNavigation();
    bindNavigationEvents();
}

/**
 * 创建架构导航
 */
function createArchitectureNavigation() {
    const architectureSections = document.querySelector('.architecture-sections');
    
    if (!architectureSections) return;
    
    const navContainer = document.createElement('div');
    navContainer.className = 'architecture-navigation';
    navContainer.innerHTML = `
        <div class="nav-container">
            <h3>架构导航</h3>
            <div class="nav-items">
                <a href="#tech-architecture" class="nav-item" data-section="tech">
                    <i class="fas fa-server"></i>
                    <span>技术架构</span>
                </a>
                <a href="#org-architecture" class="nav-item" data-section="org">
                    <i class="fas fa-sitemap"></i>
                    <span>组织架构</span>
                </a>
                <a href="#system-features" class="nav-item" data-section="features">
                    <i class="fas fa-star"></i>
                    <span>系统特性</span>
                </a>
            </div>
        </div>
    `;
    
    architectureSections.insertBefore(navContainer, architectureSections.firstChild);
    
    // 为架构部分添加ID
    const sections = document.querySelectorAll('.architecture-section');
    const sectionIds = ['tech-architecture', 'org-architecture', 'system-features'];
    sections.forEach((section, index) => {
        if (sectionIds[index]) {
            section.id = sectionIds[index];
        }
    });
    
    addArchitectureNavStyles();
}

/**
 * 添加架构导航样式
 */
function addArchitectureNavStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .architecture-navigation {
            position: sticky;
            top: 100px;
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            z-index: 100;
        }
        
        .architecture-navigation .nav-container h3 {
            margin-bottom: 1rem;
            color: #333;
            font-size: 1.1rem;
            text-align: center;
        }
        
        .architecture-navigation .nav-items {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .architecture-navigation .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 10px;
            text-decoration: none;
            color: #666;
            background: #f8f9fa;
            transition: all 0.3s ease;
            min-width: 100px;
        }
        
        .architecture-navigation .nav-item:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .architecture-navigation .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .architecture-navigation .nav-item i {
            font-size: 1.2rem;
        }
        
        .architecture-navigation .nav-item span {
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .architecture-navigation {
                position: relative;
                top: auto;
            }
            
            .architecture-navigation .nav-items {
                flex-direction: column;
                align-items: center;
            }
            
            .architecture-navigation .nav-item {
                width: 200px;
                flex-direction: row;
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 绑定导航事件
 */
function bindNavigationEvents() {
    const navItems = document.querySelectorAll('.architecture-navigation .nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // 更新导航状态
                navItems.forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
                
                // 平滑滚动到目标
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // 高亮目标部分
                highlightSection(targetElement);
            }
        });
    });
    
    // 监听滚动更新导航状态
    window.addEventListener('scroll', throttle(updateArchitectureNavState, 100));
}

/**
 * 更新架构导航状态
 */
function updateArchitectureNavState() {
    const sections = document.querySelectorAll('.architecture-section');
    const navItems = document.querySelectorAll('.architecture-navigation .nav-item');
    
    let currentSection = null;
    
    sections.forEach((section, index) => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= 200 && rect.bottom >= 200) {
            currentSection = index;
        }
    });
    
    if (currentSection !== null) {
        navItems.forEach(item => item.classList.remove('active'));
        if (navItems[currentSection]) {
            navItems[currentSection].classList.add('active');
        }
    }
}

/**
 * 高亮部分
 */
function highlightSection(sectionElement) {
    // 移除其他部分的高亮
    document.querySelectorAll('.architecture-section').forEach(section => {
        section.classList.remove('highlighted');
    });
    
    // 添加当前部分高亮
    sectionElement.classList.add('highlighted');
    
    // 3秒后移除高亮
    setTimeout(() => {
        sectionElement.classList.remove('highlighted');
    }, 3000);
    
    // 添加高亮样式
    if (!document.querySelector('#section-highlight-styles')) {
        const style = document.createElement('style');
        style.id = 'section-highlight-styles';
        style.textContent = `
            .architecture-section.highlighted {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
                transform: scale(1.01);
                transition: all 0.5s ease;
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 初始化统计动画
 */
function initStatsAnimation() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const finalValue = target.textContent;
                
                if (!isNaN(finalValue)) {
                    animateNumber(target, 0, parseInt(finalValue), 1500);
                }
                
                observer.unobserve(target);
            }
        });
    });
    
    statNumbers.forEach(stat => {
        observer.observe(stat);
    });
}

/**
 * 数字动画函数
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const easeOutCubic = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(start + (end - start) * easeOutCubic);
        
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        } else {
            element.textContent = end;
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
