/* 组件样式 */

/* 处理模式标识 */
.mode-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1.2;
}

.mode-badge.instant {
    background-color: rgba(255, 215, 0, 0.1);
    color: #FA8C16;
}

.mode-badge.instant .icon {
    color: #FAAD14;
}

.mode-badge.normal {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

.mode-badge.collaborative {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
}

/* 督办标识 */
.supervision-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1.2;
}

.supervision-badge.city {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
}

.supervision-badge.district {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
}

.supervision-badge.street {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

/* 时间倒计时 */
.time-countdown {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.time-countdown.normal {
    color: var(--success-color);
}

.time-countdown.warning {
    color: var(--warning-color);
}

.time-countdown.danger {
    color: var(--error-color);
    animation: blink 1s infinite;
}

.time-countdown.overtime {
    color: var(--error-color);
    background-color: rgba(255, 77, 79, 0.1);
    padding: 2px var(--spacing-xs);
    border-radius: var(--border-radius-sm);
}

/* 市民信息脱敏显示 */
.citizen-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.citizen-name {
    font-weight: 500;
    color: var(--text-primary);
}

.citizen-phone {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-family: monospace;
}

.citizen-type {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.vip-badge {
    background-color: rgba(255, 215, 0, 0.2);
    color: #FA8C16;
    padding: 1px 4px;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

/* 问题描述 */
.problem-desc {
    max-width: 200px;
    line-height: 1.4;
}

.problem-category {
    display: inline-block;
    padding: 1px 4px;
    background-color: var(--bg-color);
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    margin-bottom: 2px;
}

/* 当前环节显示 */
.current-stage {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.stage-name {
    font-weight: 500;
    color: var(--text-primary);
}

.stage-handler {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.stage-duration {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.action-btn {
    padding: 2px var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-white);
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.action-btn.primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.action-btn.success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.action-btn.warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.action-btn.danger {
    background-color: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

/* 下拉菜单 */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-white);
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    cursor: pointer;
}

.dropdown-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 120px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-fast);
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-align: left;
    cursor: pointer;
    transition: var(--transition-fast);
}

.dropdown-item:hover {
    background-color: var(--bg-color);
    color: var(--primary-color);
}

.dropdown-item:first-child {
    border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 var(--border-radius-base) var(--border-radius-base);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: var(--spacing-xs) 0;
}

/* 标签组 */
.tag-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 1px var(--spacing-xs);
    background-color: var(--bg-color);
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-light);
}

.tag.primary {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
    border-color: rgba(24, 144, 255, 0.2);
}

.tag.success {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
    border-color: rgba(82, 196, 26, 0.2);
}

.tag.warning {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
    border-color: rgba(250, 173, 20, 0.2);
}

.tag.danger {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
    border-color: rgba(255, 77, 79, 0.2);
}

/* 进度条 */
.progress {
    width: 100%;
    height: 4px;
    background-color: var(--border-light);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-bar.success {
    background-color: var(--success-color);
}

.progress-bar.warning {
    background-color: var(--warning-color);
}

.progress-bar.danger {
    background-color: var(--error-color);
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
    text-align: center;
}

.empty-state .icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state .title {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.empty-state .description {
    font-size: var(--font-size-sm);
    line-height: 1.5;
    max-width: 400px;
}

/* 加载状态 */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
}

/* 通知消息 */
.notification {
    position: fixed;
    top: 80px;
    right: var(--spacing-lg);
    min-width: 300px;
    max-width: 400px;
    padding: var(--spacing-md);
    background-color: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    box-shadow: var(--shadow-lg);
    z-index: 2000;
    transform: translateX(100%);
    transition: var(--transition-base);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.notification-title {
    font-weight: 500;
    color: var(--text-primary);
}

.notification-close {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
}

.notification-close:hover {
    background-color: var(--bg-color);
    color: var(--text-primary);
}

.notification-content {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 工具提示组件 */
.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-family: var(--font-family);
    font-size: var(--font-size-xs);
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    opacity: 0;
    transition: opacity 0.15s;
}

.tooltip.show {
    opacity: 0.9;
}

.tooltip-arrow {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.tooltip-content {
    max-width: 200px;
    padding: 4px 8px;
    color: #fff;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: var(--border-radius-sm);
}

/* 加载器组件 */
.loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.loader.small .loader-spinner {
    width: 16px;
    height: 16px;
    border-width: 2px;
}

.loader.medium .loader-spinner {
    width: 24px;
    height: 24px;
    border-width: 3px;
}

.loader.large .loader-spinner {
    width: 32px;
    height: 32px;
    border-width: 4px;
}

.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
}

.loader-spinner {
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loader-text {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* 标签页组件 */
.tabs {
    display: flex;
    flex-direction: column;
}

.tabs-nav {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-white);
}

.tab-button {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tabs-content {
    flex: 1;
}

.tab-pane {
    display: none;
    padding: var(--spacing-md);
}

.tab-pane.active {
    display: block;
}

/* 面包屑导航 */
.breadcrumb {
    padding: var(--spacing-sm) 0;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 var(--spacing-sm);
    color: var(--text-disabled);
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-primary);
}

/* 徽章组件 */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-sm);
}

.badge-primary {
    color: #fff;
    background-color: var(--primary-color);
}

.badge-secondary {
    color: #fff;
    background-color: var(--text-secondary);
}

.badge-success {
    color: #fff;
    background-color: var(--success-color);
}

.badge-warning {
    color: #212529;
    background-color: var(--warning-color);
}

.badge-danger {
    color: #fff;
    background-color: var(--error-color);
}

.badge-info {
    color: #fff;
    background-color: var(--info-color);
}

.badge-pill {
    padding-right: 0.6em;
    padding-left: 0.6em;
    border-radius: 10rem;
}

/* 卡片组件 */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--bg-white);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
}

.card-header {
    padding: var(--spacing-md);
    margin-bottom: 0;
    background-color: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

.card-title {
    margin-bottom: 0;
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--text-primary);
}

.card-body {
    flex: 1 1 auto;
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    background-color: var(--bg-color);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius-base) var(--border-radius-base);
}

/* 进度条组件增强 */
.progress-bar.bg-primary {
    background-color: var(--primary-color);
}

.progress-bar.bg-success {
    background-color: var(--success-color);
}

.progress-bar.bg-warning {
    background-color: var(--warning-color);
}

.progress-bar.bg-danger {
    background-color: var(--error-color);
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem;
    }
}