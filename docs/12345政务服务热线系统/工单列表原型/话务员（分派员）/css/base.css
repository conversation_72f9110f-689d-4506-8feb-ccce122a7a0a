/* 基础样式和CSS变量 */
:root {
    /* 主色调 */
    --primary-color: #1890FF;
    --primary-hover: #40A9FF;
    --primary-active: #096DD9;
    
    /* 辅助色 */
    --success-color: #52C41A;
    --warning-color: #FAAD14;
    --error-color: #FF4D4F;
    --info-color: #1890FF;
    
    /* 中性色 */
    --text-primary: #262626;
    --text-secondary: #595959;
    --text-disabled: #BFBFBF;
    --border-color: #D9D9D9;
    --border-light: #F0F0F0;
    --bg-color: #FAFAFA;
    --bg-white: #FFFFFF;
    
    /* 状态色 */
    --status-draft: #8C8C8C;
    --status-pending: #FA8C16;
    --status-processing: #1890FF;
    --status-reviewing: #722ED1;
    --status-callback: #13C2C2;
    --status-closed: #52C41A;
    --status-suspended: #FAAD14;
    --status-cancelled: #FF4D4F;
    
    /* 紧急程度色 */
    --urgent-critical: #FF4D4F;
    --urgent-high: #FAAD14;
    --urgent-normal: #52C41A;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角 */
    --border-radius-sm: 2px;
    --border-radius-base: 6px;
    --border-radius-lg: 8px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.03);
    --shadow-base: 0 1px 6px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    /* 动画 */
    --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    --transition-fast: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 14px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background-color: var(--bg-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 基础元素样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
}

button {
    font-family: inherit;
    font-size: inherit;
    border: none;
    background: none;
    cursor: pointer;
    outline: none;
    transition: var(--transition-fast);
}

input, select, textarea {
    font-family: inherit;
    font-size: inherit;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    padding: var(--spacing-sm) var(--spacing-md);
    outline: none;
    transition: var(--transition-fast);
}

input:focus, select:focus, textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-sm);
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    cursor: pointer;
    user-select: none;
    transition: var(--transition-fast);
    min-height: 32px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-primary:active {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
}

.btn-secondary {
    background-color: white;
    border-color: var(--border-color);
    color: var(--text-primary);
}

.btn-secondary:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-base);
    background-color: transparent;
    color: var(--text-secondary);
    position: relative;
}

.btn-icon:hover {
    background-color: var(--border-light);
    color: var(--text-primary);
}

/* 徽章样式 */
.badge {
    position: absolute;
    top: -2px;
    right: -2px;
    min-width: 16px;
    height: 16px;
    padding: 0 4px;
    background-color: var(--error-color);
    color: white;
    font-size: 10px;
    line-height: 16px;
    text-align: center;
    border-radius: 8px;
    font-weight: 500;
}

/* 状态标签样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1.2;
}

.status-badge.draft {
    background-color: rgba(140, 140, 140, 0.1);
    color: var(--status-draft);
}

.status-badge.pending {
    background-color: rgba(250, 140, 22, 0.1);
    color: var(--status-pending);
}

.status-badge.processing {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--status-processing);
}

.status-badge.reviewing {
    background-color: rgba(114, 46, 209, 0.1);
    color: var(--status-reviewing);
}

.status-badge.callback {
    background-color: rgba(19, 194, 194, 0.1);
    color: var(--status-callback);
}

.status-badge.closed {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--status-closed);
}

.status-badge.suspended {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--status-suspended);
}

.status-badge.cancelled {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--status-cancelled);
}

/* 紧急程度标签 */
.urgency-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 2px var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1.2;
}

.urgency-badge.urgent {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--urgent-critical);
    animation: blink 1s infinite;
}

.urgency-badge.high {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--urgent-high);
}

.urgency-badge.normal {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--urgent-normal);
}

/* 闪烁动画 */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition-fast);
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 加载状态 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden !important;
}

/* 文本截断 */
.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 响应式辅助类 */
@media (max-width: 768px) {
    .hidden-mobile {
        display: none !important;
    }
}

@media (min-width: 769px) {
    .hidden-desktop {
        display: none !important;
    }
}