/* 表格样式 */

.ticket-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-white);
    font-size: var(--font-size-sm);
}

/* 表头样式 */
.ticket-table thead {
    background-color: var(--bg-color);
    border-bottom: 2px solid var(--border-color);
}

.ticket-table th {
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
    position: relative;
    user-select: none;
}

.ticket-table th.sortable {
    cursor: pointer;
    transition: var(--transition-fast);
}

.ticket-table th.sortable:hover {
    background-color: var(--border-light);
}

.ticket-table th.sortable.asc .sort-icon::after {
    content: '↑';
    color: var(--primary-color);
}

.ticket-table th.sortable.desc .sort-icon::after {
    content: '↓';
    color: var(--primary-color);
}

.sort-icon {
    margin-left: var(--spacing-xs);
    color: var(--text-disabled);
    font-size: var(--font-size-xs);
}

/* 列宽设置 */
.checkbox-col {
    width: 40px;
    text-align: center;
}

.actions-col {
    width: 160px;
    text-align: center;
}

/* 表格行样式 */
.ticket-table tbody tr {
    border-bottom: 1px solid var(--border-light);
    transition: var(--transition-fast);
}

.ticket-table tbody tr:hover {
    background-color: rgba(24, 144, 255, 0.02);
}

.ticket-table tbody tr.selected {
    background-color: rgba(24, 144, 255, 0.05);
}

.ticket-table tbody tr.urgent {
    background-color: rgba(255, 77, 79, 0.02);
    border-left: 3px solid var(--error-color);
}

.ticket-table tbody tr.high-priority {
    background-color: rgba(250, 173, 20, 0.02);
    border-left: 3px solid var(--warning-color);
}

/* 表格单元格样式 */
.ticket-table td {
    padding: var(--spacing-md);
    vertical-align: top;
    border-bottom: 1px solid var(--border-light);
    line-height: 1.4;
}

.ticket-table td.checkbox-col {
    text-align: center;
    vertical-align: middle;
}

.ticket-table td.actions-col {
    text-align: center;
    vertical-align: middle;
}

/* 工单编号列 */
.ticket-no {
    font-family: monospace;
    font-weight: 500;
    color: var(--primary-color);
    cursor: pointer;
    text-decoration: none;
}

.ticket-no:hover {
    text-decoration: underline;
}

/* 状态列样式 */
.status-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-icon.draft {
    background-color: var(--status-draft);
}

.status-icon.pending {
    background-color: var(--status-pending);
    animation: pulse 2s infinite;
}

.status-icon.processing {
    background-color: var(--status-processing);
    animation: spin 2s linear infinite;
}

.status-icon.reviewing {
    background-color: var(--status-reviewing);
}

.status-icon.callback {
    background-color: var(--status-callback);
}

.status-icon.closed {
    background-color: var(--status-closed);
}

.status-icon.suspended {
    background-color: var(--status-suspended);
    animation: blink 1s infinite;
}

.status-icon.cancelled {
    background-color: var(--status-cancelled);
}

/* 紧急程度列样式 */
.urgency-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.urgency-icon {
    font-size: var(--font-size-sm);
}

.urgency-icon.urgent {
    color: var(--urgent-critical);
    animation: blink 1s infinite;
}

.urgency-icon.high {
    color: var(--urgent-high);
}

.urgency-icon.normal {
    color: var(--urgent-normal);
}

/* 市民信息列样式 */
.citizen-cell {
    min-width: 120px;
}

.citizen-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.citizen-phone {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-family: monospace;
}

.citizen-tags {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: 2px;
}

/* 问题描述列样式 */
.problem-cell {
    max-width: 250px;
}

.problem-category {
    display: inline-block;
    padding: 1px 4px;
    background-color: var(--bg-color);
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    margin-bottom: 4px;
}

.problem-content {
    line-height: 1.4;
    color: var(--text-primary);
}

/* 处理模式列样式 */
.mode-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.mode-icon {
    font-size: var(--font-size-sm);
}

.mode-icon.instant {
    color: #FAAD14;
}

.mode-icon.normal {
    color: var(--primary-color);
}

.mode-icon.collaborative {
    color: var(--success-color);
}

/* 时间列样式 */
.time-cell {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    white-space: nowrap;
}

.create-time {
    margin-bottom: 2px;
}

.relative-time {
    color: var(--text-disabled);
}

/* 剩余时间列样式 */
.remaining-time {
    font-weight: 500;
    white-space: nowrap;
}

.remaining-time.normal {
    color: var(--success-color);
}

.remaining-time.warning {
    color: var(--warning-color);
}

.remaining-time.danger {
    color: var(--error-color);
    animation: blink 1s infinite;
}

.remaining-time.overtime {
    color: var(--error-color);
    background-color: rgba(255, 77, 79, 0.1);
    padding: 2px 4px;
    border-radius: var(--border-radius-sm);
}

/* 当前环节列样式 */
.stage-cell {
    min-width: 100px;
}

.stage-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.stage-handler {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.stage-duration {
    font-size: var(--font-size-xs);
    color: var(--text-disabled);
}

/* 操作按钮列样式 */
.actions-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.action-btn-sm {
    padding: 2px 6px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-white);
    color: var(--text-primary);
    font-size: 11px;
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
    min-width: auto;
    height: 22px;
    text-align: center;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-btn-sm:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(24, 144, 255, 0.05);
}

.action-btn-sm.primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.action-btn-sm.primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.action-btn-sm.success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.action-btn-sm.warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.action-btn-sm.danger {
    background-color: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

/* 表格复选框样式 */
.table-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* 表格空状态 */
.table-empty {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.table-empty .icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.table-empty .title {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.table-empty .description {
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* 表格加载状态 */
.table-loading {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.table-loading .spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-sm);
}

/* 表格行展开 */
.expandable-row {
    cursor: pointer;
}

.expand-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    text-align: center;
    transition: var(--transition-fast);
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

.expanded-content {
    background-color: var(--bg-color);
    border-top: 1px solid var(--border-light);
}

.expanded-content td {
    padding: var(--spacing-lg);
}

/* 表格固定列 */
.table-container.fixed-columns .ticket-table {
    table-layout: fixed;
}

.table-container.fixed-columns .ticket-table th:first-child,
.table-container.fixed-columns .ticket-table td:first-child {
    position: sticky;
    left: 0;
    background-color: var(--bg-white);
    z-index: 10;
}

.table-container.fixed-columns .ticket-table th:last-child,
.table-container.fixed-columns .ticket-table td:last-child {
    position: sticky;
    right: 0;
    background-color: var(--bg-white);
    z-index: 10;
}

/* 响应式表格 */
@media (max-width: 1200px) {
    .ticket-table .hidden-lg {
        display: none;
    }
}

@media (max-width: 992px) {
    .ticket-table .hidden-md {
        display: none;
    }
}

@media (max-width: 768px) {
    .ticket-table .hidden-sm {
        display: none;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: var(--spacing-sm);
    }
    
    .actions-cell {
        flex-direction: column;
        gap: 2px;
    }
    
    .action-btn-sm {
        width: 100%;
        min-width: auto;
    }
}