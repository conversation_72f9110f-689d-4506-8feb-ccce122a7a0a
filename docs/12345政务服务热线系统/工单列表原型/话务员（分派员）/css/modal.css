/* 模态框样式 */

/* 模态框遮罩 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框容器 */
.modal {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: var(--transition-base);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

/* 模态框尺寸 */
.modal.small {
    width: 400px;
}

.modal.medium {
    width: 600px;
}

.modal.large {
    width: 800px;
}

.modal.extra-large {
    width: 1200px;
}

/* 模态框头部 */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-white);
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-base);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--bg-color);
    color: var(--text-primary);
}

/* 模态框内容 */
.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: calc(90vh - 140px);
}

/* 模态框底部 */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-color);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-label.required::after {
    content: '*';
    color: var(--error-color);
    margin-left: 4px;
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-control.error {
    border-color: var(--error-color);
}

.form-control.error:focus {
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.form-text {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.form-error {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--error-color);
}

/* 表单行布局 */
.form-row {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.form-col {
    flex: 1;
}

.form-col-auto {
    flex: none;
}

/* 选择器样式 */
.form-select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-sm);
    background-color: var(--bg-white);
    cursor: pointer;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 多选框和单选框 */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.form-check-label {
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

/* 文件上传 */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.file-upload-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius-base);
    background-color: var(--bg-color);
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.file-upload:hover .file-upload-button {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.file-list {
    margin-top: var(--spacing-sm);
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-base);
    margin-bottom: var(--spacing-xs);
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.file-name {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.file-size {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.file-remove {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
}

.file-remove:hover {
    background-color: var(--error-color);
    color: white;
}

/* 确认对话框 */
.confirm-dialog {
    text-align: center;
}

.confirm-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-lg);
}

.confirm-icon.warning {
    color: var(--warning-color);
}

.confirm-icon.danger {
    color: var(--error-color);
}

.confirm-icon.info {
    color: var(--info-color);
}

.confirm-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.confirm-message {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-lg);
}

/* 加载对话框 */
.loading-dialog {
    text-align: center;
    padding: var(--spacing-xl);
}

.loading-spinner-large {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

.loading-text {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

/* 步骤条 */
.steps {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xl);
}

.step {
    display: flex;
    align-items: center;
    flex: 1;
    position: relative;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 100%;
    height: 1px;
    background-color: var(--border-color);
    z-index: 1;
}

.step.completed::after {
    background-color: var(--success-color);
}

.step-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--border-light);
    color: var(--text-disabled);
    font-weight: 500;
    z-index: 2;
    position: relative;
}

.step.active .step-indicator {
    background-color: var(--primary-color);
    color: white;
}

.step.completed .step-indicator {
    background-color: var(--success-color);
    color: white;
}

.step-title {
    margin-left: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.step.active .step-title {
    color: var(--primary-color);
    font-weight: 500;
}

.step.completed .step-title {
    color: var(--success-color);
}

/* 标签页 */
.tabs {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.tab-nav {
    display: flex;
    gap: var(--spacing-lg);
}

.tab-item {
    padding: var(--spacing-md) 0;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
}

.tab-item:hover {
    color: var(--text-primary);
}

.tab-item.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}