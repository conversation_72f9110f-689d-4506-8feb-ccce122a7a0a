/* 响应式样式 */

/* 大屏幕 (≥1200px) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
    
    .sidebar {
        width: 260px;
    }
    
    .search-box input {
        width: 350px;
    }
}

/* 中等屏幕 (992px - 1199px) */
@media (max-width: 1199px) {
    .header {
        padding: 0 var(--spacing-md);
    }
    
    .toolbar {
        padding: var(--spacing-md);
    }
    
    .filter-bar {
        padding: var(--spacing-md);
    }
    
    .stats-bar {
        padding: var(--spacing-md);
        gap: var(--spacing-lg);
    }
    
    .pagination {
        padding: var(--spacing-md);
    }
    
    /* 隐藏部分列 */
    .ticket-table .hidden-lg {
        display: none;
    }
    
    .search-box input {
        width: 280px;
    }
}

/* 小屏幕 (768px - 991px) */
@media (max-width: 991px) {
    .header-center {
        display: none;
    }
    
    .sidebar {
        width: 200px;
    }
    
    .toolbar {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }
    
    .filter-bar {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .filter-group select,
    .filter-group input {
        min-width: 150px;
    }
    
    .stats-bar {
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    
    .stat-item {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    /* 隐藏更多列 */
    .ticket-table .hidden-md {
        display: none;
    }
    
    .search-box input {
        width: 250px;
    }
    
    /* 模态框调整 */
    .modal.large,
    .modal.extra-large {
        width: 95vw;
    }
}

/* 移动设备 (≤767px) */
@media (max-width: 767px) {
    body {
        font-size: var(--font-size-xs);
    }
    
    .header {
        height: 56px;
        padding: 0 var(--spacing-sm);
    }
    
    .logo span {
        display: none;
    }
    
    .header-actions {
        gap: var(--spacing-xs);
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        order: 2;
    }
    
    .nav-menu {
        display: flex;
        overflow-x: auto;
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .nav-item {
        flex-shrink: 0;
        margin-bottom: 0;
        padding: var(--spacing-sm);
        white-space: nowrap;
    }
    
    .content-area {
        order: 1;
    }
    
    .toolbar {
        padding: var(--spacing-sm);
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .toolbar-left,
    .toolbar-right {
        width: 100%;
        justify-content: space-between;
    }
    
    .batch-actions {
        padding-left: 0;
        border-left: none;
        border-top: 1px solid var(--border-color);
        padding-top: var(--spacing-sm);
        margin-top: var(--spacing-sm);
        width: 100%;
        justify-content: space-around;
    }
    
    .search-box {
        width: 100%;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .filter-bar {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }
    
    .filter-group {
        width: 100%;
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .filter-group label {
        font-weight: 500;
    }
    
    .filter-group select,
    .filter-group input {
        width: 100%;
        min-width: auto;
    }
    
    .stats-bar {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
        justify-content: space-around;
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: 2px;
    }
    
    .stat-label {
        font-size: var(--font-size-xs);
    }
    
    .stat-value {
        font-size: var(--font-size-sm);
    }
    
    /* 表格移动端优化 */
    .table-container {
        overflow-x: auto;
    }
    
    .ticket-table {
        min-width: 800px;
    }
    
    .ticket-table .hidden-sm {
        display: none;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: var(--spacing-xs);
        font-size: var(--font-size-xs);
    }
    
    .actions-cell {
        flex-direction: column;
        gap: 2px;
    }
    
    .action-btn-sm {
        width: 100%;
        min-width: auto;
        font-size: var(--font-size-xs);
        padding: 2px 4px;
    }
    
    .pagination {
        padding: var(--spacing-sm);
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .pagination-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-page {
        min-width: 28px;
        height: 28px;
        font-size: var(--font-size-xs);
    }
    
    #pageSizeSelect {
        width: 80px;
        height: 28px;
        font-size: var(--font-size-xs);
    }
    
    /* 模态框移动端优化 */
    .modal {
        width: 95vw;
        max-height: 95vh;
        margin: 0;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
    
    .modal-title {
        font-size: var(--font-size-base);
    }
    
    .modal-footer {
        flex-direction: column-reverse;
        gap: var(--spacing-sm);
    }
    
    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
    
    /* 表单移动端优化 */
    .form-row {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .form-control,
    .form-select {
        font-size: var(--font-size-sm);
    }
    
    /* 按钮移动端优化 */
    .btn {
        min-height: 36px;
        font-size: var(--font-size-sm);
    }
    
    .btn-icon {
        width: 36px;
        height: 36px;
    }
}

/* 超小屏幕 (≤480px) */
@media (max-width: 480px) {
    .header {
        height: 48px;
    }
    
    .logo img {
        width: 24px;
        height: 24px;
    }
    
    .user-info {
        display: none;
    }
    
    .toolbar-left,
    .toolbar-right {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .batch-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .stats-bar {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        padding: var(--spacing-xs);
        background-color: var(--bg-color);
        border-radius: var(--border-radius-base);
    }
    
    .ticket-table {
        min-width: 600px;
    }
    
    .pagination-controls {
        gap: var(--spacing-xs);
    }
    
    .page-numbers {
        display: none;
    }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
    .header {
        height: 48px;
    }
    
    .sidebar {
        display: none;
    }
    
    .filter-bar,
    .stats-bar {
        display: none;
    }
    
    .modal {
        max-height: 95vh;
    }
    
    .modal-body {
        max-height: calc(95vh - 120px);
    }
}

/* 打印样式 */
@media print {
    .header,
    .sidebar,
    .toolbar,
    .filter-bar,
    .pagination,
    .modal-overlay {
        display: none !important;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .content-area {
        margin: 0;
        padding: 0;
    }
    
    .table-container {
        overflow: visible;
    }
    
    .ticket-table {
        font-size: 12px;
    }
    
    .ticket-table th,
    .ticket-table td {
        padding: 4px;
        border: 1px solid #000;
    }
    
    .actions-col {
        display: none;
    }
    
    .status-badge,
    .urgency-badge,
    .mode-badge {
        border: 1px solid #000;
        background: none !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --border-light: #666666;
        --text-secondary: #000000;
        --text-disabled: #666666;
    }
    
    .btn {
        border-width: 2px;
    }
    
    .status-badge,
    .urgency-badge,
    .mode-badge {
        border: 1px solid currentColor;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}