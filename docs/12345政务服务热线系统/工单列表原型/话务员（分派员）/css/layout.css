/* 布局样式 */

/* 页面整体布局 */
body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* 顶部导航栏 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    padding: 0 var(--spacing-lg);
    background-color: var(--bg-white);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Logo区域 */
.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.logo img {
    width: 32px;
    height: 32px;
}

/* 用户信息 */
.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-color);
    border-radius: var(--border-radius-lg);
}

.role-badge {
    padding: 2px var(--spacing-sm);
    background-color: var(--primary-color);
    color: white;
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
}

.dept-name {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

/* 头部操作按钮 */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 240px;
    background-color: var(--bg-white);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
    flex-shrink: 0;
}

/* 导航菜单 */
.nav-menu {
    padding: var(--spacing-md);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius-base);
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
}

.nav-item:hover {
    background-color: var(--bg-color);
}

.nav-item.active {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

.nav-item .icon {
    font-size: var(--font-size-base);
    width: 20px;
    text-align: center;
}

.nav-item .count {
    margin-left: auto;
    padding: 2px 6px;
    background-color: var(--border-light);
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.nav-item .count.urgent {
    background-color: var(--error-color);
    color: white;
    animation: pulse 2s infinite;
}

.nav-item .count.warning {
    background-color: var(--warning-color);
    color: white;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 内容区域 */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--bg-white);
}

/* 工具栏 */
.toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-white);
    flex-shrink: 0;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* 批量操作区域 */
.batch-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-left: var(--spacing-md);
    border-left: 1px solid var(--border-color);
}

/* 搜索框 */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    width: 300px;
    padding-right: 40px;
}

.btn-search {
    position: absolute;
    right: 8px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
}

.btn-search:hover {
    background-color: var(--bg-color);
    color: var(--text-primary);
}

/* 筛选栏 */
.filter-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
    flex-shrink: 0;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-group label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    white-space: nowrap;
}

.filter-group select,
.filter-group input {
    min-width: 120px;
}

/* 统计信息栏 */
.stats-bar {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-white);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.stat-value {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
}

.stat-value.urgent {
    color: var(--error-color);
}

.stat-value.warning {
    color: var(--warning-color);
}

.stat-value.success {
    color: var(--success-color);
}

/* 表格容器 */
.table-container {
    flex: 1;
    overflow: auto;
    background-color: var(--bg-white);
}

/* 分页器 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-white);
    flex-shrink: 0;
}

.pagination-info {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-page {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    background-color: var(--bg-white);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-page:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-page.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-numbers {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

#pageSizeSelect {
    width: 100px;
    height: 32px;
}