// 通用组件库

/**
 * 创建下拉菜单组件
 * @param {Object} options - 配置选项
 * @returns {HTMLElement} 下拉菜单元素
 */
function createDropdown(options = {}) {
    const config = {
        trigger: 'click', // click, hover
        placement: 'bottom-start', // top, bottom, left, right
        items: [],
        className: '',
        ...options
    };

    const dropdown = document.createElement('div');
    dropdown.className = `dropdown ${config.className}`;
    
    const menu = document.createElement('div');
    menu.className = 'dropdown-menu';
    menu.style.display = 'none';
    
    config.items.forEach(item => {
        const menuItem = document.createElement('div');
        menuItem.className = 'dropdown-item';
        
        if (item.divider) {
            menuItem.className = 'dropdown-divider';
        } else {
            menuItem.innerHTML = item.label;
            menuItem.addEventListener('click', () => {
                if (item.action) {
                    item.action();
                }
                hideDropdown(dropdown);
            });
        }
        
        menu.appendChild(menuItem);
    });
    
    dropdown.appendChild(menu);
    
    return dropdown;
}

/**
 * 显示下拉菜单
 * @param {HTMLElement} dropdown - 下拉菜单元素
 */
function showDropdown(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    if (menu) {
        menu.style.display = 'block';
        dropdown.classList.add('show');
    }
}

/**
 * 隐藏下拉菜单
 * @param {HTMLElement} dropdown - 下拉菜单元素
 */
function hideDropdown(dropdown) {
    const menu = dropdown.querySelector('.dropdown-menu');
    if (menu) {
        menu.style.display = 'none';
        dropdown.classList.remove('show');
    }
}

/**
 * 创建工具提示组件
 * @param {HTMLElement} element - 目标元素
 * @param {Object} options - 配置选项
 */
function createTooltip(element, options = {}) {
    const config = {
        content: '',
        placement: 'top',
        trigger: 'hover',
        delay: 0,
        ...options
    };

    let tooltip = null;
    let showTimer = null;
    let hideTimer = null;

    function showTooltip() {
        if (tooltip) return;

        tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-arrow"></div>
            <div class="tooltip-content">${config.content}</div>
        `;
        
        document.body.appendChild(tooltip);
        
        // 计算位置
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        let top, left;
        
        switch (config.placement) {
            case 'top':
                top = rect.top - tooltipRect.height - 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = rect.bottom + 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.left - tooltipRect.width - 8;
                break;
            case 'right':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.right + 8;
                break;
        }
        
        tooltip.style.top = `${top}px`;
        tooltip.style.left = `${left}px`;
        tooltip.classList.add('show');
    }

    function hideTooltip() {
        if (tooltip) {
            tooltip.classList.remove('show');
            setTimeout(() => {
                if (tooltip && tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
                tooltip = null;
            }, 200);
        }
    }

    if (config.trigger === 'hover') {
        element.addEventListener('mouseenter', () => {
            clearTimeout(hideTimer);
            showTimer = setTimeout(showTooltip, config.delay);
        });
        
        element.addEventListener('mouseleave', () => {
            clearTimeout(showTimer);
            hideTimer = setTimeout(hideTooltip, 100);
        });
    } else if (config.trigger === 'click') {
        element.addEventListener('click', () => {
            if (tooltip) {
                hideTooltip();
            } else {
                showTooltip();
            }
        });
    }
}

/**
 * 创建加载指示器
 * @param {Object} options - 配置选项
 * @returns {HTMLElement} 加载指示器元素
 */
function createLoader(options = {}) {
    const config = {
        size: 'medium', // small, medium, large
        text: '加载中...',
        overlay: false,
        ...options
    };

    const loader = document.createElement('div');
    loader.className = `loader ${config.size}`;
    
    if (config.overlay) {
        loader.classList.add('loader-overlay');
    }
    
    loader.innerHTML = `
        <div class="loader-spinner"></div>
        ${config.text ? `<div class="loader-text">${config.text}</div>` : ''}
    `;
    
    return loader;
}

/**
 * 创建进度条组件
 * @param {Object} options - 配置选项
 * @returns {HTMLElement} 进度条元素
 */
function createProgressBar(options = {}) {
    const config = {
        value: 0,
        max: 100,
        showText: true,
        animated: false,
        striped: false,
        variant: 'primary', // primary, success, warning, danger
        ...options
    };

    const progressBar = document.createElement('div');
    progressBar.className = 'progress';
    
    const progressFill = document.createElement('div');
    progressFill.className = `progress-bar bg-${config.variant}`;
    
    if (config.animated) {
        progressFill.classList.add('progress-bar-animated');
    }
    
    if (config.striped) {
        progressFill.classList.add('progress-bar-striped');
    }
    
    const percentage = Math.round((config.value / config.max) * 100);
    progressFill.style.width = `${percentage}%`;
    
    if (config.showText) {
        progressFill.textContent = `${percentage}%`;
    }
    
    progressBar.appendChild(progressFill);
    
    // 添加更新方法
    progressBar.updateProgress = function(value) {
        const newPercentage = Math.round((value / config.max) * 100);
        progressFill.style.width = `${newPercentage}%`;
        if (config.showText) {
            progressFill.textContent = `${newPercentage}%`;
        }
    };
    
    return progressBar;
}

/**
 * 创建标签页组件
 * @param {Object} options - 配置选项
 * @returns {HTMLElement} 标签页元素
 */
function createTabs(options = {}) {
    const config = {
        tabs: [],
        activeTab: 0,
        className: '',
        ...options
    };

    const tabsContainer = document.createElement('div');
    tabsContainer.className = `tabs ${config.className}`;
    
    const tabsNav = document.createElement('div');
    tabsNav.className = 'tabs-nav';
    
    const tabsContent = document.createElement('div');
    tabsContent.className = 'tabs-content';
    
    config.tabs.forEach((tab, index) => {
        // 创建标签按钮
        const tabButton = document.createElement('button');
        tabButton.className = 'tab-button';
        tabButton.textContent = tab.label;
        tabButton.dataset.tabIndex = index;
        
        if (index === config.activeTab) {
            tabButton.classList.add('active');
        }
        
        tabButton.addEventListener('click', () => {
            switchTab(index);
        });
        
        tabsNav.appendChild(tabButton);
        
        // 创建标签内容
        const tabPane = document.createElement('div');
        tabPane.className = 'tab-pane';
        tabPane.innerHTML = tab.content;
        
        if (index === config.activeTab) {
            tabPane.classList.add('active');
        }
        
        tabsContent.appendChild(tabPane);
    });
    
    function switchTab(index) {
        // 移除所有活跃状态
        tabsNav.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        tabsContent.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        
        // 激活选中的标签
        tabsNav.children[index].classList.add('active');
        tabsContent.children[index].classList.add('active');
        
        // 触发切换事件
        const switchEvent = new CustomEvent('tabSwitch', {
            detail: { index, tab: config.tabs[index] }
        });
        tabsContainer.dispatchEvent(switchEvent);
    }
    
    tabsContainer.appendChild(tabsNav);
    tabsContainer.appendChild(tabsContent);
    
    // 添加切换方法
    tabsContainer.switchTab = switchTab;
    
    return tabsContainer;
}

/**
 * 创建面包屑导航
 * @param {Array} items - 面包屑项目
 * @returns {HTMLElement} 面包屑元素
 */
function createBreadcrumb(items = []) {
    const breadcrumb = document.createElement('nav');
    breadcrumb.className = 'breadcrumb';
    breadcrumb.setAttribute('aria-label', '面包屑导航');
    
    const ol = document.createElement('ol');
    ol.className = 'breadcrumb-list';
    
    items.forEach((item, index) => {
        const li = document.createElement('li');
        li.className = 'breadcrumb-item';
        
        if (index === items.length - 1) {
            li.classList.add('active');
            li.textContent = item.label;
        } else {
            const link = document.createElement('a');
            link.href = item.href || '#';
            link.textContent = item.label;
            
            if (item.action) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    item.action();
                });
            }
            
            li.appendChild(link);
        }
        
        ol.appendChild(li);
    });
    
    breadcrumb.appendChild(ol);
    
    return breadcrumb;
}

/**
 * 创建徽章组件
 * @param {Object} options - 配置选项
 * @returns {HTMLElement} 徽章元素
 */
function createBadge(options = {}) {
    const config = {
        text: '',
        variant: 'primary', // primary, secondary, success, warning, danger, info
        pill: false,
        ...options
    };

    const badge = document.createElement('span');
    badge.className = `badge badge-${config.variant}`;
    
    if (config.pill) {
        badge.classList.add('badge-pill');
    }
    
    badge.textContent = config.text;
    
    return badge;
}

/**
 * 创建卡片组件
 * @param {Object} options - 配置选项
 * @returns {HTMLElement} 卡片元素
 */
function createCard(options = {}) {
    const config = {
        title: '',
        content: '',
        footer: '',
        className: '',
        ...options
    };

    const card = document.createElement('div');
    card.className = `card ${config.className}`;
    
    if (config.title) {
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header';
        cardHeader.innerHTML = `<h5 class="card-title">${config.title}</h5>`;
        card.appendChild(cardHeader);
    }
    
    if (config.content) {
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        cardBody.innerHTML = config.content;
        card.appendChild(cardBody);
    }
    
    if (config.footer) {
        const cardFooter = document.createElement('div');
        cardFooter.className = 'card-footer';
        cardFooter.innerHTML = config.footer;
        card.appendChild(cardFooter);
    }
    
    return card;
}

// 导出组件函数
if (typeof window !== 'undefined') {
    window.createDropdown = createDropdown;
    window.showDropdown = showDropdown;
    window.hideDropdown = hideDropdown;
    window.createTooltip = createTooltip;
    window.createLoader = createLoader;
    window.createProgressBar = createProgressBar;
    window.createTabs = createTabs;
    window.createBreadcrumb = createBreadcrumb;
    window.createBadge = createBadge;
    window.createCard = createCard;
}