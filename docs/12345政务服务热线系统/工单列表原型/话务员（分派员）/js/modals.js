// 模态框管理

/**
 * 模态框管理器
 */
class ModalManager {
    constructor() {
        this.modals = new Map();
        this.zIndexBase = 1000;
        this.init();
    }

    /**
     * 初始化模态框管理器
     */
    init() {
        this.createModalContainer();
        this.bindGlobalEvents();
    }

    /**
     * 创建模态框容器
     */
    createModalContainer() {
        if (!document.getElementById('modal-container')) {
            const container = document.createElement('div');
            container.id = 'modal-container';
            container.className = 'modal-container';
            document.body.appendChild(container);
        }
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeTopModal();
            }
        });
    }

    /**
     * 创建模态框
     * @param {Object} options - 模态框选项
     * @returns {HTMLElement} 模态框元素
     */
    create(options = {}) {
        const config = {
            id: generateId('modal'),
            title: '提示',
            content: '',
            footer: '',
            size: 'medium', // small, medium, large, full
            closable: true,
            backdrop: true,
            keyboard: true,
            animation: true,
            className: '',
            ...options
        };

        const modal = this.createModalElement(config);
        this.modals.set(config.id, {
            element: modal,
            config: config
        });

        return modal;
    }

    /**
     * 创建模态框元素
     * @param {Object} config - 配置对象
     * @returns {HTMLElement} 模态框元素
     */
    createModalElement(config) {
        const modal = document.createElement('div');
        modal.className = `modal ${config.size} ${config.className}`;
        modal.id = config.id;
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('role', 'dialog');
        modal.setAttribute('aria-labelledby', `${config.id}-title`);
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-backdrop" ${config.backdrop ? 'data-dismiss="modal"' : ''}></div>
            <div class="modal-dialog">
                <div class="modal-content">
                    ${this.createModalHeader(config)}
                    <div class="modal-body">
                        ${config.content}
                    </div>
                    ${config.footer ? `<div class="modal-footer">${config.footer}</div>` : ''}
                </div>
            </div>
        `;

        // 绑定事件
        this.bindModalEvents(modal, config);

        return modal;
    }

    /**
     * 创建模态框头部
     * @param {Object} config - 配置对象
     * @returns {string} 头部HTML
     */
    createModalHeader(config) {
        if (!config.title && !config.closable) {
            return '';
        }

        return `
            <div class="modal-header">
                ${config.title ? `<h4 class="modal-title" id="${config.id}-title">${config.title}</h4>` : ''}
                ${config.closable ? '<button type="button" class="modal-close" data-dismiss="modal" aria-label="关闭"><span aria-hidden="true">&times;</span></button>' : ''}
            </div>
        `;
    }

    /**
     * 绑定模态框事件
     * @param {HTMLElement} modal - 模态框元素
     * @param {Object} config - 配置对象
     */
    bindModalEvents(modal, config) {
        // 关闭按钮事件
        const closeButtons = modal.querySelectorAll('[data-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.hide(config.id);
            });
        });

        // 阻止模态框内容区域点击事件冒泡
        const modalDialog = modal.querySelector('.modal-dialog');
        if (modalDialog) {
            modalDialog.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    }

    /**
     * 显示模态框
     * @param {string|HTMLElement} modal - 模态框ID或元素
     * @param {Object} options - 显示选项
     */
    show(modal, options = {}) {
        let modalElement;
        let modalId;

        if (typeof modal === 'string') {
            modalId = modal;
            const modalData = this.modals.get(modalId);
            if (!modalData) {
                console.error(`Modal with id '${modalId}' not found`);
                return;
            }
            modalElement = modalData.element;
        } else {
            modalElement = modal;
            modalId = modal.id;
        }

        // 添加到容器
        const container = document.getElementById('modal-container');
        if (!container.contains(modalElement)) {
            container.appendChild(modalElement);
        }

        // 设置z-index
        const zIndex = this.zIndexBase + this.getOpenModalCount();
        modalElement.style.zIndex = zIndex;

        // 显示模态框
        modalElement.classList.add('show');
        modalElement.setAttribute('aria-hidden', 'false');
        document.body.classList.add('modal-open');

        // 聚焦到模态框
        setTimeout(() => {
            modalElement.focus();
        }, 100);

        // 触发显示事件
        const showEvent = new CustomEvent('modalShown', {
            detail: { modalId, element: modalElement }
        });
        document.dispatchEvent(showEvent);
    }

    /**
     * 隐藏模态框
     * @param {string|HTMLElement} modal - 模态框ID或元素
     */
    hide(modal) {
        let modalElement;
        let modalId;

        if (typeof modal === 'string') {
            modalId = modal;
            const modalData = this.modals.get(modalId);
            if (!modalData) {
                console.error(`Modal with id '${modalId}' not found`);
                return;
            }
            modalElement = modalData.element;
        } else {
            modalElement = modal;
            modalId = modal.id;
        }

        // 隐藏模态框
        modalElement.classList.remove('show');
        modalElement.setAttribute('aria-hidden', 'true');

        // 如果没有其他模态框打开，移除body类
        setTimeout(() => {
            if (this.getOpenModalCount() === 0) {
                document.body.classList.remove('modal-open');
            }
        }, 300);

        // 触发隐藏事件
        const hideEvent = new CustomEvent('modalHidden', {
            detail: { modalId, element: modalElement }
        });
        document.dispatchEvent(hideEvent);
    }

    /**
     * 销毁模态框
     * @param {string} modalId - 模态框ID
     */
    destroy(modalId) {
        const modalData = this.modals.get(modalId);
        if (!modalData) {
            return;
        }

        this.hide(modalId);
        
        setTimeout(() => {
            if (modalData.element.parentNode) {
                modalData.element.parentNode.removeChild(modalData.element);
            }
            this.modals.delete(modalId);
        }, 300);
    }

    /**
     * 关闭顶层模态框
     */
    closeTopModal() {
        const openModals = Array.from(this.modals.values())
            .filter(modal => modal.element.classList.contains('show'))
            .sort((a, b) => {
                const aZIndex = parseInt(a.element.style.zIndex) || 0;
                const bZIndex = parseInt(b.element.style.zIndex) || 0;
                return bZIndex - aZIndex;
            });

        if (openModals.length > 0) {
            this.hide(openModals[0].element);
        }
    }

    /**
     * 获取打开的模态框数量
     * @returns {number} 打开的模态框数量
     */
    getOpenModalCount() {
        return Array.from(this.modals.values())
            .filter(modal => modal.element.classList.contains('show')).length;
    }

    /**
     * 更新模态框内容
     * @param {string} modalId - 模态框ID
     * @param {string} content - 新内容
     */
    updateContent(modalId, content) {
        const modalData = this.modals.get(modalId);
        if (!modalData) {
            return;
        }

        const modalBody = modalData.element.querySelector('.modal-body');
        if (modalBody) {
            modalBody.innerHTML = content;
        }
    }

    /**
     * 更新模态框标题
     * @param {string} modalId - 模态框ID
     * @param {string} title - 新标题
     */
    updateTitle(modalId, title) {
        const modalData = this.modals.get(modalId);
        if (!modalData) {
            return;
        }

        const modalTitle = modalData.element.querySelector('.modal-title');
        if (modalTitle) {
            modalTitle.textContent = title;
        }
    }
}

// 全局模态框管理器实例
const modalManager = new ModalManager();

/**
 * 创建模态框的便捷函数
 * @param {Object} options - 模态框选项
 * @returns {HTMLElement} 模态框元素
 */
function createModal(options) {
    return modalManager.create(options);
}

/**
 * 显示模态框的便捷函数
 * @param {string|HTMLElement} modal - 模态框ID或元素
 * @param {Object} options - 显示选项
 */
function showModal(modal, options) {
    modalManager.show(modal, options);
}

/**
 * 隐藏模态框的便捷函数
 * @param {string|HTMLElement} modal - 模态框ID或元素
 */
function hideModal(modal) {
    modalManager.hide(modal);
}

/**
 * 销毁模态框的便捷函数
 * @param {string} modalId - 模态框ID
 */
function destroyModal(modalId) {
    modalManager.destroy(modalId);
}

/**
 * 创建确认对话框
 * @param {Object} options - 对话框选项
 * @returns {Promise} 确认结果
 */
function createConfirmDialog(options = {}) {
    const config = {
        title: '确认',
        message: '确定要执行此操作吗？',
        confirmText: '确定',
        cancelText: '取消',
        type: 'warning', // info, success, warning, danger
        ...options
    };

    return new Promise((resolve) => {
        const modal = createModal({
            title: config.title,
            size: 'small',
            content: `
                <div class="confirm-dialog ${config.type}">
                    <div class="confirm-icon">
                        ${getConfirmIcon(config.type)}
                    </div>
                    <div class="confirm-message">
                        ${config.message}
                    </div>
                </div>
            `,
            footer: `
                <button class="btn btn-outline" data-action="cancel">${config.cancelText}</button>
                <button class="btn btn-${getConfirmButtonClass(config.type)}" data-action="confirm">${config.confirmText}</button>
            `
        });

        // 绑定事件
        modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
            hideModal(modal);
            resolve(false);
        });

        modal.querySelector('[data-action="confirm"]').addEventListener('click', () => {
            hideModal(modal);
            resolve(true);
        });

        // 监听模态框关闭事件
        document.addEventListener('modalHidden', function onModalHidden(e) {
            if (e.detail.element === modal) {
                document.removeEventListener('modalHidden', onModalHidden);
                setTimeout(() => destroyModal(modal.id), 300);
            }
        });

        showModal(modal);
    });
}

/**
 * 创建提示对话框
 * @param {Object} options - 对话框选项
 * @returns {Promise} 确认结果
 */
function createAlertDialog(options = {}) {
    const config = {
        title: '提示',
        message: '',
        confirmText: '确定',
        type: 'info',
        ...options
    };

    return new Promise((resolve) => {
        const modal = createModal({
            title: config.title,
            size: 'small',
            content: `
                <div class="alert-dialog ${config.type}">
                    <div class="alert-icon">
                        ${getConfirmIcon(config.type)}
                    </div>
                    <div class="alert-message">
                        ${config.message}
                    </div>
                </div>
            `,
            footer: `
                <button class="btn btn-${getConfirmButtonClass(config.type)}" data-action="confirm">${config.confirmText}</button>
            `
        });

        // 绑定事件
        modal.querySelector('[data-action="confirm"]').addEventListener('click', () => {
            hideModal(modal);
            resolve(true);
        });

        // 监听模态框关闭事件
        document.addEventListener('modalHidden', function onModalHidden(e) {
            if (e.detail.element === modal) {
                document.removeEventListener('modalHidden', onModalHidden);
                setTimeout(() => destroyModal(modal.id), 300);
            }
        });

        showModal(modal);
    });
}

/**
 * 创建工单详情模态框
 * @param {Object} ticket - 工单数据
 * @returns {HTMLElement} 模态框元素
 */
function createTicketDetailModal(ticket) {
    const modal = createModal({
        title: `工单详情 - ${ticket.ticketNo}`,
        size: 'large',
        content: createTicketDetailContent(ticket),
        footer: `
            <button class="btn btn-outline" data-action="close">关闭</button>
            <button class="btn btn-primary" data-action="edit">编辑</button>
            <button class="btn btn-success" data-action="process">处理</button>
        `
    });

    // 绑定事件
    modal.querySelector('[data-action="close"]').addEventListener('click', () => {
        hideModal(modal);
    });

    modal.querySelector('[data-action="edit"]').addEventListener('click', () => {
        // 编辑工单逻辑
        console.log('编辑工单:', ticket.id);
    });

    modal.querySelector('[data-action="process"]').addEventListener('click', () => {
        // 处理工单逻辑
        console.log('处理工单:', ticket.id);
    });

    return modal;
}

/**
 * 创建工单详情内容
 * @param {Object} ticket - 工单数据
 * @returns {string} 详情HTML
 */
function createTicketDetailContent(ticket) {
    return `
        <div class="ticket-detail">
            <div class="detail-section">
                <h5>基本信息</h5>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>工单编号:</label>
                        <span>${ticket.ticketNo}</span>
                    </div>
                    <div class="detail-item">
                        <label>状态:</label>
                        <span class="status-badge status-${ticket.status}">${getStatusText(ticket.status)}</span>
                    </div>
                    <div class="detail-item">
                        <label>紧急程度:</label>
                        <span class="urgency-badge urgency-${ticket.urgency}">${getUrgencyText(ticket.urgency)}</span>
                    </div>
                    <div class="detail-item">
                        <label>处理模式:</label>
                        <span>${getModeText(ticket.mode)}</span>
                    </div>
                    <div class="detail-item">
                        <label>创建时间:</label>
                        <span>${formatDateTime(ticket.createTime)}</span>
                    </div>
                    <div class="detail-item">
                        <label>时限要求:</label>
                        <span>${ticket.timeLimit}小时</span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h5>市民信息</h5>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>姓名:</label>
                        <span>${ticket.callerName}</span>
                    </div>
                    <div class="detail-item">
                        <label>联系电话:</label>
                        <span>${ticket.callerPhone}</span>
                    </div>
                    <div class="detail-item">
                        <label>所属区域:</label>
                        <span>${ticket.district}</span>
                    </div>
                    <div class="detail-item">
                        <label>详细地址:</label>
                        <span>${ticket.address || '未填写'}</span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h5>问题信息</h5>
                <div class="detail-grid">
                    <div class="detail-item full-width">
                        <label>问题分类:</label>
                        <span>${ticket.category}</span>
                    </div>
                    <div class="detail-item full-width">
                        <label>问题描述:</label>
                        <div class="description-content">${ticket.description}</div>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h5>处理信息</h5>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>承办部门:</label>
                        <span>${ticket.department || '待分派'}</span>
                    </div>
                    <div class="detail-item">
                        <label>处理人:</label>
                        <span>${ticket.handler || '待分派'}</span>
                    </div>
                    <div class="detail-item">
                        <label>分派时间:</label>
                        <span>${ticket.assignTime ? formatDateTime(ticket.assignTime) : '未分派'}</span>
                    </div>
                    <div class="detail-item">
                        <label>处理结果:</label>
                        <span>${ticket.result || '待处理'}</span>
                    </div>
                </div>
            </div>
            
            ${ticket.attachments && ticket.attachments.length > 0 ? `
                <div class="detail-section">
                    <h5>附件信息</h5>
                    <div class="attachments-list">
                        ${ticket.attachments.map(file => `
                            <div class="attachment-item">
                                <span class="attachment-name">${file.name}</span>
                                <span class="attachment-size">${formatFileSize(file.size)}</span>
                                <button class="btn btn-sm btn-outline" onclick="downloadFile('${file.url}', '${file.name}')">下载</button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        </div>
    `;
}

/**
 * 获取确认图标
 * @param {string} type - 类型
 * @returns {string} 图标HTML
 */
function getConfirmIcon(type) {
    const icons = {
        info: '&#8505;',
        success: '&#10004;',
        warning: '&#9888;',
        danger: '&#10006;'
    };
    return icons[type] || icons.info;
}

/**
 * 获取确认按钮样式类
 * @param {string} type - 类型
 * @returns {string} 样式类
 */
function getConfirmButtonClass(type) {
    const classes = {
        info: 'primary',
        success: 'success',
        warning: 'warning',
        danger: 'danger'
    };
    return classes[type] || 'primary';
}

/**
 * 获取状态文本
 * @param {string} status - 状态值
 * @returns {string} 状态文本
 */
function getStatusText(status) {
    const statusMap = {
        draft: '草稿/暂存',
        pending: '待接收',
        processing: '处理中',
        reviewing: '待审核',
        callback: '待回访',
        resolved: '已办结',
        closed: '已关闭'
    };
    return statusMap[status] || status;
}

/**
 * 获取紧急程度文本
 * @param {string} urgency - 紧急程度值
 * @returns {string} 紧急程度文本
 */
function getUrgencyText(urgency) {
    const urgencyMap = {
        low: '一般',
        normal: '普通',
        high: '紧急',
        urgent: '特急'
    };
    return urgencyMap[urgency] || urgency;
}

/**
 * 获取处理模式文本
 * @param {string} mode - 模式值
 * @returns {string} 模式文本
 */
function getModeText(mode) {
    const modeMap = {
        instant: '即时办结',
        normal: '普通流转',
        collaborative: '多部门协同'
    };
    return modeMap[mode] || mode;
}

// 导出模态框相关函数
if (typeof window !== 'undefined') {
    window.ModalManager = ModalManager;
    window.createModal = createModal;
    window.showModal = showModal;
    window.hideModal = hideModal;
    window.destroyModal = destroyModal;
    window.createConfirmDialog = createConfirmDialog;
    window.createAlertDialog = createAlertDialog;
    window.createTicketDetailModal = createTicketDetailModal;
}