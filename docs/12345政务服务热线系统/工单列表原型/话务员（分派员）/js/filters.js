// 筛选功能管理

/**
 * 筛选器管理类
 */
class FilterManager {
    constructor(options = {}) {
        this.options = {
            autoApply: false,
            debounceDelay: 300,
            ...options
        };
        
        this.filters = {
            keyword: '',
            status: '',
            urgency: '',
            mode: '',
            startDate: '',
            endDate: '',
            category: '',
            district: '',
            department: ''
        };
        
        this.elements = {};
        this.debounceTimer = null;
        
        this.init();
    }

    /**
     * 初始化筛选器
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.loadSavedFilters();
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.elements = {
            searchInput: document.getElementById('searchInput'),
            statusFilter: document.getElementById('statusFilter'),
            urgencyFilter: document.getElementById('urgencyFilter'),
            modeFilter: document.getElementById('modeFilter'),
            startDate: document.getElementById('startDate'),
            endDate: document.getElementById('endDate'),
            applyFilterBtn: document.getElementById('applyFilterBtn'),
            clearFilterBtn: document.getElementById('clearFilterBtn'),
            advancedSearchBtn: document.getElementById('advancedSearchBtn')
        };
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 搜索输入框
        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('input', (e) => {
                this.updateFilter('keyword', e.target.value);
                if (this.options.autoApply) {
                    this.debounceApply();
                }
            });
            
            this.elements.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });
        }

        // 状态筛选
        if (this.elements.statusFilter) {
            this.elements.statusFilter.addEventListener('change', (e) => {
                this.updateFilter('status', e.target.value);
                if (this.options.autoApply) {
                    this.applyFilters();
                }
            });
        }

        // 紧急程度筛选
        if (this.elements.urgencyFilter) {
            this.elements.urgencyFilter.addEventListener('change', (e) => {
                this.updateFilter('urgency', e.target.value);
                if (this.options.autoApply) {
                    this.applyFilters();
                }
            });
        }

        // 处理模式筛选
        if (this.elements.modeFilter) {
            this.elements.modeFilter.addEventListener('change', (e) => {
                this.updateFilter('mode', e.target.value);
                if (this.options.autoApply) {
                    this.applyFilters();
                }
            });
        }

        // 开始日期
        if (this.elements.startDate) {
            this.elements.startDate.addEventListener('change', (e) => {
                this.updateFilter('startDate', e.target.value);
                this.validateDateRange();
                if (this.options.autoApply) {
                    this.applyFilters();
                }
            });
        }

        // 结束日期
        if (this.elements.endDate) {
            this.elements.endDate.addEventListener('change', (e) => {
                this.updateFilter('endDate', e.target.value);
                this.validateDateRange();
                if (this.options.autoApply) {
                    this.applyFilters();
                }
            });
        }

        // 应用筛选按钮
        if (this.elements.applyFilterBtn) {
            this.elements.applyFilterBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }

        // 清除筛选按钮
        if (this.elements.clearFilterBtn) {
            this.elements.clearFilterBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }

        // 高级搜索按钮
        if (this.elements.advancedSearchBtn) {
            this.elements.advancedSearchBtn.addEventListener('click', () => {
                this.showAdvancedSearch();
            });
        }
    }

    /**
     * 更新筛选条件
     * @param {string} key - 筛选键
     * @param {any} value - 筛选值
     */
    updateFilter(key, value) {
        this.filters[key] = value;
        this.updateFilterDisplay();
        this.saveFilters();
    }

    /**
     * 应用筛选
     */
    applyFilters() {
        const activeFilters = this.getActiveFilters();
        
        // 触发筛选事件
        const event = new CustomEvent('filtersApplied', {
            detail: {
                filters: activeFilters,
                allFilters: { ...this.filters }
            }
        });
        document.dispatchEvent(event);
        
        this.updateFilterCount();
    }

    /**
     * 防抖应用筛选
     */
    debounceApply() {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.applyFilters();
        }, this.options.debounceDelay);
    }

    /**
     * 清除筛选
     */
    clearFilters() {
        // 重置筛选条件
        Object.keys(this.filters).forEach(key => {
            this.filters[key] = '';
        });
        
        // 重置表单元素
        Object.values(this.elements).forEach(element => {
            if (element && element.tagName) {
                if (element.type === 'text' || element.type === 'date') {
                    element.value = '';
                } else if (element.tagName === 'SELECT') {
                    element.selectedIndex = 0;
                }
            }
        });
        
        this.updateFilterDisplay();
        this.saveFilters();
        this.applyFilters();
    }

    /**
     * 获取活跃的筛选条件
     * @returns {Object} 活跃的筛选条件
     */
    getActiveFilters() {
        const activeFilters = {};
        
        Object.entries(this.filters).forEach(([key, value]) => {
            if (value && value.toString().trim() !== '') {
                activeFilters[key] = value;
            }
        });
        
        return activeFilters;
    }

    /**
     * 设置筛选条件
     * @param {Object} filters - 筛选条件
     */
    setFilters(filters) {
        Object.entries(filters).forEach(([key, value]) => {
            if (this.filters.hasOwnProperty(key)) {
                this.filters[key] = value;
            }
        });
        
        this.updateFormElements();
        this.updateFilterDisplay();
    }

    /**
     * 更新表单元素值
     */
    updateFormElements() {
        Object.entries(this.filters).forEach(([key, value]) => {
            const element = this.getElementByFilterKey(key);
            if (element) {
                element.value = value;
            }
        });
    }

    /**
     * 根据筛选键获取对应的DOM元素
     * @param {string} key - 筛选键
     * @returns {HTMLElement} DOM元素
     */
    getElementByFilterKey(key) {
        const elementMap = {
            keyword: this.elements.searchInput,
            status: this.elements.statusFilter,
            urgency: this.elements.urgencyFilter,
            mode: this.elements.modeFilter,
            startDate: this.elements.startDate,
            endDate: this.elements.endDate
        };
        
        return elementMap[key];
    }

    /**
     * 验证日期范围
     */
    validateDateRange() {
        const startDate = this.filters.startDate;
        const endDate = this.filters.endDate;
        
        if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
            showNotification('开始日期不能晚于结束日期', 'warning');
            
            // 自动调整结束日期
            this.updateFilter('endDate', startDate);
            if (this.elements.endDate) {
                this.elements.endDate.value = startDate;
            }
        }
    }

    /**
     * 更新筛选显示
     */
    updateFilterDisplay() {
        const activeFilters = this.getActiveFilters();
        const hasActiveFilters = Object.keys(activeFilters).length > 0;
        
        // 更新清除按钮状态
        if (this.elements.clearFilterBtn) {
            this.elements.clearFilterBtn.disabled = !hasActiveFilters;
        }
        
        // 更新筛选指示器
        this.updateFilterIndicators(activeFilters);
    }

    /**
     * 更新筛选指示器
     * @param {Object} activeFilters - 活跃的筛选条件
     */
    updateFilterIndicators(activeFilters) {
        // 移除现有指示器
        const existingIndicators = document.querySelectorAll('.filter-indicator');
        existingIndicators.forEach(indicator => indicator.remove());
        
        // 创建新的指示器
        const filterBar = document.querySelector('.filter-bar');
        if (!filterBar) return;
        
        Object.entries(activeFilters).forEach(([key, value]) => {
            if (key === 'keyword' && value.trim()) {
                this.createFilterIndicator(filterBar, '搜索', `"${value}"`, key);
            } else if (key === 'status' && value) {
                this.createFilterIndicator(filterBar, '状态', this.getStatusText(value), key);
            } else if (key === 'urgency' && value) {
                this.createFilterIndicator(filterBar, '紧急程度', this.getUrgencyText(value), key);
            } else if (key === 'mode' && value) {
                this.createFilterIndicator(filterBar, '处理模式', this.getModeText(value), key);
            } else if (key === 'startDate' || key === 'endDate') {
                // 日期范围单独处理
                if (activeFilters.startDate || activeFilters.endDate) {
                    const dateRange = this.getDateRangeText(activeFilters.startDate, activeFilters.endDate);
                    if (dateRange && !document.querySelector('.filter-indicator[data-key="dateRange"]')) {
                        this.createFilterIndicator(filterBar, '时间范围', dateRange, 'dateRange');
                    }
                }
            }
        });
    }

    /**
     * 创建筛选指示器
     * @param {HTMLElement} container - 容器元素
     * @param {string} label - 标签
     * @param {string} value - 值
     * @param {string} key - 键
     */
    createFilterIndicator(container, label, value, key) {
        const indicator = document.createElement('div');
        indicator.className = 'filter-indicator';
        indicator.dataset.key = key;
        indicator.innerHTML = `
            <span class="filter-label">${label}:</span>
            <span class="filter-value">${value}</span>
            <button class="filter-remove" data-key="${key}">×</button>
        `;
        
        // 绑定移除事件
        const removeBtn = indicator.querySelector('.filter-remove');
        removeBtn.addEventListener('click', () => {
            this.removeFilter(key);
        });
        
        container.appendChild(indicator);
    }

    /**
     * 移除筛选条件
     * @param {string} key - 筛选键
     */
    removeFilter(key) {
        if (key === 'dateRange') {
            this.updateFilter('startDate', '');
            this.updateFilter('endDate', '');
            if (this.elements.startDate) this.elements.startDate.value = '';
            if (this.elements.endDate) this.elements.endDate.value = '';
        } else {
            this.updateFilter(key, '');
            const element = this.getElementByFilterKey(key);
            if (element) {
                if (element.type === 'text') {
                    element.value = '';
                } else if (element.tagName === 'SELECT') {
                    element.selectedIndex = 0;
                }
            }
        }
        
        this.applyFilters();
    }

    /**
     * 获取日期范围文本
     * @param {string} startDate - 开始日期
     * @param {string} endDate - 结束日期
     * @returns {string} 日期范围文本
     */
    getDateRangeText(startDate, endDate) {
        if (startDate && endDate) {
            return `${startDate} 至 ${endDate}`;
        } else if (startDate) {
            return `${startDate} 之后`;
        } else if (endDate) {
            return `${endDate} 之前`;
        }
        return '';
    }

    /**
     * 获取状态文本
     * @param {string} status - 状态值
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            draft: '草稿/暂存',
            pending: '待接收',
            processing: '处理中',
            reviewing: '待审核',
            callback: '待回访',
            resolved: '已办结',
            closed: '已关闭'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取紧急程度文本
     * @param {string} urgency - 紧急程度值
     * @returns {string} 紧急程度文本
     */
    getUrgencyText(urgency) {
        const urgencyMap = {
            low: '一般',
            normal: '普通',
            high: '紧急',
            urgent: '特急'
        };
        return urgencyMap[urgency] || urgency;
    }

    /**
     * 获取处理模式文本
     * @param {string} mode - 模式值
     * @returns {string} 模式文本
     */
    getModeText(mode) {
        const modeMap = {
            instant: '即时办结',
            normal: '普通流转',
            collaborative: '多部门协同'
        };
        return modeMap[mode] || mode;
    }

    /**
     * 更新筛选计数
     */
    updateFilterCount() {
        const activeFilters = this.getActiveFilters();
        const count = Object.keys(activeFilters).length;
        
        // 更新高级搜索按钮显示
        if (this.elements.advancedSearchBtn) {
            const originalText = this.elements.advancedSearchBtn.dataset.originalText || '高级搜索';
            this.elements.advancedSearchBtn.dataset.originalText = originalText;
            
            if (count > 0) {
                this.elements.advancedSearchBtn.textContent = `${originalText} (${count})`;
                this.elements.advancedSearchBtn.classList.add('active');
            } else {
                this.elements.advancedSearchBtn.textContent = originalText;
                this.elements.advancedSearchBtn.classList.remove('active');
            }
        }
    }

    /**
     * 显示高级搜索
     */
    showAdvancedSearch() {
        // 创建高级搜索模态框
        const modal = createModal({
            title: '高级搜索',
            size: 'large',
            content: this.createAdvancedSearchForm(),
            footer: `
                <button class="btn btn-outline" data-action="cancel">取消</button>
                <button class="btn btn-secondary" data-action="reset">重置</button>
                <button class="btn btn-primary" data-action="search">搜索</button>
            `
        });
        
        // 绑定事件
        modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
            hideModal(modal);
        });
        
        modal.querySelector('[data-action="reset"]').addEventListener('click', () => {
            this.resetAdvancedSearchForm(modal);
        });
        
        modal.querySelector('[data-action="search"]').addEventListener('click', () => {
            this.applyAdvancedSearch(modal);
            hideModal(modal);
        });
        
        showModal(modal);
    }

    /**
     * 创建高级搜索表单
     * @returns {string} 表单HTML
     */
    createAdvancedSearchForm() {
        return `
            <div class="advanced-search-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>工单编号:</label>
                        <input type="text" id="advTicketNo" class="form-control" value="${this.filters.ticketNo || ''}">
                    </div>
                    <div class="form-group">
                        <label>市民姓名:</label>
                        <input type="text" id="advCallerName" class="form-control" value="${this.filters.callerName || ''}">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>联系电话:</label>
                        <input type="text" id="advCallerPhone" class="form-control" value="${this.filters.callerPhone || ''}">
                    </div>
                    <div class="form-group">
                        <label>问题分类:</label>
                        <select id="advCategory" class="form-control">
                            <option value="">全部分类</option>
                            <option value="市政设施">市政设施</option>
                            <option value="环境保护">环境保护</option>
                            <option value="交通出行">交通出行</option>
                            <option value="公共安全">公共安全</option>
                            <option value="教育文化">教育文化</option>
                            <option value="医疗卫生">医疗卫生</option>
                            <option value="住房保障">住房保障</option>
                            <option value="社会保障">社会保障</option>
                            <option value="城市管理">城市管理</option>
                            <option value="供水供电">供水供电</option>
                            <option value="供暖燃气">供暖燃气</option>
                            <option value="通信网络">通信网络</option>
                            <option value="食品安全">食品安全</option>
                            <option value="消费维权">消费维权</option>
                            <option value="劳动就业">劳动就业</option>
                            <option value="社区服务">社区服务</option>
                            <option value="政务服务">政务服务</option>
                            <option value="法律咨询">法律咨询</option>
                            <option value="其他民生">其他民生</option>
                            <option value="物业管理">物业管理</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>所属区域:</label>
                        <select id="advDistrict" class="form-control">
                            <option value="">全部区域</option>
                            <option value="海淀区">海淀区</option>
                            <option value="西城区">西城区</option>
                            <option value="东城区">东城区</option>
                            <option value="丰台区">丰台区</option>
                            <option value="石景山区">石景山区</option>
                            <option value="门头沟区">门头沟区</option>
                            <option value="房山区">房山区</option>
                            <option value="通州区">通州区</option>
                            <option value="顺义区">顺义区</option>
                            <option value="昌平区">昌平区</option>
                            <option value="大兴区">大兴区</option>
                            <option value="怀柔区">怀柔区</option>
                            <option value="平谷区">平谷区</option>
                            <option value="密云区">密云区</option>
                            <option value="延庆区">延庆区</option>
                            <option value="朝阳区">朝阳区</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>承办部门:</label>
                        <input type="text" id="advDepartment" class="form-control" value="${this.filters.department || ''}">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>处理人:</label>
                        <input type="text" id="advHandler" class="form-control" value="${this.filters.handler || ''}">
                    </div>
                    <div class="form-group">
                        <label>问题描述:</label>
                        <input type="text" id="advDescription" class="form-control" value="${this.filters.description || ''}">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 重置高级搜索表单
     * @param {HTMLElement} modal - 模态框元素
     */
    resetAdvancedSearchForm(modal) {
        const inputs = modal.querySelectorAll('input, select');
        inputs.forEach(input => {
            if (input.type === 'text') {
                input.value = '';
            } else if (input.tagName === 'SELECT') {
                input.selectedIndex = 0;
            }
        });
    }

    /**
     * 应用高级搜索
     * @param {HTMLElement} modal - 模态框元素
     */
    applyAdvancedSearch(modal) {
        const formData = {
            ticketNo: modal.querySelector('#advTicketNo').value,
            callerName: modal.querySelector('#advCallerName').value,
            callerPhone: modal.querySelector('#advCallerPhone').value,
            category: modal.querySelector('#advCategory').value,
            district: modal.querySelector('#advDistrict').value,
            department: modal.querySelector('#advDepartment').value,
            handler: modal.querySelector('#advHandler').value,
            description: modal.querySelector('#advDescription').value
        };
        
        // 更新筛选条件
        Object.entries(formData).forEach(([key, value]) => {
            this.updateFilter(key, value);
        });
        
        this.applyFilters();
    }

    /**
     * 保存筛选条件到本地存储
     */
    saveFilters() {
        storage.set('ticketFilters', this.filters);
    }

    /**
     * 从本地存储加载筛选条件
     */
    loadSavedFilters() {
        const savedFilters = storage.get('ticketFilters', {});
        if (savedFilters && Object.keys(savedFilters).length > 0) {
            this.setFilters(savedFilters);
        }
    }

    /**
     * 获取当前筛选条件
     * @returns {Object} 当前筛选条件
     */
    getFilters() {
        return { ...this.filters };
    }

    /**
     * 重置筛选器
     */
    reset() {
        this.clearFilters();
        storage.remove('ticketFilters');
    }
}

// 导出筛选器类
if (typeof window !== 'undefined') {
    window.FilterManager = FilterManager;
}