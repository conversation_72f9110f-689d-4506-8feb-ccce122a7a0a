// API接口管理

/**
 * API配置
 */
const API_CONFIG = {
    baseURL: '/api',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
};

/**
 * HTTP请求工具
 */
class HttpClient {
    constructor(config = {}) {
        this.config = { ...API_CONFIG, ...config };
    }

    /**
     * 发送请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求结果
     */
    async request(url, options = {}) {
        const fullUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`;
        
        const requestOptions = {
            method: 'GET',
            headers: { ...this.config.headers },
            ...options
        };

        if (requestOptions.body && typeof requestOptions.body === 'object') {
            requestOptions.body = JSON.stringify(requestOptions.body);
        }

        try {
            const response = await fetch(fullUrl, requestOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        } catch (error) {
            console.error('请求失败:', error);
            throw error;
        }
    }

    get(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url;
        return this.request(fullUrl);
    }

    post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: data
        });
    }

    put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: data
        });
    }

    delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
}

/**
 * 工单API服务
 */
class TicketAPI {
    constructor() {
        this.http = new HttpClient();
    }

    /**
     * 获取工单列表
     * @param {Object} params - 查询参数
     * @returns {Promise} 工单列表数据
     */
    async getTicketList(params = {}) {
        // 模拟API调用，返回静态数据
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(this.getMockTicketData(params));
            }, 500);
        });
    }

    /**
     * 获取工单详情
     * @param {string} ticketId - 工单ID
     * @returns {Promise} 工单详情
     */
    async getTicketDetail(ticketId) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(this.getMockTicketDetail(ticketId));
            }, 300);
        });
    }

    /**
     * 创建工单
     * @param {Object} ticketData - 工单数据
     * @returns {Promise} 创建结果
     */
    async createTicket(ticketData) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: {
                        ticketId: 'T' + Date.now(),
                        ...ticketData
                    },
                    message: '工单创建成功'
                });
            }, 800);
        });
    }

    /**
     * 更新工单
     * @param {string} ticketId - 工单ID
     * @param {Object} updateData - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateTicket(ticketId, updateData) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: { ticketId, ...updateData },
                    message: '工单更新成功'
                });
            }, 600);
        });
    }

    /**
     * 派发工单
     * @param {string} ticketId - 工单ID
     * @param {Object} assignData - 派发数据
     * @returns {Promise} 派发结果
     */
    async assignTicket(ticketId, assignData) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: { ticketId, ...assignData },
                    message: '工单派发成功'
                });
            }, 700);
        });
    }

    /**
     * 即时办结
     * @param {string} ticketId - 工单ID
     * @param {Object} resolveData - 办结数据
     * @returns {Promise} 办结结果
     */
    async instantResolve(ticketId, resolveData) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: { ticketId, ...resolveData },
                    message: '即时办结成功'
                });
            }, 600);
        });
    }

    /**
     * 批量操作
     * @param {Array} ticketIds - 工单ID数组
     * @param {string} action - 操作类型
     * @param {Object} actionData - 操作数据
     * @returns {Promise} 批量操作结果
     */
    async batchOperation(ticketIds, action, actionData = {}) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: {
                        processed: ticketIds.length,
                        failed: 0,
                        action,
                        ...actionData
                    },
                    message: `批量${action}成功，共处理${ticketIds.length}条工单`
                });
            }, 1000);
        });
    }

    /**
     * 获取工单统计信息
     * @returns {Promise} 统计信息
     */
    async getTicketStatistics() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: {
                        todayNew: 45,
                        pending: 23,
                        processing: 67,
                        instantResolved: 156,
                        overdue: 8,
                        satisfaction: 98.5
                    }
                });
            }, 300);
        });
    }

    /**
     * 获取模拟工单数据
     * @param {Object} params - 查询参数
     * @returns {Object} 模拟数据
     */
    getMockTicketData(params = {}) {
        const { page = 1, pageSize = 20, status, urgency, keyword } = params;
        
        // 模拟工单数据
        const allTickets = this.generateMockTickets(156);
        
        // 过滤数据
        let filteredTickets = allTickets;
        
        if (status) {
            filteredTickets = filteredTickets.filter(ticket => ticket.status === status);
        }
        
        if (urgency) {
            filteredTickets = filteredTickets.filter(ticket => ticket.urgency === urgency);
        }
        
        if (keyword) {
            filteredTickets = filteredTickets.filter(ticket => 
                ticket.ticketNo.includes(keyword) ||
                ticket.callerName.includes(keyword) ||
                ticket.description.includes(keyword)
            );
        }
        
        // 分页
        const total = filteredTickets.length;
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        const tickets = filteredTickets.slice(start, end);
        
        return {
            success: true,
            data: {
                list: tickets,
                total,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize)
                }
            }
        };
    }

    /**
     * 生成模拟工单数据
     * @param {number} count - 数据条数
     * @returns {Array} 工单数组
     */
    generateMockTickets(count) {
        const statuses = ['draft', 'pending', 'processing', 'reviewing', 'callback', 'resolved', 'closed'];
        const urgencies = ['low', 'normal', 'high', 'urgent'];
        const modes = ['instant', 'normal', 'collaborative'];
        const categories = ['市政设施', '环境保护', '交通出行', '公共安全', '教育文化', '医疗卫生', '住房保障', '社会保障', '城市管理', '供水供电', '供暖燃气', '通信网络', '食品安全', '消费维权', '劳动就业', '社区服务', '政务服务', '法律咨询', '其他民生', '物业管理'];
        const districts = ['海淀区', '西城区', '东城区', '丰台区', '石景山区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区', '朝阳区'];
        const departments = ['城管委', '环保局', '交通委', '公安局', '教委', '卫健委', '民政局', '人社局', '市场监管局', '文旅局', '应急局', '水务局', '园林绿化局', '发改委', '财政局', '规自委', '商务局', '科委', '司法局', '住建委'];
        
        const tickets = [];
        
        for (let i = 1; i <= count; i++) {
            const ticketNo = `T${String(i).padStart(8, '0')}`;
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const urgency = urgencies[Math.floor(Math.random() * urgencies.length)];
            const mode = modes[Math.floor(Math.random() * modes.length)];
            const category = categories[Math.floor(Math.random() * categories.length)];
            const district = districts[Math.floor(Math.random() * districts.length)];
            const department = departments[Math.floor(Math.random() * departments.length)];
            
            const createTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
            const deadline = new Date(createTime.getTime() + 3 * 24 * 60 * 60 * 1000);
            
            tickets.push({
                ticketId: `ticket_${i}`,
                ticketNo,
                status,
                urgency,
                mode,
                callerName: `市民${String.fromCharCode(65 + Math.floor(Math.random() * 26))}**`,
                callerPhone: `1${Math.floor(Math.random() * 9) + 3}${Math.floor(Math.random() * 10)}****${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
                description: this.getRandomDescription(),
                category,
                district,
                department: `${district}${department}`,
                handler: this.getRandomHandler(),
                createTime: createTime.toISOString(),
                deadline: deadline.toISOString(),
                currentStep: this.getCurrentStep(status),
                remainingTime: getRemainingTime(deadline)
            });
        }
        
        return tickets.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
    }

    /**
     * 获取随机问题描述
     * @returns {string} 问题描述
     */
    getRandomDescription() {
        const descriptions = [
            '道路积水影响出行安全',
            '夜间施工噪音扰民投诉',
            '垃圾清运不及时影响环境',
            '路灯损坏需要维修',
            '公园健身设施损坏',
            '交通信号灯故障影响通行',
            '违章建筑举报',
            '工厂排污环境污染投诉',
            '公共设施维护不到位',
            '停车位划线不清晰',
            '公交站台破损需维修',
            '下水道堵塞积水严重',
            '小区门禁系统故障',
            '绿化带杂草丛生无人管理',
            '电梯故障频发安全隐患',
            '供暖不足室温过低',
            '自来水水压不足',
            '网络信号覆盖不良',
            '社区活动场所设施老化',
            '学校周边交通拥堵'
        ];
        return descriptions[Math.floor(Math.random() * descriptions.length)];
    }

    /**
     * 获取随机处理人
     * @returns {string} 处理人
     */
    getRandomHandler() {
        const handlers = [
            '王专员', '张主管', '刘负责人', '陈工作人员', '赵督察', '孙科长', '周处长', '吴主任',
            '郑专家', '马调研员', '胡协调员', '林检查员', '何监督员', '高管理员', '罗执法员',
            '梁服务员', '谢技术员', '宋联络员', '唐办事员', '李处理员'
        ];
        return handlers[Math.floor(Math.random() * handlers.length)];
    }

    /**
     * 获取当前环节
     * @param {string} status - 工单状态
     * @returns {string} 当前环节
     */
    getCurrentStep(status) {
        const steps = {
            draft: '草稿编辑',
            pending: '待接收',
            processing: '处理中',
            reviewing: '审核中',
            callback: '回访中',
            resolved: '已办结',
            closed: '已关闭'
        };
        return steps[status] || '未知状态';
    }

    /**
     * 获取模拟工单详情
     * @param {string} ticketId - 工单ID
     * @returns {Object} 工单详情
     */
    getMockTicketDetail(ticketId) {
        return {
            success: true,
            data: {
                ticketId,
                ticketNo: `T${String(Math.floor(Math.random() * 100000)).padStart(8, '0')}`,
                status: 'processing',
                urgency: 'normal',
                mode: 'normal',
                callerInfo: {
                    name: '张**',
                    phone: '138****1234',
                    address: '北京市海淀区中关村大街1号',
                    idCard: '110101********1234'
                },
                description: '道路积水影响出行安全，请相关部门及时处理',
                category: '市政设施',
                district: '海淀区',
                department: '海淀区城管委',
                handler: '王专员',
                createTime: new Date().toISOString(),
                deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                currentStep: '处理中',
                processHistory: [
                    {
                        step: '工单创建',
                        operator: '话务员张三',
                        time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                        remark: '市民来电反映问题，已记录工单'
                    },
                    {
                        step: '工单派发',
                        operator: '分派员李四',
                        time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
                        remark: '已派发至海淀区城管委处理'
                    }
                ]
            }
        };
    }
}

// 创建全局API实例
const ticketAPI = new TicketAPI();

// 导出API实例
if (typeof window !== 'undefined') {
    window.ticketAPI = ticketAPI;
}