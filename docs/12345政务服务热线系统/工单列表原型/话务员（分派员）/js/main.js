// 主应用入口文件

/**
 * 工单列表应用主类
 */
class TicketListApp {
    constructor() {
        this.api = null;
        this.table = null;
        this.filterManager = null;
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalCount = 0;
        this.selectedTickets = new Set();
        this.sortConfig = {
            field: 'createTime',
            order: 'desc'
        };
        
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 初始化API服务
            this.api = new TicketAPI();
            
            // 初始化表格
            this.table = new TicketTable('ticketTable', {
                onRowClick: (ticket) => this.handleRowClick(ticket),
                onSelectionChange: (selectedIds) => this.handleSelectionChange(selectedIds),
                onSort: (field, order) => this.handleSort(field, order)
            });
            
            // 初始化筛选器
            this.filterManager = new FilterManager({
                autoApply: true,
                debounceDelay: 300
            });
            
            // 绑定事件
            this.bindEvents();
            
            // 加载初始数据
            await this.loadTicketList();
            
            // 初始化统计信息
            this.updateStatistics();
            
            console.log('工单列表应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
            showNotification('应用初始化失败，请刷新页面重试', 'error');
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 筛选事件
        document.addEventListener('filtersApplied', (e) => {
            this.handleFiltersApplied(e.detail.filters);
        });
        
        // 工具栏按钮事件
        this.bindToolbarEvents();
        
        // 分页事件
        this.bindPaginationEvents();
        
        // 批量操作事件
        this.bindBatchOperationEvents();
        
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }
        
        // 导出按钮
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportData();
            });
        }
    }

    /**
     * 绑定工具栏事件
     */
    bindToolbarEvents() {
        // 新建工单
        const createBtn = document.getElementById('createTicketBtn');
        if (createBtn) {
            createBtn.addEventListener('click', () => {
                this.createTicket();
            });
        }
        
        // 批量分派
        const batchAssignBtn = document.getElementById('batchAssignBtn');
        if (batchAssignBtn) {
            batchAssignBtn.addEventListener('click', () => {
                this.batchAssignTickets();
            });
        }
        
        // 批量关闭
        const batchCloseBtn = document.getElementById('batchCloseBtn');
        if (batchCloseBtn) {
            batchCloseBtn.addEventListener('click', () => {
                this.batchCloseTickets();
            });
        }
    }

    /**
     * 绑定分页事件
     */
    bindPaginationEvents() {
        // 页码输入框
        const pageInput = document.getElementById('pageInput');
        if (pageInput) {
            pageInput.addEventListener('change', (e) => {
                const page = parseInt(e.target.value);
                if (page > 0 && page <= this.getTotalPages()) {
                    this.goToPage(page);
                }
            });
        }
        
        // 页面大小选择
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.changePageSize(parseInt(e.target.value));
            });
        }
        
        // 分页按钮
        document.addEventListener('click', (e) => {
            if (e.target.matches('.pagination-btn')) {
                const action = e.target.dataset.action;
                switch (action) {
                    case 'first':
                        this.goToPage(1);
                        break;
                    case 'prev':
                        this.goToPage(Math.max(1, this.currentPage - 1));
                        break;
                    case 'next':
                        this.goToPage(Math.min(this.getTotalPages(), this.currentPage + 1));
                        break;
                    case 'last':
                        this.goToPage(this.getTotalPages());
                        break;
                    default:
                        if (!isNaN(action)) {
                            this.goToPage(parseInt(action));
                        }
                }
            }
        });
    }

    /**
     * 绑定批量操作事件
     */
    bindBatchOperationEvents() {
        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.handleSelectAll(e.target.checked);
            });
        }
    }

    /**
     * 加载工单列表
     * @param {boolean} showLoading - 是否显示加载状态
     */
    async loadTicketList(showLoading = true) {
        try {
            if (showLoading) {
                this.showTableLoading();
            }
            
            const filters = this.filterManager.getActiveFilters();
            const params = {
                page: this.currentPage,
                pageSize: this.pageSize,
                sortField: this.sortConfig.field,
                sortOrder: this.sortConfig.order,
                ...filters
            };
            
            const response = await this.api.getTicketList(params);
            
            if (response.success) {
                this.totalCount = response.data.total;
                this.table.render(response.data.list);
                this.updatePagination();
                this.updateStatistics();
            } else {
                throw new Error(response.message || '获取工单列表失败');
            }
        } catch (error) {
            console.error('加载工单列表失败:', error);
            showNotification('加载工单列表失败: ' + error.message, 'error');
            this.table.showEmpty('加载失败，请重试');
        } finally {
            this.hideTableLoading();
        }
    }

    /**
     * 处理筛选应用
     * @param {Object} filters - 筛选条件
     */
    async handleFiltersApplied(filters) {
        this.currentPage = 1; // 重置到第一页
        await this.loadTicketList();
    }

    /**
     * 处理排序
     * @param {string} field - 排序字段
     * @param {string} order - 排序方向
     */
    async handleSort(field, order) {
        this.sortConfig = { field, order };
        this.currentPage = 1; // 重置到第一页
        await this.loadTicketList();
    }

    /**
     * 处理行点击
     * @param {Object} ticket - 工单数据
     */
    handleRowClick(ticket) {
        const modal = createTicketDetailModal(ticket);
        showModal(modal);
    }

    /**
     * 处理选择变化
     * @param {Array} selectedIds - 选中的ID列表
     */
    handleSelectionChange(selectedIds) {
        this.selectedTickets = new Set(selectedIds);
        this.updateBatchOperationButtons();
        this.updateSelectAllCheckbox();
    }

    /**
     * 处理全选
     * @param {boolean} checked - 是否选中
     */
    handleSelectAll(checked) {
        this.table.selectAll(checked);
    }

    /**
     * 跳转到指定页面
     * @param {number} page - 页码
     */
    async goToPage(page) {
        if (page !== this.currentPage && page > 0 && page <= this.getTotalPages()) {
            this.currentPage = page;
            await this.loadTicketList();
        }
    }

    /**
     * 改变页面大小
     * @param {number} pageSize - 页面大小
     */
    async changePageSize(pageSize) {
        this.pageSize = pageSize;
        this.currentPage = 1; // 重置到第一页
        await this.loadTicketList();
    }

    /**
     * 获取总页数
     * @returns {number} 总页数
     */
    getTotalPages() {
        return Math.ceil(this.totalCount / this.pageSize);
    }

    /**
     * 更新分页器
     */
    updatePagination() {
        const totalPages = this.getTotalPages();
        
        // 更新页码信息
        const pageInfo = document.getElementById('pageInfo');
        if (pageInfo) {
            const start = (this.currentPage - 1) * this.pageSize + 1;
            const end = Math.min(this.currentPage * this.pageSize, this.totalCount);
            pageInfo.textContent = `显示 ${start}-${end} 条，共 ${this.totalCount} 条`;
        }
        
        // 更新页码输入框
        const pageInput = document.getElementById('pageInput');
        if (pageInput) {
            pageInput.value = this.currentPage;
            pageInput.max = totalPages;
        }
        
        // 更新总页数显示
        const totalPagesSpan = document.getElementById('totalPages');
        if (totalPagesSpan) {
            totalPagesSpan.textContent = totalPages;
        }
        
        // 更新分页按钮状态
        this.updatePaginationButtons();
    }

    /**
     * 更新分页按钮状态
     */
    updatePaginationButtons() {
        const totalPages = this.getTotalPages();
        
        // 禁用/启用按钮
        const firstBtn = document.querySelector('[data-action="first"]');
        const prevBtn = document.querySelector('[data-action="prev"]');
        const nextBtn = document.querySelector('[data-action="next"]');
        const lastBtn = document.querySelector('[data-action="last"]');
        
        if (firstBtn) firstBtn.disabled = this.currentPage === 1;
        if (prevBtn) prevBtn.disabled = this.currentPage === 1;
        if (nextBtn) nextBtn.disabled = this.currentPage === totalPages;
        if (lastBtn) lastBtn.disabled = this.currentPage === totalPages;
    }

    /**
     * 更新统计信息
     */
    async updateStatistics() {
        try {
            const stats = await this.api.getTicketStatistics();
            if (stats.success) {
                this.renderStatistics(stats.data);
            }
        } catch (error) {
            console.error('获取统计信息失败:', error);
        }
    }

    /**
     * 渲染统计信息
     * @param {Object} stats - 统计数据
     */
    renderStatistics(stats) {
        const statsContainer = document.querySelector('.stats-bar');
        if (!statsContainer) return;
        
        statsContainer.innerHTML = `
            <div class="stat-item">
                <span class="stat-label">总计:</span>
                <span class="stat-value">${stats.total || 0}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">待接收:</span>
                <span class="stat-value pending">${stats.pending || 0}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">处理中:</span>
                <span class="stat-value processing">${stats.processing || 0}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">已办结:</span>
                <span class="stat-value resolved">${stats.resolved || 0}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">超时:</span>
                <span class="stat-value overdue">${stats.overdue || 0}</span>
            </div>
        `;
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchOperationButtons() {
        const hasSelection = this.selectedTickets.size > 0;
        
        const batchButtons = document.querySelectorAll('.batch-operation-btn');
        batchButtons.forEach(btn => {
            btn.disabled = !hasSelection;
        });
        
        // 更新选择计数
        const selectionCount = document.getElementById('selectionCount');
        if (selectionCount) {
            if (hasSelection) {
                selectionCount.textContent = `已选择 ${this.selectedTickets.size} 项`;
                selectionCount.style.display = 'inline';
            } else {
                selectionCount.style.display = 'none';
            }
        }
    }

    /**
     * 更新全选复选框状态
     */
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAll');
        if (!selectAllCheckbox) return;
        
        const currentPageTickets = this.table.getCurrentTickets();
        const currentPageIds = currentPageTickets.map(ticket => ticket.id);
        const selectedInCurrentPage = currentPageIds.filter(id => this.selectedTickets.has(id));
        
        if (selectedInCurrentPage.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedInCurrentPage.length === currentPageIds.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading() {
        const tableBody = document.getElementById('ticketListBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr class="loading-row">
                    <td colspan="10" class="text-center">
                        <div class="loading-spinner"></div>
                        <span>加载中...</span>
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 隐藏表格加载状态
     */
    hideTableLoading() {
        // 由table.render()方法处理
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        await this.loadTicketList();
        showNotification('数据已刷新', 'success');
    }

    /**
     * 导出数据
     */
    async exportData() {
        try {
            showLoading('正在导出数据...');
            
            const filters = this.filterManager.getActiveFilters();
            const params = {
                sortField: this.sortConfig.field,
                sortOrder: this.sortConfig.order,
                ...filters
            };
            
            const response = await this.api.exportTickets(params);
            
            if (response.success) {
                // 创建下载链接
                const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `工单列表_${formatDateTime(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.csv`;
                link.click();
                
                showNotification('导出成功', 'success');
            } else {
                throw new Error(response.message || '导出失败');
            }
        } catch (error) {
            console.error('导出失败:', error);
            showNotification('导出失败: ' + error.message, 'error');
        } finally {
            hideLoading();
        }
    }

    /**
     * 创建工单
     */
    createTicket() {
        // 这里可以打开创建工单的模态框或跳转到创建页面
        showNotification('创建工单功能开发中...', 'info');
    }

    /**
     * 批量分派工单
     */
    async batchAssignTickets() {
        if (this.selectedTickets.size === 0) {
            showNotification('请先选择要分派的工单', 'warning');
            return;
        }
        
        const confirmed = await createConfirmDialog({
            title: '批量分派',
            message: `确定要分派选中的 ${this.selectedTickets.size} 个工单吗？`,
            type: 'info'
        });
        
        if (confirmed) {
            try {
                showLoading('正在分派工单...');
                
                const ticketIds = Array.from(this.selectedTickets);
                const response = await this.api.batchAssignTickets(ticketIds, {
                    department: '市政管理局', // 这里应该从表单获取
                    handler: '张三' // 这里应该从表单获取
                });
                
                if (response.success) {
                    showNotification('批量分派成功', 'success');
                    this.selectedTickets.clear();
                    await this.loadTicketList();
                } else {
                    throw new Error(response.message || '批量分派失败');
                }
            } catch (error) {
                console.error('批量分派失败:', error);
                showNotification('批量分派失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }
    }

    /**
     * 批量关闭工单
     */
    async batchCloseTickets() {
        if (this.selectedTickets.size === 0) {
            showNotification('请先选择要关闭的工单', 'warning');
            return;
        }
        
        const confirmed = await createConfirmDialog({
            title: '批量关闭',
            message: `确定要关闭选中的 ${this.selectedTickets.size} 个工单吗？`,
            type: 'warning'
        });
        
        if (confirmed) {
            try {
                showLoading('正在关闭工单...');
                
                const ticketIds = Array.from(this.selectedTickets);
                const response = await this.api.batchCloseTickets(ticketIds);
                
                if (response.success) {
                    showNotification('批量关闭成功', 'success');
                    this.selectedTickets.clear();
                    await this.loadTicketList();
                } else {
                    throw new Error(response.message || '批量关闭失败');
                }
            } catch (error) {
                console.error('批量关闭失败:', error);
                showNotification('批量关闭失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }
    }

    /**
     * 获取选中的工单
     * @returns {Array} 选中的工单ID列表
     */
    getSelectedTickets() {
        return Array.from(this.selectedTickets);
    }

    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedTickets.clear();
        this.table.clearSelection();
        this.updateBatchOperationButtons();
        this.updateSelectAllCheckbox();
    }
}

// 全局应用实例
let ticketListApp;

// DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    ticketListApp = new TicketListApp();
    
    // 将应用实例挂载到window对象，方便调试
    window.TicketListApp = ticketListApp;
});

// 导出应用类
if (typeof window !== 'undefined') {
    window.TicketListAppClass = TicketListApp;
}