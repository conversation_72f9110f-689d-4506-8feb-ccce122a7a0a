// 工具函数库

/**
 * 格式化日期时间
 * @param {Date|string} date - 日期对象或日期字符串
 * @param {string} format - 格式化模式
 * @returns {string} 格式化后的日期字符串
 */
function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 计算相对时间
 * @param {Date|string} date - 日期对象或日期字符串
 * @returns {string} 相对时间描述
 */
function getRelativeTime(date) {
    if (!date) return '';
    
    const now = new Date();
    const target = new Date(date);
    const diff = now.getTime() - target.getTime();
    
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    const week = 7 * day;
    const month = 30 * day;
    
    if (diff < minute) {
        return '刚刚';
    } else if (diff < hour) {
        return Math.floor(diff / minute) + '分钟前';
    } else if (diff < day) {
        return Math.floor(diff / hour) + '小时前';
    } else if (diff < week) {
        return Math.floor(diff / day) + '天前';
    } else if (diff < month) {
        return Math.floor(diff / week) + '周前';
    } else {
        return Math.floor(diff / month) + '个月前';
    }
}

/**
 * 计算剩余时间
 * @param {Date|string} deadline - 截止时间
 * @returns {Object} 剩余时间信息
 */
function getRemainingTime(deadline) {
    if (!deadline) return { text: '', status: 'normal', isOvertime: false };
    
    const now = new Date();
    const target = new Date(deadline);
    const diff = target.getTime() - now.getTime();
    
    if (diff <= 0) {
        const overTime = Math.abs(diff);
        const hours = Math.floor(overTime / (1000 * 60 * 60));
        const days = Math.floor(hours / 24);
        
        if (days > 0) {
            return { text: `超时${days}天`, status: 'overtime', isOvertime: true };
        } else {
            return { text: `超时${hours}小时`, status: 'overtime', isOvertime: true };
        }
    }
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    let status = 'normal';
    if (hours <= 2) {
        status = 'danger';
    } else if (hours <= 8) {
        status = 'warning';
    }
    
    if (days > 0) {
        return { text: `${days}天${hours % 24}小时`, status, isOvertime: false };
    } else {
        return { text: `${hours}小时`, status, isOvertime: false };
    }
}

/**
 * 脱敏处理手机号
 * @param {string} phone - 手机号
 * @returns {string} 脱敏后的手机号
 */
function maskPhone(phone) {
    if (!phone || phone.length < 7) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * 脱敏处理姓名
 * @param {string} name - 姓名
 * @returns {string} 脱敏后的姓名
 */
function maskName(name) {
    if (!name || name.length < 2) return name;
    if (name.length === 2) {
        return name[0] + '*';
    }
    return name[0] + '*'.repeat(name.length - 2) + name[name.length - 1];
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 获取URL参数
 * @param {string} name - 参数名
 * @returns {string|null} 参数值
 */
function getUrlParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * 设置URL参数
 * @param {string} name - 参数名
 * @param {string} value - 参数值
 */
function setUrlParam(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.replaceState({}, '', url);
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, warning, error, info)
 * @param {number} duration - 显示时长（毫秒）
 */
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-header">
            <span class="notification-title">${getNotificationTitle(type)}</span>
            <button class="notification-close">×</button>
        </div>
        <div class="notification-content">${message}</div>
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => notification.classList.add('show'), 100);
    
    // 关闭按钮事件
    notification.querySelector('.notification-close').addEventListener('click', () => {
        hideNotification(notification);
    });
    
    // 自动关闭
    if (duration > 0) {
        setTimeout(() => hideNotification(notification), duration);
    }
}

/**
 * 隐藏通知消息
 * @param {HTMLElement} notification - 通知元素
 */
function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * 获取通知标题
 * @param {string} type - 通知类型
 * @returns {string} 标题
 */
function getNotificationTitle(type) {
    const titles = {
        success: '成功',
        warning: '警告',
        error: '错误',
        info: '提示'
    };
    return titles[type] || '提示';
}

/**
 * 确认对话框
 * @param {string} message - 确认消息
 * @param {string} title - 对话框标题
 * @param {string} type - 对话框类型
 * @returns {Promise<boolean>} 用户选择结果
 */
function showConfirm(message, title = '确认', type = 'warning') {
    return new Promise((resolve) => {
        const modal = createModal({
            title: title,
            size: 'small',
            content: `
                <div class="confirm-dialog">
                    <div class="confirm-icon ${type}">
                        ${getConfirmIcon(type)}
                    </div>
                    <div class="confirm-title">${title}</div>
                    <div class="confirm-message">${message}</div>
                </div>
            `,
            footer: `
                <button class="btn btn-outline" data-action="cancel">取消</button>
                <button class="btn btn-primary" data-action="confirm">确认</button>
            `
        });
        
        modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
            hideModal(modal);
            resolve(false);
        });
        
        modal.querySelector('[data-action="confirm"]').addEventListener('click', () => {
            hideModal(modal);
            resolve(true);
        });
        
        showModal(modal);
    });
}

/**
 * 获取确认图标
 * @param {string} type - 图标类型
 * @returns {string} 图标HTML
 */
function getConfirmIcon(type) {
    const icons = {
        warning: '⚠️',
        danger: '❌',
        info: 'ℹ️',
        success: '✅'
    };
    return icons[type] || 'ℹ️';
}

/**
 * 加载对话框
 * @param {string} message - 加载消息
 * @returns {HTMLElement} 模态框元素
 */
function showLoading(message = '正在处理...') {
    const modal = createModal({
        title: '',
        size: 'small',
        content: `
            <div class="loading-dialog">
                <div class="loading-spinner-large"></div>
                <div class="loading-text">${message}</div>
            </div>
        `,
        footer: '',
        closable: false
    });
    
    showModal(modal);
    return modal;
}

/**
 * 隐藏加载对话框
 * @param {HTMLElement} modal - 模态框元素
 */
function hideLoading(modal) {
    if (modal) {
        hideModal(modal);
    }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 验证表单
 * @param {HTMLFormElement} form - 表单元素
 * @returns {boolean} 验证结果
 */
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        const value = field.value.trim();
        const errorElement = field.parentNode.querySelector('.form-error');
        
        if (!value) {
            field.classList.add('error');
            if (errorElement) {
                errorElement.textContent = '此字段为必填项';
            }
            isValid = false;
        } else {
            field.classList.remove('error');
            if (errorElement) {
                errorElement.textContent = '';
            }
        }
    });
    
    return isValid;
}

/**
 * 清除表单验证状态
 * @param {HTMLFormElement} form - 表单元素
 */
function clearFormValidation(form) {
    const fields = form.querySelectorAll('.form-control');
    const errors = form.querySelectorAll('.form-error');
    
    fields.forEach(field => field.classList.remove('error'));
    errors.forEach(error => error.textContent = '');
}

/**
 * 导出数据为CSV
 * @param {Array} data - 数据数组
 * @param {string} filename - 文件名
 */
function exportToCSV(data, filename = 'export.csv') {
    if (!data || data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 本地存储工具
 */
const storage = {
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (e) {
            console.error('存储失败:', e);
        }
    },
    
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            console.error('读取失败:', e);
            return defaultValue;
        }
    },
    
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.error('删除失败:', e);
        }
    },
    
    clear() {
        try {
            localStorage.clear();
        } catch (e) {
            console.error('清空失败:', e);
        }
    }
};

// 导出工具函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatDateTime,
        getRelativeTime,
        getRemainingTime,
        maskPhone,
        maskName,
        debounce,
        throttle,
        deepClone,
        generateId,
        getUrlParam,
        setUrlParam,
        showNotification,
        showConfirm,
        showLoading,
        hideLoading,
        formatFileSize,
        validateForm,
        clearFormValidation,
        exportToCSV,
        storage
    };
}