// 表格管理功能

/**
 * 工单表格管理类
 */
class TicketTable {
    constructor(tableId, options = {}) {
        this.tableId = tableId;
        this.table = document.getElementById(tableId);
        
        if (!this.table) {
            throw new Error(`Table with id '${tableId}' not found`);
        }
        
        this.tbody = this.table.querySelector('tbody');
        if (!this.tbody) {
            throw new Error(`Table body not found in table '${tableId}'`);
        }
        
        this.selectAllCheckbox = document.getElementById('selectAll');
        
        this.options = {
            pageSize: 20,
            sortField: 'createTime',
            sortOrder: 'desc',
            ...options
        };
        
        this.currentData = [];
        this.selectedRows = new Set();
        this.sortConfig = {
            field: this.options.sortField,
            order: this.options.sortOrder
        };
        
        this.init();
    }

    /**
     * 初始化表格
     */
    init() {
        this.bindEvents();
        this.initSorting();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 全选/取消全选
        if (this.selectAllCheckbox) {
            this.selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }

        // 行选择事件委托
        this.tbody.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.classList.contains('row-checkbox')) {
                this.toggleRowSelection(e.target.value, e.target.checked);
            }
        });

        // 行点击事件委托
        this.tbody.addEventListener('click', (e) => {
            const target = e.target;
            const row = target.closest('tr');
            
            if (!row) return;
            
            // 处理操作按钮点击
            if (target.classList.contains('btn-action')) {
                e.stopPropagation();
                this.handleActionClick(target, row);
                return;
            }
            
            // 处理行点击（查看详情）
            if (!target.type === 'checkbox') {
                this.handleRowClick(row);
            }
        });
    }

    /**
     * 初始化排序功能
     */
    initSorting() {
        const sortableHeaders = this.table.querySelectorAll('th.sortable');
        
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const field = header.dataset.sort;
                this.handleSort(field);
            });
        });
    }

    /**
     * 渲染表格数据
     * @param {Array} data - 工单数据
     */
    render(data) {
        this.currentData = data;
        this.tbody.innerHTML = '';
        
        if (!data || data.length === 0) {
            this.renderEmptyState();
            return;
        }
        
        data.forEach(ticket => {
            const row = this.createTableRow(ticket);
            this.tbody.appendChild(row);
        });
        
        this.updateSelectAllState();
    }

    /**
     * 创建表格行
     * @param {Object} ticket - 工单数据
     * @returns {HTMLElement} 表格行元素
     */
    createTableRow(ticket) {
        const row = document.createElement('tr');
        row.dataset.ticketId = ticket.ticketId;
        row.className = this.getRowClassName(ticket);
        
        row.innerHTML = `
            <td class="checkbox-col">
                <input type="checkbox" class="row-checkbox" value="${ticket.ticketId}" 
                       ${this.selectedRows.has(ticket.ticketId) ? 'checked' : ''}>
            </td>
            <td class="ticket-no">
                <span class="ticket-link" data-ticket-id="${ticket.ticketId}">${ticket.ticketNo}</span>
            </td>
            <td class="status-col">
                <span class="status-badge ${ticket.status}">${this.getStatusText(ticket.status)}</span>
            </td>
            <td class="urgency-col">
                <span class="urgency-badge ${ticket.urgency}">${this.getUrgencyText(ticket.urgency)}</span>
            </td>
            <td class="caller-info">
                <div class="caller-name">${ticket.callerName}</div>
                <div class="caller-phone">${ticket.callerPhone}</div>
            </td>
            <td class="description">
                <div class="description-text" title="${ticket.description}">
                    ${this.truncateText(ticket.description, 30)}
                </div>
                <div class="category-info">
                    <span class="category">${ticket.category}</span>
                    <span class="district">${ticket.district}</span>
                </div>
            </td>
            <td class="mode-col">
                <span class="mode-badge ${ticket.mode}">${this.getModeText(ticket.mode)}</span>
                ${ticket.department ? `<div class="department">${ticket.department}</div>` : ''}
            </td>
            <td class="time-col">
                <div class="create-time">${getRelativeTime(ticket.createTime)}</div>
                <div class="exact-time" title="${formatDateTime(ticket.createTime)}">
                    ${formatDateTime(ticket.createTime, 'MM-DD HH:mm')}
                </div>
            </td>
            <td class="remaining-col">
                <span class="remaining-time ${ticket.remainingTime.status}">
                    ${ticket.remainingTime.text}
                </span>
            </td>
            <td class="step-col">
                <span class="current-step">${ticket.currentStep}</span>
                ${ticket.handler ? `<div class="handler">处理人：${ticket.handler}</div>` : ''}
            </td>
            <td class="actions-col">
                ${this.createActionButtons(ticket)}
            </td>
        `;
        
        return row;
    }

    /**
     * 创建操作按钮
     * @param {Object} ticket - 工单数据
     * @returns {string} 按钮HTML
     */
    createActionButtons(ticket) {
        const buttons = [];
        
        // 根据工单状态显示不同操作
        switch (ticket.status) {
            case 'draft':
                buttons.push(`<button class="action-btn-sm" data-action="edit" data-ticket-id="${ticket.ticketId}">编辑</button>`);
                buttons.push(`<button class="action-btn-sm primary" data-action="submit" data-ticket-id="${ticket.ticketId}">提交</button>`);
                break;
                
            case 'pending':
                buttons.push(`<button class="action-btn-sm primary" data-action="assign" data-ticket-id="${ticket.ticketId}">派发</button>`);
                buttons.push(`<button class="action-btn-sm success" data-action="instant" data-ticket-id="${ticket.ticketId}">办结</button>`);
                break;
                
            case 'processing':
                buttons.push(`<button class="action-btn-sm warning" data-action="urge" data-ticket-id="${ticket.ticketId}">催办</button>`);
                break;
                
            case 'reviewing':
                buttons.push(`<button class="action-btn-sm success" data-action="approve" data-ticket-id="${ticket.ticketId}">审核</button>`);
                break;
                
            case 'callback':
                buttons.push(`<button class="action-btn-sm primary" data-action="callback" data-ticket-id="${ticket.ticketId}">回访</button>`);
                break;
        }
        
        // 通用操作
        buttons.push(`<button class="action-btn-sm" data-action="detail" data-ticket-id="${ticket.ticketId}">详情</button>`);
        
        return buttons.join('');
    }

    /**
     * 获取行样式类名
     * @param {Object} ticket - 工单数据
     * @returns {string} 样式类名
     */
    getRowClassName(ticket) {
        const classes = ['ticket-row'];
        
        if (ticket.urgency === 'urgent') {
            classes.push('urgent-row');
        } else if (ticket.urgency === 'high') {
            classes.push('high-priority-row');
        }
        
        if (ticket.remainingTime.isOvertime) {
            classes.push('overtime-row');
        }
        
        if (this.selectedRows.has(ticket.ticketId)) {
            classes.push('selected-row');
        }
        
        return classes.join(' ');
    }

    /**
     * 获取状态文本
     * @param {string} status - 状态值
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            draft: '草稿',
            pending: '待派单',
            processing: '处理中',
            reviewing: '待审核',
            callback: '待回访',
            resolved: '已办结',
            closed: '已关闭'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取紧急程度文本
     * @param {string} urgency - 紧急程度值
     * @returns {string} 紧急程度文本
     */
    getUrgencyText(urgency) {
        const urgencyMap = {
            low: '一般',
            normal: '普通',
            high: '紧急',
            urgent: '特急'
        };
        return urgencyMap[urgency] || urgency;
    }

    /**
     * 获取处理模式文本
     * @param {string} mode - 模式值
     * @returns {string} 模式文本
     */
    getModeText(mode) {
        const modeMap = {
            instant: '即时办结',
            normal: '普通流转',
            collaborative: '协同处理'
        };
        return modeMap[mode] || mode;
    }

    /**
     * 截断文本
     * @param {string} text - 原文本
     * @param {number} maxLength - 最大长度
     * @returns {string} 截断后的文本
     */
    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        this.tbody.innerHTML = `
            <tr class="empty-row">
                <td colspan="11" class="empty-cell">
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <div class="empty-text">暂无工单数据</div>
                        <div class="empty-hint">您可以创建新工单或调整筛选条件</div>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 处理排序
     * @param {string} field - 排序字段
     */
    handleSort(field) {
        if (this.sortConfig.field === field) {
            // 切换排序方向
            this.sortConfig.order = this.sortConfig.order === 'asc' ? 'desc' : 'asc';
        } else {
            // 新字段，默认降序
            this.sortConfig.field = field;
            this.sortConfig.order = 'desc';
        }
        
        this.updateSortIcons();
        this.triggerSort();
    }

    /**
     * 更新排序图标
     */
    updateSortIcons() {
        const headers = this.table.querySelectorAll('th.sortable');
        
        headers.forEach(header => {
            const icon = header.querySelector('.sort-icon');
            const field = header.dataset.sort;
            
            if (field === this.sortConfig.field) {
                icon.textContent = this.sortConfig.order === 'asc' ? '↑' : '↓';
                header.classList.add('sorted');
            } else {
                icon.textContent = '↕️';
                header.classList.remove('sorted');
            }
        });
    }

    /**
     * 触发排序事件
     */
    triggerSort() {
        const event = new CustomEvent('tableSort', {
            detail: {
                field: this.sortConfig.field,
                order: this.sortConfig.order
            }
        });
        this.table.dispatchEvent(event);
    }

    /**
     * 切换全选状态
     * @param {boolean} checked - 是否选中
     */
    toggleSelectAll(checked) {
        const checkboxes = this.tbody.querySelectorAll('.row-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            this.toggleRowSelection(checkbox.value, checked);
        });
        
        this.updateBatchActions();
    }

    /**
     * 切换行选择状态
     * @param {string} ticketId - 工单ID
     * @param {boolean} checked - 是否选中
     */
    toggleRowSelection(ticketId, checked) {
        const row = this.tbody.querySelector(`tr[data-ticket-id="${ticketId}"]`);
        
        if (checked) {
            this.selectedRows.add(ticketId);
            row?.classList.add('selected-row');
        } else {
            this.selectedRows.delete(ticketId);
            row?.classList.remove('selected-row');
        }
        
        this.updateSelectAllState();
        this.updateBatchActions();
    }

    /**
     * 更新全选状态
     */
    updateSelectAllState() {
        if (!this.selectAllCheckbox) return;
        
        const checkboxes = this.tbody.querySelectorAll('.row-checkbox');
        const checkedCount = this.selectedRows.size;
        const totalCount = checkboxes.length;
        
        if (checkedCount === 0) {
            this.selectAllCheckbox.checked = false;
            this.selectAllCheckbox.indeterminate = false;
        } else if (checkedCount === totalCount) {
            this.selectAllCheckbox.checked = true;
            this.selectAllCheckbox.indeterminate = false;
        } else {
            this.selectAllCheckbox.checked = false;
            this.selectAllCheckbox.indeterminate = true;
        }
    }

    /**
     * 更新批量操作按钮状态
     */
    updateBatchActions() {
        const batchActions = document.getElementById('batchActions');
        if (!batchActions) return;
        
        const selectedCount = this.selectedRows.size;
        
        if (selectedCount > 0) {
            batchActions.style.display = 'flex';
            
            // 更新按钮文本显示选中数量
            const buttons = batchActions.querySelectorAll('button');
            buttons.forEach(button => {
                const originalText = button.dataset.originalText || button.textContent;
                button.dataset.originalText = originalText;
                button.innerHTML = `${originalText} (${selectedCount})`;
            });
        } else {
            batchActions.style.display = 'none';
        }
        
        // 触发选择变化事件
        const event = new CustomEvent('selectionChange', {
            detail: {
                selectedIds: Array.from(this.selectedRows),
                selectedCount: selectedCount
            }
        });
        this.table.dispatchEvent(event);
    }

    /**
     * 处理操作按钮点击
     * @param {HTMLElement} button - 按钮元素
     * @param {HTMLElement} row - 行元素
     */
    handleActionClick(button, row) {
        const action = button.dataset.action;
        const ticketId = button.dataset.ticketId;
        
        const event = new CustomEvent('actionClick', {
            detail: {
                action,
                ticketId,
                button,
                row
            }
        });
        this.table.dispatchEvent(event);
    }

    /**
     * 处理行点击
     * @param {HTMLElement} row - 行元素
     */
    handleRowClick(row) {
        const ticketId = row.dataset.ticketId;
        
        const event = new CustomEvent('rowClick', {
            detail: {
                ticketId,
                row
            }
        });
        this.table.dispatchEvent(event);
    }

    /**
     * 获取选中的工单ID
     * @returns {Array} 选中的工单ID数组
     */
    getSelectedIds() {
        return Array.from(this.selectedRows);
    }

    /**
     * 清除选择
     */
    clearSelection() {
        this.selectedRows.clear();
        const checkboxes = this.tbody.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = false);
        
        const rows = this.tbody.querySelectorAll('.selected-row');
        rows.forEach(row => row.classList.remove('selected-row'));
        
        this.updateSelectAllState();
        this.updateBatchActions();
    }

    /**
     * 刷新表格
     */
    refresh() {
        this.clearSelection();
        // 触发刷新事件
        const event = new CustomEvent('tableRefresh');
        this.table.dispatchEvent(event);
    }

    /**
     * 获取当前排序配置
     * @returns {Object} 排序配置
     */
    getSortConfig() {
        return { ...this.sortConfig };
    }

    /**
     * 设置排序配置
     * @param {string} field - 排序字段
     * @param {string} order - 排序方向
     */
    setSortConfig(field, order) {
        this.sortConfig.field = field;
        this.sortConfig.order = order;
        this.updateSortIcons();
    }
}

// 导出表格类
if (typeof window !== 'undefined') {
    window.TicketTable = TicketTable;
}