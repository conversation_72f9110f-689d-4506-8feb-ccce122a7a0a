<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12345热线工单管理系统 - 话务员工作台</title>
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/table.css">
    <link rel="stylesheet" href="css/modal.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-left">
            <div class="logo">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iOCIgZmlsbD0iIzE4OTBGRiIvPgo8cGF0aCBkPSJNOCAxMkgxNlYyMEg4VjEyWiIgZmlsbD0id2hpdGUiLz4KPHA+dGggZD0iTTIwIDhIMjhWMTZIMjBWOFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMCAyMEgyOFYyOEgyMFYyMFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=" alt="12345">
                <span>12345热线工单系统</span>
            </div>
        </div>
        <div class="header-center">
            <div class="user-info">
                <span class="role-badge">话务员</span>
                <span class="user-name">张小明</span>
                <span class="dept-name">市级12345中心</span>
            </div>
        </div>
        <div class="header-right">
            <div class="header-actions">
                <button class="btn-icon" title="通知">
                    <span class="icon">🔔</span>
                    <span class="badge">3</span>
                </button>
                <button class="btn-icon" title="设置">
                    <span class="icon">⚙️</span>
                </button>
                <button class="btn-icon" title="退出">
                    <span class="icon">🚪</span>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <nav class="nav-menu">
                <div class="nav-item active">
                    <span class="icon">📋</span>
                    <span>工单列表</span>
                    <span class="count">156</span>
                </div>
                <div class="nav-item">
                    <span class="icon">➕</span>
                    <span>新建工单</span>
                </div>
                <div class="nav-item">
                    <span class="icon">⚡</span>
                    <span>即时办结</span>
                    <span class="count">23</span>
                </div>
                <div class="nav-item">
                    <span class="icon">🔄</span>
                    <span>待派单</span>
                    <span class="count urgent">12</span>
                </div>
                <div class="nav-item">
                    <span class="icon">🔗</span>
                    <span>工单合并</span>
                </div>
                <div class="nav-item">
                    <span class="icon">📊</span>
                    <span>统计报表</span>
                </div>
            </nav>
        </aside>

        <!-- 工单列表区域 -->
        <section class="content-area">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <button class="btn btn-primary" id="newTicketBtn">
                        <span class="icon">➕</span>
                        新建工单
                    </button>
                    <button class="btn btn-secondary" id="refreshBtn">
                        <span class="icon">🔄</span>
                        刷新
                    </button>
                    <div class="batch-actions" id="batchActions" style="display: none;">
                        <button class="btn btn-warning" id="batchInstantResolveBtn">
                            <span class="icon">⚡</span>
                            批量即时办结
                        </button>
                        <button class="btn btn-info" id="batchAssignBtn">
                            <span class="icon">📤</span>
                            批量派单
                        </button>
                        <button class="btn btn-success" id="batchMergeBtn">
                            <span class="icon">🔗</span>
                            批量合并
                        </button>
                    </div>
                </div>
                <div class="toolbar-right">
                    <div class="search-box">
                        <input type="text" placeholder="搜索工单编号、市民姓名、问题描述..." id="searchInput">
                        <button class="btn-search" id="searchBtn">
                            <span class="icon">🔍</span>
                        </button>
                    </div>
                    <button class="btn btn-outline" id="advancedSearchBtn">
                        高级搜索
                    </button>
                    <button class="btn btn-outline" id="exportBtn">
                        <span class="icon">📥</span>
                        导出
                    </button>
                </div>
            </div>

            <!-- 筛选栏 -->
            <div class="filter-bar">
                <div class="filter-group">
                    <label>工单状态：</label>
                    <select id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="draft">草稿/暂存</option>
                        <option value="pending">待接收</option>
                        <option value="processing">处理中</option>
                        <option value="reviewing">待审核</option>
                        <option value="callback">待回访</option>
                        <option value="closed">已关闭</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>紧急程度：</label>
                    <select id="urgencyFilter">
                        <option value="">全部</option>
                        <option value="urgent">特急</option>
                        <option value="high">紧急</option>
                        <option value="normal">一般</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>处理模式：</label>
                    <select id="modeFilter">
                        <option value="">全部模式</option>
                        <option value="instant">即时办结</option>
                        <option value="normal">普通流转</option>
                        <option value="collaborative">多部门协同</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>时间范围：</label>
                    <input type="date" id="startDate">
                    <span>至</span>
                    <input type="date" id="endDate">
                </div>
                <button class="btn btn-primary" id="applyFilterBtn">应用筛选</button>
                <button class="btn btn-outline" id="clearFilterBtn">清除</button>
            </div>

            <!-- 统计信息栏 -->
            <div class="stats-bar">
                <div class="stat-item">
                    <span class="stat-label">今日新增：</span>
                    <span class="stat-value">45</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">待处理：</span>
                    <span class="stat-value urgent">23</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">即时办结：</span>
                    <span class="stat-value">156</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">超时预警：</span>
                    <span class="stat-value warning">8</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">满意度：</span>
                    <span class="stat-value success">98.5%</span>
                </div>
            </div>

            <!-- 工单表格 -->
            <div class="table-container">
                <table class="ticket-table" id="ticketTable">
                    <thead>
                        <tr>
                            <th class="checkbox-col">
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th class="sortable" data-sort="ticketNo">
                                工单编号
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="status">
                                状态
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th class="sortable" data-sort="urgency">
                                紧急程度
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th>市民信息</th>
                            <th>问题描述</th>
                            <th>处理模式</th>
                            <th class="sortable" data-sort="createTime">
                                创建时间
                                <span class="sort-icon">↕️</span>
                            </th>
                            <th>剩余时间</th>
                            <th>当前环节</th>
                            <th class="actions-col">操作</th>
                        </tr>
                    </thead>
                    <tbody id="ticketTableBody">
                        <!-- 工单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页器 -->
            <div class="pagination">
                <div class="pagination-info">
                    共 <span id="totalCount">0</span> 条记录，第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页
                </div>
                <div class="pagination-controls">
                    <button class="btn-page" id="firstPageBtn">首页</button>
                    <button class="btn-page" id="prevPageBtn">上一页</button>
                    <span class="page-numbers" id="pageNumbers"></span>
                    <button class="btn-page" id="nextPageBtn">下一页</button>
                    <button class="btn-page" id="lastPageBtn">末页</button>
                    <select id="pageSizeSelect">
                        <option value="20">20条/页</option>
                        <option value="50">50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                </div>
            </div>
        </section>
    </main>

    <!-- 模态框容器 -->
    <div id="modalContainer"></div>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>
    <script src="js/table.js"></script>
    <script src="js/filters.js"></script>
    <script src="js/modals.js"></script>
    <script src="js/main.js"></script>
</body>
</html>