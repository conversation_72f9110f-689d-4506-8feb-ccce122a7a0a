/**
 * 12345政务服务热线系统 - 相关工单组件
 * @description 显示和管理相关工单信息
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 相关工单组件
 */
const RelatedTicketsComponent = {
    /**
     * 相关工单容器
     */
    container: null,
    
    /**
     * 相关工单数据
     */
    relatedTicketsData: null,
    
    /**
     * 初始化相关工单组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'relatedTickets') {
        console.log('初始化相关工单组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到相关工单容器元素:', containerId);
            return;
        }
        
        // 加载相关工单数据
        this.loadRelatedTicketsData();
        
        // 渲染相关工单
        this.render();
    },
    
    /**
     * 加载相关工单数据
     */
    loadRelatedTicketsData() {
        // 使用统一的Mock数据
        if (window.MockData && window.MockData.relatedTickets) {
            const related = window.MockData.relatedTickets;

            // 按关系类型分组
            this.relatedTicketsData = {
                sameCustomer: related.filter(ticket => ticket.relation === '同一客户').slice(0, 2),
                sameLocation: related.filter(ticket => ticket.relation === '同一小区').slice(0, 2),
                similarContent: related.filter(ticket => ticket.relation === '同类问题').slice(0, 3),
                duplicates: [], // 暂时为空，后续可以添加重复工单逻辑
                all: related
            };
        } else {
            // 备用数据
            this.relatedTicketsData = {
            sameCustomer: [
                {
                    id: 'GD202412200001',
                    title: '小区停车位管理混乱',
                    status: 'closed',
                    createTime: '2024-12-20 14:30:00',
                    department: '城市管理局',
                    similarity: 85,
                    relation: '同一客户'
                },
                {
                    id: 'GD202412180002',
                    title: '小区绿化带维护不当',
                    status: 'closed',
                    createTime: '2024-12-18 10:15:00',
                    department: '园林局',
                    similarity: 72,
                    relation: '同一客户'
                }
            ],
            sameLocation: [
                {
                    id: 'GD202412210003',
                    title: '某某小区电梯故障频发',
                    status: 'processing',
                    createTime: '2024-12-21 16:45:00',
                    department: '市场监管局',
                    similarity: 68,
                    relation: '同一地点'
                },
                {
                    id: 'GD202412190004',
                    title: '某某小区噪音扰民问题',
                    status: 'callback',
                    createTime: '2024-12-19 09:20:00',
                    department: '环保局',
                    similarity: 75,
                    relation: '同一地点'
                }
            ],
            similarContent: [
                {
                    id: 'GD202412170005',
                    title: '某某花园物业服务质量差',
                    status: 'closed',
                    createTime: '2024-12-17 11:30:00',
                    department: '住建局',
                    similarity: 92,
                    relation: '相似内容'
                },
                {
                    id: 'GD202412150006',
                    title: '某某公寓垃圾清运不及时',
                    status: 'closed',
                    createTime: '2024-12-15 08:45:00',
                    department: '城市管理局',
                    similarity: 88,
                    relation: '相似内容'
                }
            ],
            duplicates: [
                {
                    id: 'GD202412220007',
                    title: '小区物业管理不到位，垃圾清理不及时',
                    status: 'cancelled',
                    createTime: '2024-12-22 15:20:00',
                    department: '城市管理局',
                    similarity: 98,
                    relation: '重复工单',
                    mergedTo: 'GD202412220001'
                }
            ]
        };
        }
    },
    
    /**
     * 渲染相关工单
     */
    render() {
        if (!this.container || !this.relatedTicketsData) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        // 创建搜索和筛选区域
        const searchSection = this.createSearchSection();
        this.container.appendChild(searchSection);
        
        // 创建统计概览
        const statsSection = this.createStatsSection();
        this.container.appendChild(statsSection);
        
        // 创建相关工单列表
        const ticketsSection = this.createTicketsSection();
        this.container.appendChild(ticketsSection);
    },
    
    /**
     * 创建搜索和筛选区域
     * @returns {HTMLElement} 搜索区域元素
     */
    createSearchSection() {
        const section = document.createElement('div');
        section.className = 'related-search-section';
        
        section.innerHTML = `
            <div class="search-header">
                <h4><i class="fas fa-search"></i> 查找相关工单</h4>
                <div class="search-actions">
                    <button class="btn btn-sm btn-primary" onclick="RelatedTicketsComponent.refreshRelatedTickets()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="RelatedTicketsComponent.exportRelatedTickets()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>
            <div class="search-controls-compact">
                <div class="search-row">
                    <div class="search-input-group">
                        <input type="text" class="form-control form-control-sm" placeholder="输入工单编号或关键词搜索..." id="relatedTicketSearch">
                        <button class="btn btn-primary btn-sm" onclick="RelatedTicketsComponent.searchRelatedTickets()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="filter-controls-inline">
                        <select class="form-control form-control-sm" id="relationTypeFilter">
                            <option value="">全部关联类型</option>
                            <option value="同一客户">同一客户</option>
                            <option value="同一地点">同一地点</option>
                            <option value="相似内容">相似内容</option>
                            <option value="重复工单">重复工单</option>
                        </select>
                        <select class="form-control form-control-sm" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="processing">处理中</option>
                            <option value="callback">待回访</option>
                            <option value="closed">已关闭</option>
                            <option value="cancelled">已废除</option>
                        </select>
                        <select class="form-control form-control-sm" id="similarityFilter">
                            <option value="">全部相似度</option>
                            <option value="high">高相似度(≥80%)</option>
                            <option value="medium">中相似度(60-79%)</option>
                            <option value="low">低相似度(<60%)</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建统计概览区域
     * @returns {HTMLElement} 统计区域元素
     */
    createStatsSection() {
        const section = document.createElement('div');
        section.className = 'related-stats-section';
        
        // 计算统计数据 - 使用all数组避免重复计算
        const allTickets = this.relatedTicketsData.all || [];

        const totalCount = allTickets.length;
        const highSimilarity = allTickets.filter(t => t.similarity >= 80).length;
        const processingCount = allTickets.filter(t => t.status === 'processing').length;
        const duplicateCount = allTickets.filter(t => t.status === 'cancelled' || t.mergedTo).length;
        
        section.innerHTML = `
            <div class="stats-header">
                <h4><i class="fas fa-chart-pie"></i> 关联统计</h4>
            </div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">${totalCount}</div>
                        <div class="stat-label">相关工单总数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">${highSimilarity}</div>
                        <div class="stat-label">高相似度工单</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">${processingCount}</div>
                        <div class="stat-label">处理中工单</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-copy"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">${duplicateCount}</div>
                        <div class="stat-label">重复工单</div>
                    </div>
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建相关工单列表区域
     * @returns {HTMLElement} 工单列表元素
     */
    createTicketsSection() {
        const section = document.createElement('div');
        section.className = 'related-tickets-section';
        
        section.innerHTML = `
            <div class="tickets-header">
                <h4><i class="fas fa-list"></i> 相关工单列表</h4>
                <div class="view-controls">
                    <button class="view-btn active" data-view="grouped" onclick="RelatedTicketsComponent.switchView('grouped')">
                        <i class="fas fa-layer-group"></i> 分组视图
                    </button>
                    <button class="view-btn" data-view="list" onclick="RelatedTicketsComponent.switchView('list')">
                        <i class="fas fa-list"></i> 列表视图
                    </button>
                </div>
            </div>
            <div class="tickets-content" id="ticketsContent">
                ${this.renderGroupedView()}
            </div>
        `;
        
        return section;
    },
    
    /**
     * 渲染分组视图
     * @returns {string} 分组视图HTML
     */
    renderGroupedView() {
        const groups = [
            { key: 'duplicates', title: '重复工单', icon: 'fas fa-copy', data: this.relatedTicketsData.duplicates || [] },
            { key: 'similarContent', title: '相似内容', icon: 'fas fa-file-alt', data: this.relatedTicketsData.similarContent || [] },
            { key: 'sameCustomer', title: '同一客户', icon: 'fas fa-user', data: this.relatedTicketsData.sameCustomer || [] },
            { key: 'sameLocation', title: '同一地点', icon: 'fas fa-map-marker-alt', data: this.relatedTicketsData.sameLocation || [] }
        ];
        
        return groups.map(group => `
            <div class="ticket-group">
                <div class="group-header">
                    <h5><i class="${group.icon}"></i> ${group.title} (${group.data.length})</h5>
                    <button class="group-toggle" onclick="RelatedTicketsComponent.toggleGroup('${group.key}')">
                        <i class="fas fa-chevron-up"></i>
                    </button>
                </div>
                <div class="group-content" id="group-${group.key}">
                    ${group.data.map(ticket => this.renderTicketCard(ticket)).join('')}
                </div>
            </div>
        `).join('');
    },
    
    /**
     * 渲染工单卡片
     * @param {Object} ticket - 工单数据
     * @returns {string} 工单卡片HTML
     */
    renderTicketCard(ticket) {
        const statusClass = this.getStatusClass(ticket.status);
        const similarityClass = this.getSimilarityClass(ticket.similarity);
        
        return `
            <div class="ticket-card" data-ticket-id="${ticket.number || ticket.id}">
                <div class="ticket-header">
                    <div class="ticket-id">
                        <a href="#" onclick="RelatedTicketsComponent.viewTicket('${ticket.number || ticket.id}')">${ticket.number || ticket.id}</a>
                    </div>
                    <div class="ticket-badges">
                        <span class="status-badge ${statusClass}">${this.getStatusText(ticket.status)}</span>
                        <span class="similarity-badge ${similarityClass}">${ticket.similarity}%</span>
                    </div>
                </div>
                <div class="ticket-content">
                    <div class="ticket-title">${ticket.title}</div>
                    <div class="ticket-meta">
                        <span class="meta-item">
                            <i class="fas fa-calendar"></i>
                            ${ticket.createTime}
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-building"></i>
                            ${ticket.handler || ticket.department}
                        </span>
                        <span class="meta-item">
                            <i class="fas fa-tag"></i>
                            ${ticket.relation}
                        </span>
                    </div>
                    ${ticket.mergedTo ? `
                        <div class="merge-info">
                            <i class="fas fa-code-branch"></i>
                            已合并到: <a href="#" onclick="RelatedTicketsComponent.viewTicket('${ticket.mergedTo}')">${ticket.mergedTo}</a>
                        </div>
                    ` : ''}
                </div>
                <div class="ticket-actions">
                    <button class="btn btn-sm btn-outline" onclick="RelatedTicketsComponent.viewTicket('${ticket.number || ticket.id}')">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    <button class="btn btn-sm btn-outline" onclick="RelatedTicketsComponent.compareTickets('${ticket.number || ticket.id}')">
                        <i class="fas fa-balance-scale"></i> 对比
                    </button>
                    ${ticket.status !== 'cancelled' && !ticket.mergedTo ? `
                        <button class="btn btn-sm btn-warning" onclick="RelatedTicketsComponent.markAsDuplicate('${ticket.number || ticket.id}')">
                            <i class="fas fa-copy"></i> 标记重复
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    },
    
    /**
     * 获取状态样式类
     * @param {string} status - 状态
     * @returns {string} 样式类名
     */
    getStatusClass(status) {
        const classMap = {
            processing: 'status-processing',
            callback: 'status-callback',
            closed: 'status-closed',
            cancelled: 'status-cancelled'
        };
        return classMap[status] || 'status-default';
    },
    
    /**
     * 获取状态文本
     * @param {string} status - 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const textMap = {
            processing: '处理中',
            callback: '待回访',
            closed: '已关闭',
            cancelled: '已废除'
        };
        return textMap[status] || status;
    },
    
    /**
     * 获取相似度样式类
     * @param {number} similarity - 相似度
     * @returns {string} 样式类名
     */
    getSimilarityClass(similarity) {
        if (similarity >= 80) return 'similarity-high';
        if (similarity >= 60) return 'similarity-medium';
        return 'similarity-low';
    },
    
    /**
     * 刷新相关工单
     */
    refreshRelatedTickets() {
        Utils.showToast('正在刷新相关工单...', 'info');
        this.loadRelatedTicketsData();
        this.render();
        Utils.showToast('相关工单刷新完成', 'success');
    },
    
    /**
     * 导出相关工单
     */
    exportRelatedTickets() {
        Utils.showToast('正在导出相关工单...', 'info');
        // 这里实现导出逻辑
        setTimeout(() => {
            Utils.showToast('相关工单导出成功', 'success');
        }, 2000);
    },
    
    /**
     * 搜索相关工单
     */
    searchRelatedTickets() {
        const searchTerm = document.getElementById('relatedTicketSearch').value;
        Utils.showToast(`搜索: ${searchTerm}`, 'info');
        // 这里实现搜索逻辑
    },
    
    /**
     * 切换视图模式
     * @param {string} viewType - 视图类型
     */
    switchView(viewType) {
        const buttons = document.querySelectorAll('.view-btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-view="${viewType}"]`).classList.add('active');
        
        const content = document.getElementById('ticketsContent');
        if (viewType === 'grouped') {
            content.innerHTML = this.renderGroupedView();
        } else {
            content.innerHTML = this.renderListView();
        }
    },
    
    /**
     * 渲染列表视图
     * @returns {string} 列表视图HTML
     */
    renderListView() {
        const allTickets = [
            ...this.relatedTicketsData.duplicates,
            ...this.relatedTicketsData.similarContent,
            ...this.relatedTicketsData.sameCustomer,
            ...this.relatedTicketsData.sameLocation
        ].sort((a, b) => b.similarity - a.similarity);
        
        return `
            <div class="tickets-list">
                ${allTickets.map(ticket => this.renderTicketCard(ticket)).join('')}
            </div>
        `;
    },
    
    /**
     * 切换分组展开/折叠
     * @param {string} groupKey - 分组键
     */
    toggleGroup(groupKey) {
        const content = document.getElementById(`group-${groupKey}`);
        const toggle = content.parentElement.querySelector('.group-toggle i');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            toggle.className = 'fas fa-chevron-up';
        } else {
            content.style.display = 'none';
            toggle.className = 'fas fa-chevron-down';
        }
    },
    
    /**
     * 查看工单详情
     * @param {string} ticketId - 工单ID
     */
    viewTicket(ticketId) {
        Utils.showToast(`查看工单: ${ticketId}`, 'info');
        // 这里实现查看工单详情的逻辑
    },
    
    /**
     * 对比工单
     * @param {string} ticketId - 工单ID
     */
    compareTickets(ticketId) {
        Utils.showToast(`对比工单: ${ticketId}`, 'info');
        // 这里实现工单对比的逻辑
    },
    
    /**
     * 标记为重复工单
     * @param {string} ticketId - 工单ID
     */
    markAsDuplicate(ticketId) {
        if (confirm(`确定要将工单 ${ticketId} 标记为重复工单吗？`)) {
            Utils.showToast(`工单 ${ticketId} 已标记为重复`, 'success');
            // 这里实现标记重复工单的逻辑
        }
    }
};

// 将RelatedTicketsComponent暴露到全局作用域
window.RelatedTicketsComponent = RelatedTicketsComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    RelatedTicketsComponent.initialize();
});
