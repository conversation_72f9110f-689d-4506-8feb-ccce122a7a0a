/**
 * 12345政务服务热线系统 - 补记内容组件
 * @description 工单补记内容管理组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 补记内容组件
 */
const NotesComponent = {
    /**
     * 补记内容容器
     */
    container: null,
    
    /**
     * 补记数据列表
     * @type {Array<Object>}
     */
    notes: [],
    
    /**
     * 初始化补记内容组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'notesList') {
        console.log('初始化补记内容组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到补记内容容器元素:', containerId);
            return;
        }
        
        // 加载补记数据
        this.loadNotesData();
        
        // 渲染补记内容
        this.render();
    },
    
    /**
     * 加载补记数据
     */
    loadNotesData() {
        // 使用统一的Mock数据
        if (window.MockData && window.MockData.notes) {
            this.notes = window.MockData.notes.map(note => ({
                id: note.id,
                type: note.type,
                time: note.time,
                author: `${note.author.name} (工号: ${note.author.id})`,
                department: note.author.department,
                content: note.content,
                isPublic: note.isPublic,
                attachments: note.attachments || []
            }));
        } else {
            // 备用数据
            this.notes = [
            {
                id: 'note001',
                type: 'process',
                time: '2024-12-23 09:12:33',
                author: '赵六 (工号: A005)',
                department: '城市管理局',
                content: '已联系物业公司，对方表示将立即整改。联系人：张经理，电话：13900001111。物业公司回复：已了解情况，将立即安排人员处理垃圾清理问题，并加强日常管理。',
                isPublic: true,
                attachments: []
            },
            {
                id: 'note002',
                type: 'inspection',
                time: '2024-12-24 15:30:21',
                author: '赵六 (工号: A005)',
                department: '城市管理局',
                content: '现场检查情况：物业已完成垃圾清理，但公共设施维护仍有不足。检查时间：2024-12-24 14:00，检查地点：某某小区。检查结果：垃圾已清理完毕，但部分公共设施仍需维修。后续计划：要求物业公司一周内完成所有公共设施维修。',
                isPublic: true,
                attachments: [
                    { name: '现场照片1.jpg', type: 'image', size: '1.2MB', url: '#' },
                    { name: '现场照片2.jpg', type: 'image', size: '0.8MB', url: '#' },
                    { name: '检查记录.pdf', type: 'document', size: '0.5MB', url: '#' }
                ]
            },
            {
                id: 'note003',
                type: 'internal',
                time: '2024-12-24 16:45:12',
                author: '王五 (工号: A001)',
                department: '城市管理局',
                content: '内部协调记录：已与物业管理科沟通，要求加强对该小区的监管。同时建议建立长效机制，定期检查小区管理情况。',
                isPublic: false,
                attachments: []
            }
        ];
        }
    },
    
    /**
     * 渲染补记内容
     */
    render() {
        if (!this.container) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        if (this.notes.length === 0) {
            const emptyElement = document.createElement('div');
            emptyElement.className = 'notes-empty';
            emptyElement.innerHTML = `
                <div class="empty-icon">
                    <i class="fas fa-comment-slash"></i>
                </div>
                <div class="empty-text">暂无补记内容</div>
            `;
            this.container.appendChild(emptyElement);
            return;
        }
        
        // 按时间倒序排列
        const sortedNotes = [...this.notes].sort((a, b) => new Date(b.time) - new Date(a.time));
        
        // 渲染每个补记
        sortedNotes.forEach(note => {
            const noteElement = this.createNoteElement(note);
            this.container.appendChild(noteElement);
        });
    },
    
    /**
     * 创建补记元素
     * @param {Object} note - 补记数据
     * @returns {HTMLElement} 补记元素
     */
    createNoteElement(note) {
        const noteElement = document.createElement('div');
        noteElement.className = `note-item note-${note.type}`;
        noteElement.dataset.noteId = note.id;
        
        // 补记头部
        const headerElement = document.createElement('div');
        headerElement.className = 'note-header';
        
        const typeInfo = this.getNoteTypeInfo(note.type);
        
        headerElement.innerHTML = `
            <div class="note-meta">
                <div class="note-type">
                    <i class="${typeInfo.icon}"></i>
                    <span>${typeInfo.label}</span>
                    ${!note.isPublic ? '<span class="note-private"><i class="fas fa-lock"></i> 内部</span>' : ''}
                </div>
                <div class="note-time">${note.time}</div>
            </div>
            <div class="note-author">
                <span class="author-name">${note.author}</span>
                <span class="author-department">${note.department}</span>
            </div>
            <div class="note-actions">
                <button class="btn-note-action" onclick="NotesComponent.editNote('${note.id}')" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-note-action" onclick="NotesComponent.deleteNote('${note.id}')" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn-note-action" onclick="NotesComponent.toggleNoteDetails('${note.id}')" title="展开/收起">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        `;
        
        // 补记内容
        const contentElement = document.createElement('div');
        contentElement.className = 'note-content';
        
        // 内容文本
        const textElement = document.createElement('div');
        textElement.className = 'note-text';
        textElement.textContent = note.content;
        
        contentElement.appendChild(textElement);
        
        // 附件列表
        if (note.attachments && note.attachments.length > 0) {
            const attachmentsElement = document.createElement('div');
            attachmentsElement.className = 'note-attachments';

            const attachmentsHeader = document.createElement('div');
            attachmentsHeader.className = 'attachments-header';
            attachmentsHeader.innerHTML = `
                <i class="fas fa-paperclip"></i>
                <span>附件（${note.attachments.length}）</span>
            `;

            const attachmentsList = document.createElement('div');
            attachmentsList.className = 'attachments-list';

            note.attachments.forEach(attachment => {
                const attachmentItem = document.createElement('div');
                attachmentItem.className = 'attachment-item';
                attachmentItem.innerHTML = `
                    <div class="attachment-info">
                        <i class="${this.getAttachmentIcon(attachment.type)}"></i>
                        <span class="attachment-name">${attachment.name}</span>
                        <span class="attachment-size">${attachment.size}</span>
                    </div>
                    <div class="attachment-actions">
                        <button class="btn-attachment-action" onclick="NotesComponent.previewAttachment('${attachment.url || '#'}')" title="预览">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-attachment-action" onclick="NotesComponent.downloadAttachment('${attachment.url || '#'}')" title="下载">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                `;
                attachmentsList.appendChild(attachmentItem);
            });

            attachmentsElement.appendChild(attachmentsHeader);
            attachmentsElement.appendChild(attachmentsList);
            contentElement.appendChild(attachmentsElement);
        }
        
        // 详情区域（默认隐藏）
        const detailsElement = document.createElement('div');
        detailsElement.className = 'note-details';
        detailsElement.id = `note-details-${note.id}`;
        detailsElement.style.display = 'none';
        
        // 这里可以添加更多详细信息
        detailsElement.innerHTML = `
            <div class="note-details-content">
                <div class="detail-item">
                    <span class="detail-label">创建时间：</span>
                    <span class="detail-value">${note.time}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">可见性：</span>
                    <span class="detail-value">${note.isPublic ? '公开' : '内部'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">字符数：</span>
                    <span class="detail-value">${note.content.length}</span>
                </div>
            </div>
        `;
        
        // 组装补记元素
        noteElement.appendChild(headerElement);
        noteElement.appendChild(contentElement);
        noteElement.appendChild(detailsElement);
        
        return noteElement;
    },
    
    /**
     * 获取补记类型信息
     * @param {string} type - 补记类型
     * @returns {Object} 类型信息
     */
    getNoteTypeInfo(type) {
        const typeInfoMap = {
            // 中文类型映射
            '创建工单': { label: '创建工单', icon: 'fas fa-plus-circle' },
            '指派工单': { label: '指派工单', icon: 'fas fa-share' },
            '接收工单': { label: '接收工单', icon: 'fas fa-inbox' },
            '现场调查': { label: '现场调查', icon: 'fas fa-search' },
            '联系物业': { label: '联系物业', icon: 'fas fa-phone' },
            '内部协调': { label: '内部协调', icon: 'fas fa-users' },
            '申请延期': { label: '申请延期', icon: 'fas fa-clock' },
            '跟踪检查': { label: '跟踪检查', icon: 'fas fa-eye' },
            '审核办结': { label: '审核办结', icon: 'fas fa-check-circle' },
            '工单办结': { label: '工单办结', icon: 'fas fa-flag-checkered' },

            // 英文类型映射（兼容）
            process: { label: '处理记录', icon: 'fas fa-cog' },
            communication: { label: '沟通记录', icon: 'fas fa-comments' },
            inspection: { label: '现场检查', icon: 'fas fa-search' },
            coordination: { label: '协调记录', icon: 'fas fa-handshake' },
            internal: { label: '内部记录', icon: 'fas fa-lock' },
            follow_up: { label: '跟进记录', icon: 'fas fa-eye' },
            other: { label: '其他记录', icon: 'fas fa-file-alt' }
        };

        return typeInfoMap[type] || typeInfoMap.other;
    },
    
    /**
     * 获取附件图标
     * @param {string} type - 附件类型
     * @returns {string} 图标类名
     */
    getAttachmentIcon(type) {
        const iconMap = {
            image: 'fas fa-image',
            document: 'fas fa-file-pdf',
            audio: 'fas fa-file-audio',
            video: 'fas fa-file-video',
            excel: 'fas fa-file-excel',
            word: 'fas fa-file-word'
        };
        
        return iconMap[type] || 'fas fa-file';
    },
    
    /**
     * 显示添加补记对话框
     */
    showAddNoteDialog() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3>添加补记</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addNoteForm">
                        <div class="form-group">
                            <label for="noteType">补记类型</label>
                            <select id="noteType" class="form-control" required>
                                <option value="process">处理记录</option>
                                <option value="communication">沟通记录</option>
                                <option value="inspection">现场检查</option>
                                <option value="coordination">协调记录</option>
                                <option value="internal">内部记录</option>
                                <option value="follow_up">跟进记录</option>
                                <option value="other">其他记录</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="noteContent">补记内容</label>
                            <textarea id="noteContent" class="form-control" rows="6" placeholder="请输入补记内容..." required></textarea>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="noteIsPublic" checked>
                                公开显示（取消勾选为内部记录）
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="noteAttachments">附件</label>
                            <input type="file" id="noteAttachments" class="form-control" multiple>
                            <small class="form-text">支持图片、文档、音频、视频等格式，单个文件不超过10MB</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="NotesComponent.saveNote()">保存</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 显示模态框
        setTimeout(() => {
            modal.style.display = 'flex';
        }, 100);
    },
    
    /**
     * 保存补记
     */
    saveNote() {
        const form = document.getElementById('addNoteForm');
        const formData = new FormData(form);
        
        const noteType = document.getElementById('noteType').value;
        const noteContent = document.getElementById('noteContent').value;
        const noteIsPublic = document.getElementById('noteIsPublic').checked;
        const noteAttachments = document.getElementById('noteAttachments').files;
        
        if (!noteContent.trim()) {
            Utils.showToast('请输入补记内容', 'warning');
            return;
        }
        
        // 创建新补记
        const newNote = {
            id: 'note' + Date.now(),
            type: noteType,
            time: Utils.formatDateTime(new Date()),
            author: `${AppState.currentUser.name} (工号: ${AppState.currentUser.id})`,
            department: AppState.currentUser.department,
            content: noteContent,
            isPublic: noteIsPublic,
            attachments: []
        };
        
        // 处理附件（这里只是模拟）
        if (noteAttachments.length > 0) {
            for (let i = 0; i < noteAttachments.length; i++) {
                const file = noteAttachments[i];
                newNote.attachments.push({
                    name: file.name,
                    type: this.getFileType(file.name),
                    size: this.formatFileSize(file.size),
                    url: '#' // 实际应用中这里是上传后的URL
                });
            }
        }
        
        // 添加到补记列表
        this.notes.unshift(newNote);
        
        // 重新渲染
        this.render();
        
        // 关闭模态框
        document.querySelector('.modal').remove();
        
        // 显示成功消息
        Utils.showToast('补记添加成功', 'success');
        
        // 添加到时间轴
        if (window.TimelineComponent) {
            TimelineComponent.addEvent({
                id: 'event' + Date.now(),
                type: 'note',
                time: newNote.time,
                actor: newNote.author,
                content: `添加${this.getNoteTypeInfo(newNote.type).label}：${newNote.content.substring(0, 50)}${newNote.content.length > 50 ? '...' : ''}`,
                department: newNote.department,
                details: {
                    noteType: newNote.type,
                    isPublic: newNote.isPublic,
                    attachmentCount: newNote.attachments.length
                }
            });
        }
    },
    
    /**
     * 获取文件类型
     * @param {string} fileName - 文件名
     * @returns {string} 文件类型
     */
    getFileType(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extension)) {
            return 'image';
        } else if (['pdf'].includes(extension)) {
            return 'document';
        } else if (['mp3', 'wav', 'ogg'].includes(extension)) {
            return 'audio';
        } else if (['mp4', 'avi', 'mov'].includes(extension)) {
            return 'video';
        } else if (['xls', 'xlsx'].includes(extension)) {
            return 'excel';
        } else if (['doc', 'docx'].includes(extension)) {
            return 'word';
        } else {
            return 'document';
        }
    },
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },
    
    /**
     * 编辑补记
     * @param {string} noteId - 补记ID
     */
    editNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (!note) return;
        
        Utils.showToast('编辑功能开发中...', 'info');
    },
    
    /**
     * 删除补记
     * @param {string} noteId - 补记ID
     */
    deleteNote(noteId) {
        if (!confirm('确定要删除这条补记吗？')) return;
        
        this.notes = this.notes.filter(n => n.id !== noteId);
        this.render();
        Utils.showToast('补记已删除', 'success');
    },
    
    /**
     * 切换补记详情显示
     * @param {string} noteId - 补记ID
     */
    toggleNoteDetails(noteId) {
        const detailsElement = document.getElementById(`note-details-${noteId}`);
        const button = detailsElement.parentElement.querySelector('.note-actions button:last-child i');
        
        if (detailsElement.style.display === 'none') {
            detailsElement.style.display = 'block';
            button.className = 'fas fa-chevron-up';
        } else {
            detailsElement.style.display = 'none';
            button.className = 'fas fa-chevron-down';
        }
    },

    /**
     * 获取附件图标
     */
    getAttachmentIcon(type) {
        const iconMap = {
            'image': 'fas fa-image',
            'document': 'fas fa-file-pdf',
            'audio': 'fas fa-file-audio',
            'video': 'fas fa-file-video',
            'excel': 'fas fa-file-excel',
            'word': 'fas fa-file-word'
        };
        return iconMap[type] || 'fas fa-file';
    },

    /**
     * 预览附件
     * @param {string} url - 附件URL
     */
    previewAttachment(url) {
        Utils.showToast('正在加载附件预览...', 'info');
        // 这里实现附件预览逻辑
    },
    
    /**
     * 下载附件
     * @param {string} url - 附件URL
     */
    downloadAttachment(url) {
        Utils.showToast('正在下载附件...', 'info');
        // 这里实现附件下载逻辑
    }
};

// 将NotesComponent暴露到全局作用域
window.NotesComponent = NotesComponent;

// 全局函数 - 添加补记
function addNote() {
    NotesComponent.showAddNoteDialog();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    NotesComponent.initialize();
});
