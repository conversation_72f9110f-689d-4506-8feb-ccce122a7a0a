/**
 * 12345政务服务热线系统 - 工单流转路径图组件
 * @description 可视化工单流转路径，显示当前位置、已完成路径、预计路径等
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 工单流转路径图组件
 */
const WorkflowComponent = {
    /**
     * 流转路径图容器
     */
    container: null,
    
    /**
     * 流转数据
     */
    workflowData: null,
    
    /**
     * 初始化流转路径图组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'workflowChart') {
        console.log('初始化工单流转路径图组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到流转路径图容器元素:', containerId);
            return;
        }
        
        // 加载流转数据
        this.loadWorkflowData();
        
        // 渲染流转路径图
        this.render();
    },
    
    /**
     * 加载流转数据
     */
    loadWorkflowData() {
        // 使用统一的Mock数据
        if (window.MockData && window.MockData.workflow) {
            const workflow = window.MockData.workflow;
            const statistics = window.MockData.statistics || {};
            this.workflowData = {
                ticketType: 'standard',
                currentStep: workflow.currentStep,
                steps: workflow.steps.map(step => ({
                    id: step.name.toLowerCase().replace(/\s+/g, '_'),
                    name: step.name,
                    description: `${step.name}阶段`,
                    status: step.status,
                    department: step.department,
                    actor: step.handler,
                    startTime: step.time,
                    endTime: step.status === 'completed' ? step.time : null,
                    duration: step.duration === '进行中' ? null : parseInt(step.duration),
                    estimatedDuration: 30,
                    slaLimit: 60,
                    isRequired: true,
                    canSkip: false,
                    conditions: [],
                    nextSteps: []
                })),
                milestones: workflow.milestones || [],
                statistics: {
                    totalSteps: workflow.steps.length,
                    completedSteps: workflow.steps.filter(s => s.status === 'completed').length,
                    currentStepIndex: workflow.currentStep - 1,
                    estimatedTotalTime: 15, // 15天总时限
                    actualElapsedTime: 5, // 实际用时5天
                    remainingTime: 0, // 已完成
                    progressPercentage: 100,
                    onSchedule: true,
                    delayRisk: 'low',
                    efficiency: statistics.efficiency || {},
                    performance: statistics.performance || {},
                    quality: statistics.quality || {}
                }
            };
        } else {
            // 备用数据
            const currentStatus = AppState.currentTicket?.status || 'processing';
            this.workflowData = {
                ticketType: 'standard',
                currentStep: this.getCurrentStepIndex(currentStatus),
                steps: [
                {
                    id: 'create',
                    name: '创建工单',
                    description: '市民来电投诉，话务员创建工单',
                    status: 'completed',
                    department: '市级话务中心',
                    actor: '张三 (工号: 001)',
                    startTime: '2024-12-22 09:30:15',
                    endTime: '2024-12-22 09:35:22',
                    duration: 5,
                    estimatedDuration: 5,
                    slaLimit: 10,
                    isRequired: true,
                    canSkip: false,
                    conditions: [],
                    nextSteps: ['assign']
                },
                {
                    id: 'assign',
                    name: '指派工单',
                    description: '根据工单内容指派至相关部门',
                    status: 'completed',
                    department: '市级话务中心',
                    actor: '李四 (工号: 002)',
                    startTime: '2024-12-22 10:15:22',
                    endTime: '2024-12-22 10:20:30',
                    duration: 5,
                    estimatedDuration: 15,
                    slaLimit: 30,
                    isRequired: true,
                    canSkip: false,
                    conditions: ['工单内容完整', '责任部门明确'],
                    nextSteps: ['receive']
                },
                {
                    id: 'receive',
                    name: '接收工单',
                    description: '责任部门接收并确认工单',
                    status: 'completed',
                    department: '城市管理局',
                    actor: '王五 (工号: A001)',
                    startTime: '2024-12-22 11:05:47',
                    endTime: '2024-12-22 11:15:20',
                    duration: 10,
                    estimatedDuration: 60,
                    slaLimit: 120,
                    isRequired: true,
                    canSkip: false,
                    conditions: ['部门有处理能力', '工单属于部门职责'],
                    nextSteps: ['process', 'transfer']
                },
                {
                    id: 'process',
                    name: '现场处理',
                    description: '实地调查处理问题',
                    status: 'current',
                    department: '城市管理局',
                    actor: '赵六 (工号: A005)',
                    startTime: '2024-12-22 14:30:00',
                    endTime: null,
                    duration: null,
                    estimatedDuration: 2880, // 2天
                    slaLimit: 4320, // 3天
                    isRequired: true,
                    canSkip: false,
                    conditions: ['现场可达', '问题可解决'],
                    nextSteps: ['review', 'complete']
                },
                {
                    id: 'review',
                    name: '审核办结',
                    description: '审核处理结果并办结',
                    status: 'pending',
                    department: '城市管理局',
                    actor: null,
                    startTime: null,
                    endTime: null,
                    duration: null,
                    estimatedDuration: 60,
                    slaLimit: 120,
                    isRequired: true,
                    canSkip: false,
                    conditions: ['处理结果完整', '符合办结标准'],
                    nextSteps: ['complete', 'return']
                },
                {
                    id: 'complete',
                    name: '工单办结',
                    description: '工单处理完成，等待回访',
                    status: 'pending',
                    department: '城市管理局',
                    actor: null,
                    startTime: null,
                    endTime: null,
                    duration: null,
                    estimatedDuration: 30,
                    slaLimit: 60,
                    isRequired: true,
                    canSkip: false,
                    conditions: ['问题已解决', '材料已上传'],
                    nextSteps: ['callback']
                },
                {
                    id: 'callback',
                    name: '回访评价',
                    description: '电话回访客户满意度',
                    status: 'pending',
                    department: '市级话务中心',
                    actor: null,
                    startTime: null,
                    endTime: null,
                    duration: null,
                    estimatedDuration: 10,
                    slaLimit: 4320, // 3天内
                    isRequired: true,
                    canSkip: false,
                    conditions: ['工单已办结', '客户可联系'],
                    nextSteps: ['close']
                },
                {
                    id: 'close',
                    name: '工单关闭',
                    description: '工单流程结束',
                    status: 'pending',
                    department: '市级话务中心',
                    actor: null,
                    startTime: null,
                    endTime: null,
                    duration: null,
                    estimatedDuration: 5,
                    slaLimit: 10,
                    isRequired: true,
                    canSkip: false,
                    conditions: ['回访已完成'],
                    nextSteps: []
                }
            ],
            alternativePaths: [
                {
                    id: 'transfer',
                    name: '转办工单',
                    description: '转至其他部门处理',
                    fromStep: 'receive',
                    toStep: 'receive',
                    conditions: ['不属于本部门职责', '需要其他部门协助'],
                    isOptional: true
                },
                {
                    id: 'return',
                    name: '退回重办',
                    description: '审核不通过，退回重新处理',
                    fromStep: 'review',
                    toStep: 'process',
                    conditions: ['处理结果不符合要求', '材料不完整'],
                    isOptional: true
                },
                {
                    id: 'escalate',
                    name: '升级督办',
                    description: '超时或重要工单升级处理',
                    fromStep: 'any',
                    toStep: '督办',
                    conditions: ['超过SLA时限', '重要紧急工单'],
                    isOptional: true
                }
            ],
            statistics: {
                totalSteps: 8,
                completedSteps: 3,
                currentStepIndex: 3,
                estimatedTotalTime: 7200, // 5天
                actualElapsedTime: 1800, // 1.25天
                remainingTime: 5400, // 3.75天
                progressPercentage: 37.5,
                onSchedule: true,
                delayRisk: 'low'
            },
            milestones: [
                {
                    name: '工单创建',
                    stepId: 'create',
                    importance: 'high',
                    achieved: true,
                    achievedTime: '2024-12-22 09:35:22'
                },
                {
                    name: '部门接收',
                    stepId: 'receive',
                    importance: 'high',
                    achieved: true,
                    achievedTime: '2024-12-22 11:15:20'
                },
                {
                    name: '现场处理',
                    stepId: 'process',
                    importance: 'critical',
                    achieved: false,
                    estimatedTime: '2024-12-25 14:30:00'
                },
                {
                    name: '工单办结',
                    stepId: 'complete',
                    importance: 'critical',
                    achieved: false,
                    estimatedTime: '2024-12-26 10:00:00'
                },
                {
                    name: '回访完成',
                    stepId: 'callback',
                    importance: 'high',
                    achieved: false,
                    estimatedTime: '2024-12-27 15:00:00'
                }
            ]
        };
        }
    },
    
    /**
     * 获取当前步骤索引
     * @param {string} status - 工单状态
     * @returns {number} 步骤索引
     */
    getCurrentStepIndex(status) {
        const statusStepMap = {
            draft: 0,
            pending: 2,
            processing: 3,
            reviewing: 4,
            callback: 6,
            closed: 7,
            cancelled: -1
        };
        return statusStepMap[status] || 3;
    },
    
    /**
     * 渲染流转路径图
     */
    render() {
        if (!this.container || !this.workflowData) return;

        // 清空容器
        this.container.innerHTML = '';

        // 创建页面标题
        const titleSection = this.createTitleSection();
        this.container.appendChild(titleSection);

        // 创建流转概览区域
        const overviewSection = this.createOverviewSection();
        this.container.appendChild(overviewSection);

        // 创建流转历史区域
        const workflowSection = this.createWorkflowSection();
        this.container.appendChild(workflowSection);

        // 创建里程碑区域
        const milestonesSection = this.createMilestonesSection();
        this.container.appendChild(milestonesSection);

        // 创建替代路径区域
        const alternativeSection = this.createAlternativeSection();
        this.container.appendChild(alternativeSection);
    },

    /**
     * 创建页面标题区域
     * @returns {HTMLElement} 标题元素
     */
    createTitleSection() {
        const section = document.createElement('div');
        section.className = 'workflow-title-section';

        section.innerHTML = `
            <div class="title-header">
                <h2><i class="fas fa-project-diagram"></i> 工单流转历史</h2>
                <div class="title-description">
                    展示工单完整的流转历史，包含流程步骤和处理记录
                </div>
            </div>
            <div class="view-controls">
                <button class="view-toggle active" data-view="workflow" onclick="WorkflowComponent.switchWorkflowView('workflow')">
                    <i class="fas fa-project-diagram"></i> 流转视图
                </button>
                <button class="view-toggle" data-view="timeline" onclick="WorkflowComponent.switchWorkflowView('timeline')">
                    <i class="fas fa-history"></i> 时间轴视图
                </button>
                <button class="view-toggle" data-view="flowchart" onclick="WorkflowComponent.switchWorkflowView('flowchart')">
                    <i class="fas fa-sitemap"></i> 流程图视图
                </button>
            </div>
        `;

        return section;
    },

    /**
     * 创建流转概览区域
     * @returns {HTMLElement} 概览元素
     */
    createOverviewSection() {
        const section = document.createElement('div');
        section.className = 'workflow-overview';

        const stats = this.workflowData.statistics || { onSchedule: true };

        section.innerHTML = `
            <div class="section-header">
                <h4><i class="fas fa-route"></i> 流转概览</h4>
                <div class="workflow-status ${stats.onSchedule ? 'on-schedule' : 'delayed'}">
                    ${stats.onSchedule ? '按时进行' : '存在延期风险'}
                </div>
            </div>
            <div class="overview-content">
                <div class="progress-overview">
                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${stats.progressPercentage || 0}%"></div>
                        </div>
                        <div class="progress-text">${stats.progressPercentage || 0}% 完成</div>
                    </div>
                    <div class="progress-stats">
                        <div class="stat-item">
                            <span class="stat-value">${stats.completedSteps || 0}</span>
                            <span class="stat-label">已完成</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${(stats.totalSteps || 0) - (stats.completedSteps || 0)}</span>
                            <span class="stat-label">待完成</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.actualElapsedTime || 0}天</span>
                            <span class="stat-label">已用时</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.remainingTime || 0}天</span>
                            <span class="stat-label">预计剩余</span>
                        </div>
                    </div>
                </div>
                <div class="risk-assessment">
                    <div class="risk-level risk-${stats.delayRisk}">
                        <i class="fas ${this.getRiskIcon(stats.delayRisk)}"></i>
                        <span>延期风险: ${this.getRiskText(stats.delayRisk)}</span>
                    </div>
                </div>
            </div>
        `;

        return section;
    },

    /**
     * 创建融合的流转路径和历史区域
     * @returns {HTMLElement} 融合区域元素
     */
    createWorkflowSection() {
        const section = document.createElement('div');
        section.className = 'fused-workflow-section';

        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-route"></i> 流转路径与处理历史</h4>
            <div class="section-actions">
                <button class="btn btn-sm btn-outline" onclick="WorkflowComponent.exportHistory()">
                    <i class="fas fa-download"></i> 导出
                </button>
                <button class="btn btn-sm btn-outline" onclick="WorkflowComponent.printHistory()">
                    <i class="fas fa-print"></i> 打印
                </button>
            </div>
        `;

        const content = document.createElement('div');
        content.className = 'workflow-content';
        content.id = 'workflowContent';
        content.innerHTML = this.renderWorkflowContent();

        section.appendChild(header);
        section.appendChild(content);

        return section;
    },

    /**
     * 渲染融合的流转内容
     * @returns {string} 融合内容HTML
     */
    renderWorkflowContent() {
        // 获取处理历史数据（从TimelineComponent获取或模拟）
        const historyData = this.getProcessHistoryData();

        return `
            <div class="workflow-container">
                ${this.workflowData.steps.map((step, index) =>
                    this.createStepElement(step, index, historyData)
                ).join('')}
            </div>
        `;
    },

    /**
     * 获取处理历史数据
     * @returns {Array} 处理历史数据
     */
    getProcessHistoryData() {
        // 从Mock数据中获取真实的处理历史
        if (window.MockData && window.MockData.notes) {
            return window.MockData.notes.map(note => ({
                id: note.id,
                type: note.type,
                time: note.time,
                actor: note.author.name,
                department: note.author.department,
                content: note.content,
                isPublic: note.isPublic !== false,
                attachments: note.attachments || [],
                details: note.content
            }));
        }

        // 备用数据
        return [
            {
                id: 'event1',
                type: 'create',
                time: '2024-12-22 09:30:15',
                actor: '张三 (工号: 001)',
                content: '创建工单',
                department: '市级话务中心',
                details: '市民来电投诉，话务员创建工单记录'
            },
            {
                id: 'event2',
                type: 'assign',
                time: '2024-12-22 10:15:22',
                actor: '李四 (工号: 002)',
                content: '指派工单至城市管理局',
                department: '市级话务中心',
                details: '根据工单内容分析，指派至城市管理局处理'
            },
            {
                id: 'event3',
                type: 'receive',
                time: '2024-12-22 11:05:47',
                actor: '王五 (工号: A001)',
                content: '接收工单',
                department: '城市管理局',
                details: '部门接收工单，确认处理责任'
            },
            {
                id: 'event4',
                type: 'process',
                time: '2024-12-22 14:30:00',
                actor: '赵六 (工号: A005)',
                content: '开始现场处理',
                department: '城市管理局',
                details: '派遣现场人员前往处理'
            },
            {
                id: 'event5',
                type: 'note',
                time: '2024-12-22 16:45:30',
                actor: '赵六 (工号: A005)',
                content: '添加处理备注',
                department: '城市管理局',
                details: '已联系物业公司，要求立即整改'
            },
            {
                id: 'event6',
                type: 'attachment',
                time: '2024-12-22 17:20:15',
                actor: '赵六 (工号: A005)',
                content: '上传现场照片',
                department: '城市管理局',
                details: '上传现场处理前后对比照片'
            }
        ];
    },

    /**
     * 创建流转路径图区域
     * @returns {HTMLElement} 路径图元素
     */
    createPathSection() {
        const section = document.createElement('div');
        section.className = 'workflow-path';

        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-project-diagram"></i> 流转路径</h4>
            <div class="path-legend">
                <span class="legend-item completed"><i class="fas fa-check-circle"></i> 已完成</span>
                <span class="legend-item current"><i class="fas fa-play-circle"></i> 进行中</span>
                <span class="legend-item pending"><i class="fas fa-circle"></i> 待处理</span>
            </div>
        `;

        const pathContainer = document.createElement('div');
        pathContainer.className = 'path-container';

        // 创建步骤节点
        this.workflowData.steps.forEach((step, index) => {
            const stepElement = this.createStepElement(step, index);
            pathContainer.appendChild(stepElement);

            // 添加连接线（除了最后一个步骤）
            if (index < this.workflowData.steps.length - 1) {
                const connector = this.createConnector(step, this.workflowData.steps[index + 1]);
                pathContainer.appendChild(connector);
            }
        });

        section.appendChild(header);
        section.appendChild(pathContainer);

        return section;
    },

    /**
     * 创建步骤元素
     * @param {Object} step - 步骤数据
     * @param {number} index - 步骤索引
     * @returns {HTMLElement} 步骤元素
     */
    createStepElement(step, index) {
        const element = document.createElement('div');
        element.className = `workflow-step step-${step.status}`;
        element.dataset.stepId = step.id;

        const isCurrent = step.status === 'current';
        const isCompleted = step.status === 'completed';

        element.innerHTML = `
            <div class="step-node">
                <div class="step-icon">
                    ${isCompleted ? '<i class="fas fa-check"></i>' :
                      isCurrent ? '<i class="fas fa-play"></i>' :
                      '<i class="fas fa-circle"></i>'}
                </div>
                <div class="step-number">${index + 1}</div>
            </div>
            <div class="step-content">
                <div class="step-header">
                    <h5 class="step-name">${step.name}</h5>
                    <div class="step-meta">
                        ${step.department ? `<span class="step-department">${step.department}</span>` : ''}
                        ${step.actor ? `<span class="step-actor">${step.actor}</span>` : ''}
                    </div>
                </div>
                <div class="step-description">${step.description}</div>
                <div class="step-timing">
                    ${step.startTime ? `
                        <div class="timing-item">
                            <i class="fas fa-play"></i>
                            <span>开始: ${step.startTime}</span>
                        </div>
                    ` : ''}
                    ${step.endTime ? `
                        <div class="timing-item">
                            <i class="fas fa-stop"></i>
                            <span>结束: ${step.endTime}</span>
                        </div>
                    ` : ''}
                    ${step.duration !== null ? `
                        <div class="timing-item">
                            <i class="fas fa-clock"></i>
                            <span>用时: ${step.duration}分钟</span>
                        </div>
                    ` : `
                        <div class="timing-item">
                            <i class="fas fa-hourglass-half"></i>
                            <span>预计: ${step.estimatedDuration}分钟</span>
                        </div>
                    `}
                    <div class="timing-item sla-limit">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>时限: ${step.slaLimit}分钟</span>
                    </div>
                </div>
                ${step.conditions.length > 0 ? `
                    <div class="step-conditions">
                        <div class="conditions-label">前置条件:</div>
                        <ul class="conditions-list">
                            ${step.conditions.map(condition => `<li>${condition}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                <div class="step-actions">
                    ${isCurrent ? `
                        <button class="btn btn-sm btn-primary" onclick="WorkflowComponent.completeStep('${step.id}')">
                            <i class="fas fa-check"></i> 完成步骤
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline" onclick="WorkflowComponent.viewStepDetails('${step.id}')">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                </div>
            </div>
        `;

        return element;
    },

    /**
     * 创建融合的步骤元素
     * @param {Object} step - 步骤数据
     * @param {number} index - 步骤索引
     * @param {Array} historyData - 处理历史数据
     * @returns {string} 融合步骤HTML
     */
    createStepElement(step, index, historyData) {
        const isCurrent = step.status === 'current';
        const isCompleted = step.status === 'completed';

        // 获取该步骤相关的历史记录
        const relatedHistory = historyData.filter(event => {
            // 建立步骤名称与处理记录类型的映射关系
            const stepTypeMapping = {
                '工单创建': ['创建工单'],
                '工单指派': ['指派工单'],
                '现场处理': ['接收工单', '现场调查', '联系物业', '内部协调', '申请延期', '跟踪检查'],
                '审核办结': ['审核办结'],
                '回访确认': ['工单办结']
            };

            const matchingTypes = stepTypeMapping[step.name] || [];
            return matchingTypes.includes(event.type);
        });

        return `
            <div class="workflow-step ${step.status}" data-step-id="${step.id}">
                <div class="step-timeline">
                    <div class="step-node">
                        <div class="step-icon">
                            ${isCompleted ? '<i class="fas fa-check"></i>' :
                              isCurrent ? '<i class="fas fa-play"></i>' :
                              '<i class="fas fa-circle"></i>'}
                        </div>
                        <div class="step-number">${index + 1}</div>
                    </div>
                    ${index < this.workflowData.steps.length - 1 ? `
                        <div class="step-connector ${isCompleted || isCurrent ? 'active' : 'inactive'}"></div>
                    ` : ''}
                </div>

                <div class="step-content-area">
                    <div class="step-main-info">
                        <div class="step-header">
                            <h5 class="step-name">${step.name}</h5>
                            <div class="step-status-badge ${step.status}">
                                ${this.getStepStatusText(step.status)}
                            </div>
                        </div>
                        <div class="step-description">${step.description}</div>

                        <div class="step-meta-info">
                            <div class="meta-grid">
                                <div class="meta-item">
                                    <label>责任部门</label>
                                    <span>${step.department}</span>
                                </div>
                                <div class="meta-item">
                                    <label>处理人员</label>
                                    <span>${step.actor || '待分配'}</span>
                                </div>
                                <div class="meta-item">
                                    <label>预计用时</label>
                                    <span>${step.estimatedDuration}分钟</span>
                                </div>
                                <div class="meta-item">
                                    <label>SLA时限</label>
                                    <span class="sla-limit">${step.slaLimit}分钟</span>
                                </div>
                            </div>
                        </div>

                        ${step.conditions.length > 0 ? `
                            <div class="step-conditions">
                                <label>前置条件:</label>
                                <ul>
                                    ${step.conditions.map(condition => `<li>${condition}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>

                    <div class="step-history-section">
                        <div class="history-header">
                            <h6><i class="fas fa-history"></i> 处理历史</h6>
                            <span class="history-count">${relatedHistory.length} 条记录</span>
                        </div>

                        ${relatedHistory.length > 0 ? `
                            <div class="history-list">
                                ${relatedHistory.map(event => `
                                    <div class="history-item">
                                        <div class="history-time">${event.time}</div>
                                        <div class="history-content">
                                            <div class="history-actor">${event.actor}</div>
                                            <div class="history-action">${event.content}</div>
                                            <div class="history-details">${event.details}</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : `
                            <div class="no-history">
                                <i class="fas fa-info-circle"></i>
                                ${step.status === 'pending' ? '等待处理' : '暂无记录'}
                            </div>
                        `}
                    </div>

                    <div class="step-actions">
                        ${isCurrent ? `
                            <button class="btn btn-sm btn-primary" onclick="WorkflowComponent.completeStep('${step.id}')">
                                <i class="fas fa-check"></i> 完成步骤
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline" onclick="WorkflowComponent.viewStepDetails('${step.id}')">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                        ${isCompleted || isCurrent ? `
                            <button class="btn btn-sm btn-outline" onclick="WorkflowComponent.addStepNote('${step.id}')">
                                <i class="fas fa-plus"></i> 添加备注
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * 获取步骤状态文本
     * @param {string} status - 步骤状态
     * @returns {string} 状态文本
     */
    getStepStatusText(status) {
        const textMap = {
            completed: '已完成',
            current: '进行中',
            pending: '待处理'
        };
        return textMap[status] || status;
    },

    /**
     * 创建连接线
     * @param {Object} fromStep - 起始步骤
     * @param {Object} toStep - 目标步骤
     * @returns {HTMLElement} 连接线元素
     */
    createConnector(fromStep, toStep) {
        const connector = document.createElement('div');
        connector.className = 'workflow-connector';

        const isActive = fromStep.status === 'completed' || fromStep.status === 'current';

        connector.innerHTML = `
            <div class="connector-line ${isActive ? 'active' : 'inactive'}">
                <div class="connector-arrow">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        `;

        return connector;
    },

    /**
     * 创建里程碑区域
     * @returns {HTMLElement} 里程碑元素
     */
    createMilestonesSection() {
        const section = document.createElement('div');
        section.className = 'workflow-milestones';

        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-flag"></i> 关键里程碑</h4>
        `;

        const milestonesList = document.createElement('div');
        milestonesList.className = 'milestones-list';

        const milestones = this.workflowData.milestones || [
            {
                id: 'receive',
                name: '工单接收',
                description: '承办单位接收工单',
                achieved: true,
                importance: 'high',
                estimatedTime: '2024-12-20 14:30:00'
            },
            {
                id: 'process',
                name: '现场处理',
                description: '现场调查和处理',
                achieved: false,
                importance: 'high',
                estimatedTime: '2024-12-25 15:00:00'
            }
        ];

        milestones.forEach(milestone => {
            const milestoneElement = document.createElement('div');
            const isAchieved = milestone.status === 'achieved' || milestone.achieved;
            milestoneElement.className = `milestone-item ${isAchieved ? 'achieved' : 'pending'} importance-${milestone.importance}`;

            milestoneElement.innerHTML = `
                <div class="milestone-icon">
                    <i class="fas ${isAchieved ? 'fa-flag-checkered' : 'fa-flag'}"></i>
                </div>
                <div class="milestone-content">
                    <div class="milestone-name">${milestone.name}</div>
                    <div class="milestone-time">
                        ${isAchieved ?
                            `已达成: ${milestone.achievedTime}` :
                            `预计: ${milestone.estimatedTime}`
                        }
                    </div>
                </div>
                <div class="milestone-importance importance-${milestone.importance}">
                    ${this.getImportanceText(milestone.importance)}
                </div>
            `;

            milestonesList.appendChild(milestoneElement);
        });

        section.appendChild(header);
        section.appendChild(milestonesList);

        return section;
    },

    /**
     * 创建替代路径区域
     * @returns {HTMLElement} 替代路径元素
     */
    createAlternativeSection() {
        const section = document.createElement('div');
        section.className = 'workflow-alternatives';

        const header = document.createElement('div');
        header.className = 'section-header';
        header.innerHTML = `
            <h4><i class="fas fa-random"></i> 替代路径</h4>
        `;

        const alternativesList = document.createElement('div');
        alternativesList.className = 'alternatives-list';

        const alternativePaths = this.workflowData.alternativePaths || [
            {
                id: 'emergency',
                name: '紧急处理路径',
                description: '适用于紧急情况的快速处理流程',
                estimatedDuration: 2,
                conditions: ['紧急工单', '重大影响'],
                available: false
            },
            {
                id: 'collaborative',
                name: '协办处理路径',
                description: '需要多部门协同处理的流程',
                estimatedDuration: 7,
                conditions: ['涉及多部门', '复杂问题'],
                available: true
            }
        ];

        alternativePaths.forEach(path => {
            const pathElement = document.createElement('div');
            pathElement.className = 'alternative-path';

            pathElement.innerHTML = `
                <div class="path-header">
                    <div class="path-name">${path.name}</div>
                    <div class="path-type ${path.available ? 'available' : 'unavailable'}">
                        ${path.available ? '可用' : '不可用'}
                    </div>
                </div>
                <div class="path-description">${path.description}</div>
                <div class="path-route">
                    <span class="route-info">预计用时: ${path.estimatedDuration}天</span>
                </div>
                ${path.conditions.length > 0 ? `
                    <div class="path-conditions">
                        <div class="conditions-label">触发条件:</div>
                        <ul class="conditions-list">
                            ${path.conditions.map(condition => `<li>${condition}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            `;

            alternativesList.appendChild(pathElement);
        });

        section.appendChild(header);
        section.appendChild(alternativesList);

        return section;
    },

    /**
     * 获取风险图标
     * @param {string} risk - 风险等级
     * @returns {string} 图标类名
     */
    getRiskIcon(risk) {
        const iconMap = {
            low: 'fa-check-circle',
            medium: 'fa-exclamation-triangle',
            high: 'fa-exclamation-circle'
        };
        return iconMap[risk] || 'fa-question-circle';
    },

    /**
     * 获取风险文本
     * @param {string} risk - 风险等级
     * @returns {string} 风险文本
     */
    getRiskText(risk) {
        const textMap = {
            low: '低',
            medium: '中',
            high: '高'
        };
        return textMap[risk] || '未知';
    },

    /**
     * 获取重要性文本
     * @param {string} importance - 重要性等级
     * @returns {string} 重要性文本
     */
    getImportanceText(importance) {
        const textMap = {
            low: '一般',
            high: '重要',
            critical: '关键'
        };
        return textMap[importance] || '一般';
    },

    /**
     * 获取步骤名称
     * @param {string} stepId - 步骤ID
     * @returns {string} 步骤名称
     */
    getStepName(stepId) {
        if (stepId === 'any') return '任意步骤';

        const step = this.workflowData.steps.find(s => s.id === stepId);
        return step ? step.name : stepId;
    },

    /**
     * 完成步骤
     * @param {string} stepId - 步骤ID
     */
    completeStep(stepId) {
        const step = this.workflowData.steps.find(s => s.id === stepId);
        if (!step) return;

        // 显示完成步骤对话框
        this.showCompleteStepDialog(step);
    },

    /**
     * 显示完成步骤对话框
     * @param {Object} step - 步骤数据
     */
    showCompleteStepDialog(step) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3>完成步骤: ${step.name}</h3>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="completeStepForm">
                        <div class="form-group">
                            <label for="completionNotes">处理说明</label>
                            <textarea id="completionNotes" class="form-control" rows="4"
                                placeholder="请详细说明本步骤的处理情况..." required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="nextStep">下一步骤</label>
                            <select id="nextStep" class="form-control" required>
                                ${step.nextSteps.map(nextStepId => {
                                    const nextStep = this.workflowData.steps.find(s => s.id === nextStepId);
                                    return `<option value="${nextStepId}">${nextStep ? nextStep.name : nextStepId}</option>`;
                                }).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="estimatedTime">预计用时(分钟)</label>
                            <input type="number" id="estimatedTime" class="form-control"
                                value="${step.estimatedDuration}" min="1" required>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="requiresAttention"> 需要特别关注
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
                    <button type="button" class="btn btn-primary" onclick="WorkflowComponent.saveStepCompletion('${step.id}')">确认完成</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 显示模态框
        setTimeout(() => {
            modal.style.display = 'flex';
        }, 100);
    },

    /**
     * 保存步骤完成
     * @param {string} stepId - 步骤ID
     */
    saveStepCompletion(stepId) {
        const form = document.getElementById('completeStepForm');
        const notes = document.getElementById('completionNotes').value;
        const nextStepId = document.getElementById('nextStep').value;
        const estimatedTime = parseInt(document.getElementById('estimatedTime').value);
        const requiresAttention = document.getElementById('requiresAttention').checked;

        if (!notes.trim()) {
            Utils.showToast('请填写处理说明', 'warning');
            return;
        }

        // 更新当前步骤
        const currentStep = this.workflowData.steps.find(s => s.id === stepId);
        if (currentStep) {
            currentStep.status = 'completed';
            currentStep.endTime = Utils.formatDateTime(new Date());
            currentStep.duration = Math.round((new Date() - new Date(currentStep.startTime)) / 60000);
        }

        // 更新下一步骤
        const nextStep = this.workflowData.steps.find(s => s.id === nextStepId);
        if (nextStep) {
            nextStep.status = 'current';
            nextStep.startTime = Utils.formatDateTime(new Date());
            nextStep.actor = `${AppState.currentUser.name} (工号: ${AppState.currentUser.id})`;
            nextStep.estimatedDuration = estimatedTime;
        }

        // 更新统计信息
        this.updateStatistics();

        // 重新渲染
        this.render();

        // 关闭模态框
        document.querySelector('.modal').remove();

        Utils.showToast('步骤完成成功', 'success');

        // 添加到时间轴
        if (window.TimelineComponent) {
            TimelineComponent.addEvent({
                id: 'event' + Date.now(),
                type: 'workflow',
                time: currentStep.endTime,
                actor: currentStep.actor,
                content: `完成步骤: ${currentStep.name}`,
                department: currentStep.department,
                details: {
                    stepId: stepId,
                    notes: notes,
                    duration: currentStep.duration,
                    nextStep: nextStep ? nextStep.name : null
                }
            });
        }

        // 如果需要特别关注，发送通知
        if (requiresAttention) {
            Utils.showToast('已标记为需要特别关注', 'info');
        }
    },

    /**
     * 查看步骤详情
     * @param {string} stepId - 步骤ID
     */
    viewStepDetails(stepId) {
        const step = this.workflowData.steps.find(s => s.id === stepId);
        if (!step) return;

        Utils.showToast(`查看步骤详情: ${step.name}`, 'info');
        // 这里可以实现详情查看逻辑
    },

    /**
     * 更新统计信息
     */
    updateStatistics() {
        const completedSteps = this.workflowData.steps.filter(s => s.status === 'completed').length;
        const totalSteps = this.workflowData.steps.length;
        const progressPercentage = Math.round((completedSteps / totalSteps) * 100);

        // 计算实际用时
        let actualElapsedTime = 0;
        this.workflowData.steps.forEach(step => {
            if (step.duration) {
                actualElapsedTime += step.duration;
            }
        });

        // 计算预计剩余时间
        let remainingTime = 0;
        this.workflowData.steps.forEach(step => {
            if (step.status === 'pending' || step.status === 'current') {
                remainingTime += step.estimatedDuration;
            }
        });

        // 更新统计数据
        this.workflowData.statistics = {
            ...this.workflowData.statistics,
            completedSteps: completedSteps,
            currentStepIndex: this.workflowData.steps.findIndex(s => s.status === 'current'),
            actualElapsedTime: actualElapsedTime,
            remainingTime: remainingTime,
            progressPercentage: progressPercentage,
            onSchedule: actualElapsedTime <= this.workflowData.statistics.estimatedTotalTime * 0.8,
            delayRisk: actualElapsedTime > this.workflowData.statistics.estimatedTotalTime * 0.8 ? 'high' : 'low'
        };
    },

    /**
     * 切换工作流视图
     * @param {string} viewType - 视图类型
     */
    switchWorkflowView(viewType) {
        const buttons = document.querySelectorAll('.view-toggle');
        buttons.forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-view="${viewType}"]`).classList.add('active');

        const content = document.getElementById('workflowContent');
        if (!content) return;

        switch(viewType) {
            case 'workflow':
                content.innerHTML = this.renderWorkflowContent();
                break;
            case 'timeline':
                content.innerHTML = this.renderTimelineView();
                break;
            case 'flowchart':
                content.innerHTML = this.renderFlowchartView();
                break;
        }

        Utils.showToast(`已切换到${this.getViewTypeName(viewType)}`, 'info');
    },

    /**
     * 获取视图类型名称
     * @param {string} viewType - 视图类型
     * @returns {string} 视图名称
     */
    getViewTypeName(viewType) {
        const nameMap = {
            workflow: '流转视图',
            timeline: '时间轴视图',
            flowchart: '流程图视图'
        };
        return nameMap[viewType] || viewType;
    },

    /**
     * 渲染时间轴视图
     * @returns {string} 时间轴视图HTML
     */
    renderTimelineView() {
        const historyData = this.getProcessHistoryData();

        return `
            <div class="timeline-view">
                <div class="timeline-container">
                    ${historyData.map(event => `
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas ${this.getEventIcon(event.type)}"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <div class="timeline-time">${event.time}</div>
                                    <div class="timeline-actor">${event.actor}</div>
                                </div>
                                <div class="timeline-action">${event.content}</div>
                                <div class="timeline-details">${event.details}</div>
                                <div class="timeline-department">${event.department}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    /**
     * 渲染流程图视图
     * @returns {string} 流程图视图HTML
     */
    renderFlowchartView() {
        return `
            <div class="flowchart-view">
                <div class="flowchart-container">
                    ${this.workflowData.steps.map((step, index) => `
                        <div class="flowchart-node ${step.status}">
                            <div class="node-header">
                                <div class="node-number">${index + 1}</div>
                                <div class="node-status">
                                    <i class="fas ${step.status === 'completed' ? 'fa-check' :
                                                   step.status === 'current' ? 'fa-play' : 'fa-circle'}"></i>
                                </div>
                            </div>
                            <div class="node-content">
                                <div class="node-title">${step.name}</div>
                                <div class="node-department">${step.department}</div>
                                <div class="node-timing">
                                    ${step.duration ? `实际: ${step.duration}分钟` : `预计: ${step.estimatedDuration}分钟`}
                                </div>
                            </div>
                            ${index < this.workflowData.steps.length - 1 ? `
                                <div class="node-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    /**
     * 获取事件图标
     * @param {string} eventType - 事件类型
     * @returns {string} 图标类名
     */
    getEventIcon(eventType) {
        const iconMap = {
            create: 'fa-plus-circle',
            assign: 'fa-hand-point-right',
            receive: 'fa-inbox',
            process: 'fa-cogs',
            note: 'fa-sticky-note',
            attachment: 'fa-paperclip',
            review: 'fa-search',
            complete: 'fa-check-circle',
            callback: 'fa-phone'
        };
        return iconMap[eventType] || 'fa-circle';
    },

    /**
     * 添加步骤备注
     * @param {string} stepId - 步骤ID
     */
    addStepNote(stepId) {
        const note = prompt('请输入备注内容:');
        if (note && note.trim()) {
            Utils.showToast('备注添加成功', 'success');
            // 这里可以实现添加备注的逻辑
        }
    },

    /**
     * 导出流转历史
     */
    exportHistory() {
        Utils.showToast('正在导出流转历史...', 'info');
        setTimeout(() => {
            Utils.showToast('流转历史导出成功', 'success');
        }, 2000);
    },

    /**
     * 打印流转历史
     */
    printHistory() {
        Utils.showToast('正在准备打印...', 'info');
        setTimeout(() => {
            window.print();
        }, 1000);
    }
};

// 将WorkflowComponent暴露到全局作用域
window.WorkflowComponent = WorkflowComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    WorkflowComponent.initialize();
});
