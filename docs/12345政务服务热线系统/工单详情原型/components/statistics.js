/**
 * 12345政务服务热线系统 - 统计信息组件
 * @description 工单统计和分析信息展示组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 统计信息组件
 */
const StatisticsComponent = {
    /**
     * 统计信息容器
     */
    container: null,
    
    /**
     * 统计数据
     */
    statisticsData: null,
    
    /**
     * 初始化统计信息组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'statisticsInfo') {
        console.log('初始化统计信息组件');
        this.container = document.getElementById(containerId);

        if (!this.container) {
            console.error('找不到统计信息容器元素:', containerId);
            // 尝试延迟初始化
            setTimeout(() => {
                this.initialize(containerId);
            }, 1000);
            return;
        }

        console.log('统计信息容器找到:', this.container);

        // 加载统计数据
        this.loadStatisticsData();

        console.log('统计数据加载完成:', this.statisticsData);

        // 渲染统计信息
        this.render();

        console.log('统计信息渲染完成');
    },
    
    /**
     * 加载统计数据
     */
    loadStatisticsData() {
        // 使用统一的Mock数据
        if (window.MockData && window.MockData.statistics) {
            this.statisticsData = window.MockData.statistics;
        } else {
            // 备用数据
            this.statisticsData = {
            // 处理效率统计
            efficiency: {
                totalDuration: 2.5, // 总处理时长(天)
                stages: [
                    { name: '接收处理', planned: 1, actual: 0.8, efficiency: 120 },
                    { name: '现场处理', planned: 5, actual: 2.5, efficiency: 200 },
                    { name: '审核办结', planned: 1, actual: null, efficiency: null }
                ],
                comparison: {
                    averageDuration: 4.2, // 同类工单平均时长
                    fasterThan: 68, // 比68%的同类工单快
                    ranking: 'excellent' // 效率等级: excellent, good, average, poor
                },
                slaCompliance: {
                    isOnTime: true,
                    remainingTime: 5.5,
                    complianceRate: 95 // 时限达标率
                }
            },
            
            // 绩效积分
            performance: {
                totalScore: 85,
                breakdown: {
                    timelyProcessing: 25, // 及时处理
                    qualityRating: 30, // 处理质量
                    customerSatisfaction: 20, // 客户满意度
                    collaboration: 10 // 协作配合
                },
                factors: [
                    { name: '及时接收', impact: '+5', description: '在规定时间内接收工单' },
                    { name: '主动沟通', impact: '+3', description: '主动联系客户了解情况' },
                    { name: '现场处理', impact: '+8', description: '安排现场检查和处理' },
                    { name: '协调配合', impact: '+2', description: '与相关部门良好协作' }
                ],
                penalties: [],
                bonuses: [
                    { name: '提前完成', impact: '+5', description: '提前完成处理任务' }
                ],
                trend: {
                    lastMonth: 82,
                    change: '+3',
                    direction: 'up'
                }
            },
            
            // 相关统计
            related: {
                similarTickets: {
                    total: 15,
                    thisMonth: 3,
                    trend: 'stable',
                    avgSatisfaction: 4.2
                },
                departmentStats: {
                    totalTickets: 128,
                    completedTickets: 115,
                    avgDuration: 3.8,
                    satisfactionRate: 4.3
                },
                customerStats: {
                    totalTickets: 5,
                    completedTickets: 4,
                    avgSatisfaction: 4.2,
                    lastTicketDate: '2024-11-15'
                }
            },
            
            // 质量评估
            quality: {
                overallRating: 4.5,
                aspects: [
                    { name: '响应速度', rating: 4.8, weight: 25 },
                    { name: '处理质量', rating: 4.5, weight: 30 },
                    { name: '沟通效果', rating: 4.2, weight: 20 },
                    { name: '解决效果', rating: 4.6, weight: 25 }
                ],
                improvements: [
                    '建议加强与市民的沟通频次',
                    '可以进一步优化现场处理流程'
                ]
            }
        };
        }
    },
    
    /**
     * 渲染统计信息
     */
    render() {
        if (!this.container) {
            console.warn('统计信息容器不存在');
            return;
        }

        if (!this.statisticsData) {
            console.warn('统计数据不存在，重新加载');
            this.loadStatisticsData();
        }

        // 清空容器
        this.container.innerHTML = '';

        try {
            // 创建统计信息网格容器
            const statisticsContainer = document.createElement('div');
            statisticsContainer.className = 'statistics-container';

            // 创建处理效率区域
            const efficiencySection = this.createEfficiencySection();
            if (efficiencySection) {
                statisticsContainer.appendChild(efficiencySection);
            }

            // 创建绩效积分区域
            const performanceSection = this.createPerformanceSection();
            if (performanceSection) {
                statisticsContainer.appendChild(performanceSection);
            }

            // 创建相关统计区域
            const relatedSection = this.createRelatedSection();
            if (relatedSection) {
                statisticsContainer.appendChild(relatedSection);
            }

            // 创建质量评估区域
            const qualitySection = this.createQualitySection();
            if (qualitySection) {
                statisticsContainer.appendChild(qualitySection);
            }

            // 将网格容器添加到主容器
            this.container.appendChild(statisticsContainer);
        } catch (error) {
            console.error('渲染统计信息时出错:', error);
            this.container.innerHTML = '<div class="error-message">统计信息加载失败</div>';
        }
    },
    
    /**
     * 创建处理效率区域
     * @returns {HTMLElement} 处理效率元素
     */
    createEfficiencySection() {
        const section = document.createElement('div');
        section.className = 'statistics-efficiency';

        const efficiency = {
            totalDuration: this.statisticsData.efficiency.totalDuration || 2.5,
            comparison: this.statisticsData.efficiency.comparison || {
                fasterThan: 68,
                averageDuration: 4.2,
                ranking: 'excellent'
            },
            slaCompliance: this.statisticsData.efficiency.slaCompliance || {
                isOnTime: true,
                remainingTime: 5.5,
                complianceRate: 95
            },
            stages: this.statisticsData.efficiency.stages || [
                { name: '接收处理', planned: 1, actual: 0.8, efficiency: 120 },
                { name: '现场处理', planned: 5, actual: 2.5, efficiency: 200 },
                { name: '审核办结', planned: 1, actual: null, efficiency: null }
            ]
        };
        
        section.innerHTML = `
            <div class="section-header">
                <h4><i class="fas fa-tachometer-alt"></i> 处理效率</h4>
            </div>
            <div class="efficiency-overview">
                <div class="efficiency-card">
                    <div class="card-value">${efficiency.totalDuration}天</div>
                    <div class="card-label">已用时长</div>
                </div>
                <div class="efficiency-card">
                    <div class="card-value">${efficiency.comparison.fasterThan}%</div>
                    <div class="card-label">效率超越</div>
                </div>
                <div class="efficiency-card">
                    <div class="card-value ${efficiency.slaCompliance.isOnTime ? 'text-success' : 'text-danger'}">
                        ${efficiency.slaCompliance.isOnTime ? '达标' : '超时'}
                    </div>
                    <div class="card-label">时限状态</div>
                </div>
            </div>
            <div class="efficiency-stages">
                <div class="stages-header">各阶段效率</div>
                <div class="stages-list">
                    ${efficiency.stages.map(stage => `
                        <div class="stage-item">
                            <div class="stage-name">${stage.name}</div>
                            <div class="stage-progress">
                                <div class="stage-times">
                                    <span>计划: ${stage.planned}天</span>
                                    ${stage.actual !== null ? `<span>实际: ${stage.actual}天</span>` : '<span>进行中</span>'}
                                </div>
                                ${stage.efficiency !== null ? `
                                    <div class="stage-efficiency ${stage.efficiency >= 100 ? 'text-success' : 'text-warning'}">
                                        效率: ${stage.efficiency}%
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="efficiency-comparison">
                <div class="comparison-header">效率对比</div>
                <div class="comparison-content">
                    <div class="comparison-item">
                        <span class="comparison-label">同类工单平均:</span>
                        <span class="comparison-value">${efficiency.comparison.averageDuration}天</span>
                    </div>
                    <div class="comparison-item">
                        <span class="comparison-label">效率等级:</span>
                        <span class="comparison-value efficiency-${efficiency.comparison.ranking}">
                            ${this.getEfficiencyRankingText(efficiency.comparison.ranking)}
                        </span>
                    </div>
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建绩效积分区域
     * @returns {HTMLElement} 绩效积分元素
     */
    createPerformanceSection() {
        const section = document.createElement('div');
        section.className = 'statistics-performance';
        
        const performance = {
            totalScore: this.statisticsData.performance.totalScore || 85,
            trend: this.statisticsData.performance.trend || {
                direction: 'up',
                change: '+3',
                lastMonth: 82
            },
            breakdown: this.statisticsData.performance.breakdown || {
                timelyProcessing: 25,
                qualityRating: 30,
                customerSatisfaction: 20,
                collaboration: 10
            },
            factors: this.statisticsData.performance.factors || [
                { name: '及时接收', impact: '+5', description: '在规定时间内接收工单' },
                { name: '主动沟通', impact: '+3', description: '主动联系客户了解情况' },
                { name: '现场处理', impact: '+8', description: '安排现场检查和处理' }
            ],
            bonuses: this.statisticsData.performance.bonuses || [
                { name: '提前完成', impact: '+5', description: '提前完成处理任务' }
            ]
        };
        
        section.innerHTML = `
            <div class="section-header">
                <h4><i class="fas fa-star"></i> 绩效积分</h4>
                <div class="performance-trend ${performance.trend.direction}">
                    <span class="trend-value">${performance.trend.change}</span>
                    <i class="fas fa-arrow-${performance.trend.direction === 'up' ? 'up' : 'down'}"></i>
                </div>
            </div>
            <div class="performance-overview">
                <div class="performance-score">
                    <div class="score-circle">
                        <div class="score-value">${performance.totalScore}</div>
                        <div class="score-label">总分</div>
                    </div>
                </div>
                <div class="performance-breakdown">
                    ${Object.entries(performance.breakdown).map(([key, value]) => `
                        <div class="breakdown-item">
                            <span class="breakdown-label">${this.getPerformanceLabel(key)}:</span>
                            <span class="breakdown-value">${value}分</span>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="performance-factors">
                <div class="factors-header">积分构成</div>
                <div class="factors-list">
                    ${performance.factors.map(factor => `
                        <div class="factor-item positive">
                            <div class="factor-impact">${factor.impact}</div>
                            <div class="factor-content">
                                <div class="factor-name">${factor.name}</div>
                                <div class="factor-description">${factor.description}</div>
                            </div>
                        </div>
                    `).join('')}
                    ${performance.bonuses.map(bonus => `
                        <div class="factor-item bonus">
                            <div class="factor-impact">${bonus.impact}</div>
                            <div class="factor-content">
                                <div class="factor-name">${bonus.name}</div>
                                <div class="factor-description">${bonus.description}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建相关统计区域
     * @returns {HTMLElement} 相关统计元素
     */
    createRelatedSection() {
        const section = document.createElement('div');
        section.className = 'statistics-related';
        
        const related = {
            similarTickets: this.statisticsData.related.similarTickets || {
                total: 15,
                thisMonth: 3,
                trend: 'stable',
                avgSatisfaction: 4.2
            },
            departmentStats: this.statisticsData.related.departmentStats || {
                totalTickets: 128,
                completedTickets: 115,
                avgDuration: 3.8,
                satisfactionRate: 4.3
            },
            customerStats: this.statisticsData.related.customerStats || {
                totalTickets: 5,
                completedTickets: 4,
                avgSatisfaction: 4.2,
                lastTicketDate: '2024-11-15'
            }
        };
        
        section.innerHTML = `
            <div class="section-header">
                <h4><i class="fas fa-chart-bar"></i> 相关统计</h4>
            </div>
            <div class="related-stats">
                <div class="statistics-stat-group">
                    <div class="statistics-stat-group-title">相似工单</div>
                    <div class="statistics-stat-items">
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.similarTickets.total}</span>
                            <span class="statistics-stat-label">历史总数</span>
                        </div>
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.similarTickets.thisMonth}</span>
                            <span class="statistics-stat-label">本月数量</span>
                        </div>
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.similarTickets.avgSatisfaction}</span>
                            <span class="statistics-stat-label">平均满意度</span>
                        </div>
                    </div>
                </div>
                <div class="statistics-stat-group">
                    <div class="statistics-stat-group-title">部门统计</div>
                    <div class="statistics-stat-items">
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.departmentStats.totalTickets}</span>
                            <span class="statistics-stat-label">总工单数</span>
                        </div>
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.departmentStats.avgDuration}天</span>
                            <span class="statistics-stat-label">平均时长</span>
                        </div>
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.departmentStats.satisfactionRate}</span>
                            <span class="statistics-stat-label">满意度</span>
                        </div>
                    </div>
                </div>
                <div class="statistics-stat-group">
                    <div class="statistics-stat-group-title">客户统计</div>
                    <div class="statistics-stat-items">
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.customerStats.totalTickets}</span>
                            <span class="statistics-stat-label">历史工单</span>
                        </div>
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.customerStats.avgSatisfaction}</span>
                            <span class="statistics-stat-label">平均满意度</span>
                        </div>
                        <div class="statistics-stat-item">
                            <span class="statistics-stat-value">${related.customerStats.lastTicketDate}</span>
                            <span class="statistics-stat-label">上次工单</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建质量评估区域
     * @returns {HTMLElement} 质量评估元素
     */
    createQualitySection() {
        const section = document.createElement('div');
        section.className = 'statistics-quality';
        
        const quality = {
            overallRating: this.statisticsData.quality.overallRating || 4.5,
            aspects: this.statisticsData.quality.aspects || [
                { name: '响应速度', rating: 4.8, weight: 25 },
                { name: '处理质量', rating: 4.5, weight: 30 },
                { name: '沟通效果', rating: 4.2, weight: 20 },
                { name: '解决效果', rating: 4.6, weight: 25 }
            ],
            improvements: this.statisticsData.quality.improvements || [
                '建议加强与市民的沟通频次',
                '可以进一步优化现场处理流程'
            ]
        };
        
        section.innerHTML = `
            <div class="section-header">
                <h4><i class="fas fa-award"></i> 质量评估</h4>
                <div class="quality-rating">
                    <span class="rating-value">${quality.overallRating}</span>
                    <div class="rating-stars">
                        ${this.generateStarRating(quality.overallRating)}
                    </div>
                </div>
            </div>
            <div class="quality-aspects">
                <div class="aspects-header">评估维度</div>
                <div class="aspects-list">
                    ${quality.aspects.map(aspect => `
                        <div class="aspect-item">
                            <div class="aspect-info">
                                <span class="aspect-name">${aspect.name}</span>
                                <span class="aspect-weight">权重: ${aspect.weight}%</span>
                            </div>
                            <div class="aspect-rating">
                                <span class="rating-value">${aspect.rating}</span>
                                <div class="rating-bar">
                                    <div class="rating-fill" style="width: ${(aspect.rating / 5) * 100}%"></div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            ${quality.improvements.length > 0 ? `
                <div class="quality-improvements">
                    <div class="improvements-header">改进建议</div>
                    <div class="improvements-list">
                        ${quality.improvements.map(improvement => `
                            <div class="improvement-item">
                                <i class="fas fa-lightbulb"></i>
                                <span>${improvement}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        `;
        
        return section;
    },
    
    /**
     * 获取效率等级文本
     * @param {string} ranking - 效率等级
     * @returns {string} 等级文本
     */
    getEfficiencyRankingText(ranking) {
        const rankingMap = {
            excellent: '优秀',
            good: '良好',
            average: '一般',
            poor: '较差'
        };
        
        return rankingMap[ranking] || '未知';
    },
    
    /**
     * 获取绩效标签
     * @param {string} key - 绩效键名
     * @returns {string} 标签文本
     */
    getPerformanceLabel(key) {
        const labelMap = {
            timelyProcessing: '及时处理',
            qualityRating: '处理质量',
            customerSatisfaction: '客户满意度',
            collaboration: '协作配合'
        };
        
        return labelMap[key] || key;
    },
    
    /**
     * 生成星级评分
     * @param {number} rating - 评分值
     * @returns {string} 星级HTML
     */
    generateStarRating(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        
        let starsHtml = '';
        
        // 实心星
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star"></i>';
        }
        
        // 半星
        if (hasHalfStar) {
            starsHtml += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // 空心星
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star"></i>';
        }
        
        return starsHtml;
    },
    
    /**
     * 更新统计数据
     * @param {Object} newData - 新的统计数据
     */
    updateStatisticsData(newData) {
        this.statisticsData = { ...this.statisticsData, ...newData };
        this.render();
    },
    
    /**
     * 获取统计数据
     * @returns {Object} 统计数据
     */
    getStatisticsData() {
        return this.statisticsData;
    },
    
    /**
     * 导出统计报告
     */
    exportStatisticsReport() {
        Utils.showToast('正在生成统计报告...', 'info');
        
        // 模拟导出过程
        setTimeout(() => {
            Utils.showToast('统计报告导出成功', 'success');
        }, 2000);
    }
};

// 将StatisticsComponent暴露到全局作用域
window.StatisticsComponent = StatisticsComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    StatisticsComponent.initialize();
});
