/**
 * 12345政务服务热线系统 - 权限控制组件
 * @description 基于角色的权限控制，包括信息脱敏、操作权限、数据可见性等
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 权限控制组件
 */
const PermissionsComponent = {
    /**
     * 权限配置
     */
    permissionConfig: {
        // 角色权限定义
        roles: {
            operator: {
                name: '话务员',
                level: 1,
                permissions: [
                    'view_basic_info',
                    'create_ticket',
                    'edit_ticket_content',
                    'assign_ticket',
                    'add_notes',
                    'upload_attachments',
                    'callback_customer',
                    'view_statistics_basic'
                ],
                restrictions: [
                    'no_sensitive_data',
                    'no_internal_notes',
                    'no_admin_actions'
                ],
                dataAccess: {
                    customerInfo: 'masked', // 脱敏显示
                    internalNotes: 'hidden', // 完全隐藏
                    sensitiveAttachments: 'hidden',
                    departmentInternal: 'hidden'
                }
            },
            processor: {
                name: '处理人员',
                level: 2,
                permissions: [
                    'view_basic_info',
                    'view_full_customer_info',
                    'edit_ticket_content',
                    'process_ticket',
                    'add_notes',
                    'add_internal_notes',
                    'upload_attachments',
                    'request_extension',
                    'transfer_ticket',
                    'view_statistics_detailed'
                ],
                restrictions: [
                    'no_admin_actions',
                    'no_cross_department_data'
                ],
                dataAccess: {
                    customerInfo: 'full',
                    internalNotes: 'department_only',
                    sensitiveAttachments: 'department_only',
                    departmentInternal: 'own_department'
                }
            },
            reviewer: {
                name: '审核人员',
                level: 3,
                permissions: [
                    'view_basic_info',
                    'view_full_customer_info',
                    'review_ticket',
                    'approve_completion',
                    'reject_completion',
                    'add_notes',
                    'add_internal_notes',
                    'view_all_attachments',
                    'view_statistics_detailed',
                    'export_data'
                ],
                restrictions: [
                    'no_admin_actions'
                ],
                dataAccess: {
                    customerInfo: 'full',
                    internalNotes: 'department_only',
                    sensitiveAttachments: 'full',
                    departmentInternal: 'own_department'
                }
            },
            admin: {
                name: '管理员',
                level: 4,
                permissions: [
                    'view_basic_info',
                    'view_full_customer_info',
                    'edit_ticket_content',
                    'process_ticket',
                    'review_ticket',
                    'approve_completion',
                    'reject_completion',
                    'add_notes',
                    'add_internal_notes',
                    'upload_attachments',
                    'view_all_attachments',
                    'transfer_ticket',
                    'reassign_ticket',
                    'cancel_ticket',
                    'reopen_ticket',
                    'view_statistics_full',
                    'export_data',
                    'manage_users',
                    'view_audit_logs'
                ],
                restrictions: [],
                dataAccess: {
                    customerInfo: 'full',
                    internalNotes: 'full',
                    sensitiveAttachments: 'full',
                    departmentInternal: 'all_departments'
                }
            },
            callback: {
                name: '回访员',
                level: 2,
                permissions: [
                    'view_basic_info',
                    'view_customer_contact',
                    'callback_customer',
                    'record_satisfaction',
                    'add_notes',
                    'view_statistics_basic'
                ],
                restrictions: [
                    'no_sensitive_data',
                    'no_internal_notes',
                    'no_processing_actions'
                ],
                dataAccess: {
                    customerInfo: 'contact_only',
                    internalNotes: 'hidden',
                    sensitiveAttachments: 'hidden',
                    departmentInternal: 'hidden'
                }
            },
            system_admin: {
                name: '系统管理员',
                level: 5,
                permissions: ['*'], // 所有权限
                restrictions: [],
                dataAccess: {
                    customerInfo: 'full',
                    internalNotes: 'full',
                    sensitiveAttachments: 'full',
                    departmentInternal: 'all_departments'
                }
            }
        },
        
        // 数据脱敏规则
        maskingRules: {
            phone: {
                pattern: /(\d{3})\d{4}(\d{4})/,
                replacement: '$1****$2'
            },
            idCard: {
                pattern: /(\d{6})\d{8}(\d{4})/,
                replacement: '$1********$2'
            },
            address: {
                pattern: /(.{2}).*(.{2})/,
                replacement: '$1****$2'
            },
            email: {
                pattern: /(.{2}).*(@.*)/,
                replacement: '$1****$2'
            },
            name: {
                pattern: /(.)(.+)/,
                replacement: '$1**'
            }
        },
        
        // 操作权限映射
        actionPermissions: {
            'create_ticket': ['create_ticket'],
            'edit_ticket': ['edit_ticket_content'],
            'assign_ticket': ['assign_ticket'],
            'process_ticket': ['process_ticket'],
            'review_ticket': ['review_ticket'],
            'complete_ticket': ['approve_completion'],
            'reject_ticket': ['reject_completion'],
            'transfer_ticket': ['transfer_ticket'],
            'cancel_ticket': ['cancel_ticket'],
            'reopen_ticket': ['reopen_ticket'],
            'add_note': ['add_notes'],
            'add_internal_note': ['add_internal_notes'],
            'upload_attachment': ['upload_attachments'],
            'callback': ['callback_customer'],
            'export': ['export_data'],
            'view_audit': ['view_audit_logs']
        }
    },
    
    /**
     * 检查用户是否有指定权限
     * @param {string} permission - 权限名称
     * @param {string} userRole - 用户角色
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission, userRole = null) {
        // 演示模式：所有权限都返回true
        return true;
    },
    
    /**
     * 检查用户是否可以执行指定操作
     * @param {string} action - 操作名称
     * @param {string} userRole - 用户角色
     * @returns {boolean} 是否可以执行
     */
    canPerformAction(action, userRole = null) {
        const requiredPermissions = this.permissionConfig.actionPermissions[action];
        if (!requiredPermissions) return false;
        
        return requiredPermissions.every(permission => 
            this.hasPermission(permission, userRole)
        );
    },
    
    /**
     * 获取用户数据访问级别
     * @param {string} dataType - 数据类型
     * @param {string} userRole - 用户角色
     * @returns {string} 访问级别
     */
    getDataAccessLevel(dataType, userRole = null) {
        // 演示模式：所有数据都完全可见
        return 'full';
    },
    
    /**
     * 脱敏处理数据
     * @param {string} data - 原始数据
     * @param {string} type - 数据类型
     * @returns {string} 脱敏后的数据
     */
    maskData(data, type) {
        if (!data) return data;
        
        const rule = this.permissionConfig.maskingRules[type];
        if (!rule) return data;
        
        return data.replace(rule.pattern, rule.replacement);
    },
    
    /**
     * 根据权限过滤客户信息
     * @param {Object} customerInfo - 客户信息
     * @param {string} userRole - 用户角色
     * @returns {Object} 过滤后的客户信息
     */
    filterCustomerInfo(customerInfo, userRole = null) {
        const accessLevel = this.getDataAccessLevel('customerInfo', userRole);
        
        if (accessLevel === 'hidden') {
            return {
                name: '***',
                phone: '***',
                address: '***'
            };
        }
        
        if (accessLevel === 'masked') {
            return {
                ...customerInfo,
                name: this.maskData(customerInfo.name, 'name'),
                phone: this.maskData(customerInfo.phone, 'phone'),
                idCard: this.maskData(customerInfo.idCard, 'idCard'),
                address: this.maskData(customerInfo.address, 'address'),
                email: this.maskData(customerInfo.email, 'email')
            };
        }
        
        if (accessLevel === 'contact_only') {
            return {
                name: customerInfo.name,
                phone: customerInfo.phone,
                address: '***',
                idCard: '***',
                email: '***'
            };
        }
        
        // full access
        return customerInfo;
    },
    
    /**
     * 根据权限过滤内部备注
     * @param {Array} notes - 备注列表
     * @param {string} userRole - 用户角色
     * @returns {Array} 过滤后的备注列表
     */
    filterInternalNotes(notes, userRole = null) {
        const accessLevel = this.getDataAccessLevel('internalNotes', userRole);
        const currentDepartment = AppState.currentUser?.department;
        
        if (accessLevel === 'hidden') {
            return notes.filter(note => !note.isInternal);
        }
        
        if (accessLevel === 'department_only') {
            return notes.filter(note => 
                !note.isInternal || note.department === currentDepartment
            );
        }
        
        // full access
        return notes;
    },
    
    /**
     * 根据权限过滤附件
     * @param {Array} attachments - 附件列表
     * @param {string} userRole - 用户角色
     * @returns {Array} 过滤后的附件列表
     */
    filterAttachments(attachments, userRole = null) {
        const accessLevel = this.getDataAccessLevel('sensitiveAttachments', userRole);
        const currentDepartment = AppState.currentUser?.department;
        
        if (accessLevel === 'hidden') {
            return attachments.filter(att => !att.isSensitive);
        }
        
        if (accessLevel === 'department_only') {
            return attachments.filter(att => 
                !att.isSensitive || att.department === currentDepartment
            );
        }
        
        // full access
        return attachments;
    },
    
    /**
     * 检查是否可以查看指定部门的数据
     * @param {string} targetDepartment - 目标部门
     * @param {string} userRole - 用户角色
     * @returns {boolean} 是否可以查看
     */
    canViewDepartmentData(targetDepartment, userRole = null) {
        const accessLevel = this.getDataAccessLevel('departmentInternal', userRole);
        const currentDepartment = AppState.currentUser?.department;
        
        if (accessLevel === 'hidden') return false;
        if (accessLevel === 'own_department') return targetDepartment === currentDepartment;
        if (accessLevel === 'all_departments') return true;
        
        return false;
    },
    
    /**
     * 获取用户可执行的操作列表
     * @param {string} userRole - 用户角色
     * @returns {Array} 可执行的操作列表
     */
    getAvailableActions(userRole = null) {
        const actions = [];
        
        for (const [action, permissions] of Object.entries(this.permissionConfig.actionPermissions)) {
            if (this.canPerformAction(action, userRole)) {
                actions.push(action);
            }
        }
        
        return actions;
    },
    
    /**
     * 应用权限控制到页面元素
     */
    applyPermissions() {
        const userRole = AppState.currentUser?.role || 'operator';
        
        // 隐藏无权限的操作按钮
        this.hideUnauthorizedElements(userRole);
        
        // 应用数据脱敏
        this.applyDataMasking(userRole);
        
        // 显示权限提示
        this.showPermissionIndicators(userRole);
    },
    
    /**
     * 隐藏无权限的页面元素
     * @param {string} userRole - 用户角色
     */
    hideUnauthorizedElements(userRole) {
        // 隐藏无权限的按钮
        document.querySelectorAll('[data-permission]').forEach(element => {
            const requiredPermission = element.dataset.permission;
            if (!this.hasPermission(requiredPermission, userRole)) {
                element.style.display = 'none';
            }
        });
        
        // 隐藏无权限的操作
        document.querySelectorAll('[data-action]').forEach(element => {
            const requiredAction = element.dataset.action;
            if (!this.canPerformAction(requiredAction, userRole)) {
                element.style.display = 'none';
            }
        });
    },
    
    /**
     * 应用数据脱敏
     * @param {string} userRole - 用户角色
     */
    applyDataMasking(userRole) {
        // 脱敏客户信息
        document.querySelectorAll('[data-sensitive]').forEach(element => {
            const dataType = element.dataset.sensitive;
            const originalText = element.textContent;
            
            const accessLevel = this.getDataAccessLevel('customerInfo', userRole);
            if (accessLevel === 'masked' || accessLevel === 'hidden') {
                element.textContent = this.maskData(originalText, dataType);
                element.classList.add('masked-data');
            }
        });
    },
    
    /**
     * 显示权限指示器
     * @param {string} userRole - 用户角色
     */
    showPermissionIndicators(userRole) {
        const roleConfig = this.permissionConfig.roles[userRole];
        if (!roleConfig) return;
        
        // 在页面顶部显示当前角色
        const roleIndicator = document.querySelector('.current-role');
        if (roleIndicator) {
            roleIndicator.textContent = roleConfig.name;
            roleIndicator.className = `current-role role-${userRole}`;
        }
        
        // 显示权限级别
        const permissionLevel = document.querySelector('.permission-level');
        if (permissionLevel) {
            permissionLevel.textContent = `权限级别: ${roleConfig.level}`;
        }
    }
};

// 将PermissionsComponent暴露到全局作用域
window.PermissionsComponent = PermissionsComponent;

// 页面加载完成后应用权限控制
document.addEventListener('DOMContentLoaded', function() {
    PermissionsComponent.applyPermissions();
});
