/**
 * 办结信息记录组件
 * 负责显示和管理工单的办结信息
 */
class CompleteTicketComponent {
    constructor() {
        this.completionData = null;
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.loadCompletionData();
        this.render();
    }

    /**
     * 加载办结数据
     */
    loadCompletionData() {
        if (window.MockData && window.MockData.completionInfo) {
            this.completionData = window.MockData.completionInfo;
        } else {
            // 备用数据
            this.completionData = {
                isCompleted: false,
                completionTime: null,
                completionStaff: null,
                completionMethod: null,
                completionDescription: '',
                completionResult: '',
                completionAttachments: [],
                followUpRequired: false,
                qualityScore: null,
                remarks: ''
            };
        }
    }

    /**
     * 渲染办结信息
     */
    render() {
        const container = document.getElementById('completeInfo');
        if (!container) return;

        if (this.completionData.isCompleted) {
            container.innerHTML = this.renderCompletedInfo();
        } else {
            container.innerHTML = this.renderPendingInfo();
        }

        // 更新办结按钮状态
        this.updateCompleteButton();
    }

    /**
     * 渲染已办结信息
     */
    renderCompletedInfo() {
        const data = this.completionData;
        
        return `
            <div class="completion-info completed">
                <div class="completion-status">
                    <div class="status-badge completed">
                        <i class="fas fa-check-circle"></i>
                        已办结
                    </div>
                    <div class="completion-time">
                        办结时间：${data.completionTime}
                    </div>
                </div>

                <div class="completion-details">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>办结人员</label>
                            <div class="detail-value">
                                <span class="staff-name">${data.completionStaff.name}</span>
                                <span class="staff-info">${data.completionStaff.position} - ${data.completionStaff.department}</span>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <label>办结方式</label>
                            <div class="detail-value">
                                <span class="completion-method">${data.completionMethod}</span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <label>质量评分</label>
                            <div class="detail-value">
                                <div class="quality-score">
                                    ${this.renderStarRating(data.qualityScore)}
                                    <span class="score-text">${data.qualityScore}/5分</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="completion-content">
                        <div class="content-section">
                            <label>办结说明</label>
                            <div class="content-text">${data.completionDescription}</div>
                        </div>

                        <div class="content-section">
                            <label>办结结果</label>
                            <div class="content-text">${data.completionResult}</div>
                        </div>

                        ${data.remarks ? `
                            <div class="content-section">
                                <label>备注说明</label>
                                <div class="content-text">${data.remarks}</div>
                            </div>
                        ` : ''}

                        ${data.completionAttachments && data.completionAttachments.length > 0 ? `
                            <div class="content-section">
                                <label>办结附件</label>
                                <div class="completion-attachments">
                                    ${data.completionAttachments.map(att => this.renderAttachment(att)).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>


                </div>
            </div>
        `;
    }

    /**
     * 渲染待办结信息
     */
    renderPendingInfo() {
        return `
            <div class="completion-info pending">
                <div class="completion-status">
                    <div class="status-badge pending">
                        <i class="fas fa-clock"></i>
                        待办结
                    </div>
                </div>
                
                <div class="pending-message">
                    <i class="fas fa-info-circle"></i>
                    <p>工单处理完成后，请点击"办结工单"按钮填写办结信息。</p>
                </div>
            </div>
        `;
    }

    /**
     * 渲染星级评分
     */
    renderStarRating(score) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= score) {
                stars += '<i class="fas fa-star star-filled"></i>';
            } else {
                stars += '<i class="fas fa-star star-empty"></i>';
            }
        }
        return `<div class="star-rating">${stars}</div>`;
    }

    /**
     * 渲染附件
     */
    renderAttachment(attachment) {
        const iconClass = this.getAttachmentIcon(attachment.type);
        
        return `
            <div class="attachment-item-small">
                <div class="attachment-icon">
                    <i class="${iconClass}"></i>
                </div>
                <div class="attachment-info">
                    <div class="attachment-name">${attachment.name}</div>
                    <div class="attachment-meta">
                        <span class="attachment-size">${attachment.size}</span>
                        <span class="attachment-uploader">${attachment.uploader}</span>
                    </div>
                </div>
                <div class="attachment-actions">
                    <button class="btn-icon" onclick="CompleteTicketComponent.downloadAttachment('${attachment.id}')" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取附件图标
     */
    getAttachmentIcon(type) {
        const iconMap = {
            document: 'fas fa-file-pdf',
            image: 'fas fa-image',
            audio: 'fas fa-file-audio',
            video: 'fas fa-file-video'
        };
        return iconMap[type] || 'fas fa-file';
    }

    /**
     * 更新办结按钮状态
     */
    updateCompleteButton() {
        const button = document.getElementById('completeTicketBtn');
        if (!button) return;

        if (this.completionData.isCompleted) {
            button.style.display = 'none';
        } else {
            button.style.display = 'inline-flex';
        }
    }

    /**
     * 显示办结对话框
     */
    static showCompleteDialog() {
        // 这里可以实现办结对话框的显示逻辑
        Utils.showToast('办结功能开发中...', 'info');
    }

    /**
     * 下载附件
     */
    static downloadAttachment(attachmentId) {
        Utils.showToast('下载功能开发中...', 'info');
    }

    /**
     * 渲染附件项
     */
    renderAttachment(attachment) {
        return `
            <div class="attachment-item">
                <div class="attachment-info">
                    <i class="${this.getAttachmentIcon(attachment.type)}"></i>
                    <div class="attachment-details">
                        <span class="attachment-name">${attachment.name}</span>
                        <span class="attachment-meta">${attachment.size} • ${attachment.uploadTime}</span>
                        ${attachment.description ? `<span class="attachment-desc">${attachment.description}</span>` : ''}
                    </div>
                </div>
                <div class="attachment-actions">
                    <button class="btn-attachment-action" onclick="CompleteTicketComponent.previewAttachment('${attachment.id}')" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-attachment-action" onclick="CompleteTicketComponent.downloadAttachment('${attachment.id}')" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取附件图标
     */
    getAttachmentIcon(type) {
        const iconMap = {
            'image': 'fas fa-image',
            'document': 'fas fa-file-pdf',
            'audio': 'fas fa-file-audio',
            'video': 'fas fa-file-video',
            'excel': 'fas fa-file-excel',
            'word': 'fas fa-file-word'
        };
        return iconMap[type] || 'fas fa-file';
    }
}

// 全局实例
window.CompleteTicketComponent = CompleteTicketComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('completeInfo')) {
        new CompleteTicketComponent();
    }
});
