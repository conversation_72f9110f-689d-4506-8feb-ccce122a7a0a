<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单流转 - 12345智慧政务平台</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 页面头部 -->
    <header class="page-header">
        <nav class="breadcrumb">
            <span class="breadcrumb-item active">工单详情</span>
        </nav>
        <div class="header-actions">
            <!-- 测试控制面板 -->
            <div class="test-controls">
                <div class="control-group">
                    <label for="userRoleSelect">用户角色:</label>
                    <select id="userRoleSelect" class="control-select" onchange="switchUserRole(this.value)">
                        <option value="operator">话务员</option>
                        <option value="admin">派单人员</option>
                        <option value="processor">处理人员</option>
                        <option value="reviewer">审核人员</option>
                        <option value="callback">回访员</option>
                        <option value="collaborator">协办方</option>
                        <option value="system_admin">管理员</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="ticketStatusSelect">工单状态:</label>
                    <select id="ticketStatusSelect" class="control-select" onchange="switchTicketStatus(this.value)">
                        <option value="draft">草稿/暂存</option>
                        <option value="pending">待接收</option>
                        <option value="processing">处理中</option>
                        <option value="reviewing">待审核</option>
                        <option value="suspended">挂起</option>
                        <option value="callback">待回访</option>
                        <option value="closed">已关闭</option>
                        <option value="cancelled">已废除</option>
                    </select>
                </div>

            </div>
        </div>
    </header>

    <!-- 主容器 -->
    <main class="main-container">
        <!-- 顶部操作栏 -->
        <section class="top-action-bar">
            <!-- 工单状态指示器 -->
            <div class="status-indicators">
                <div class="status-badge status-processing" id="ticketStatus">
                    <i class="fas fa-clock"></i>
                    <span>处理中</span>
                </div>
                <div class="priority-badge priority-normal" id="priorityLevel">
                    <i class="fas fa-flag"></i>
                    <span>普通</span>
                </div>
                <div class="warning-badge" id="timeoutWarning" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>即将超时</span>
                </div>
                <div class="supervision-badge" id="supervisionMark" style="display: none;">
                    <i class="fas fa-eye"></i>
                    <span>督办</span>
                </div>
            </div>

            <!-- 快速操作按钮组 -->
            <div class="action-buttons" id="actionButtons">
                <!-- 动态生成操作按钮 -->
            </div>
        </section>

        <!-- 主页签导航 -->
        <div class="main-tab-navigation">
            <button class="main-tab-button active" data-main-tab="basic">
                <i class="fas fa-file-alt"></i> 工单详情
            </button>
            <button class="main-tab-button" data-main-tab="notes">
                <i class="fas fa-sticky-note"></i> 补记内容
            </button>
            <button class="main-tab-button" data-main-tab="collaboration">
                <i class="fas fa-users"></i> 协办信息
            </button>
            <button class="main-tab-button" data-main-tab="workflow">
                <i class="fas fa-project-diagram"></i> 流转历史
            </button>
             <button class="main-tab-button" data-main-tab="callback">
                <i class="fas fa-phone-alt"></i> 回访满意度
            </button>
            <button class="main-tab-button" data-main-tab="related">
                <i class="fas fa-link"></i> 相关工单
            </button>
            <button class="main-tab-button" data-main-tab="statistics">
                <i class="fas fa-chart-bar"></i> 统计信息
            </button>
        </div>

        <!-- 主页签内容 -->
        <div class="main-tab-content">
            <!-- 工单详情页签 -->
            <div class="main-tab-panel active" id="main-tab-basic">
                <!-- 内容区域 -->
                <div class="content-layout">
                    <!-- 左侧主内容区 -->
                    <div class="main-content">
                <!-- 工单基础信息 -->
                <section class="info-card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> 工单基础信息</h3>
                        <button class="btn btn-sm btn-outline" onclick="toggleSection('basicInfo')">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                    <div class="card-content" id="basicInfo">
                        <div class="info-grid">
                            <div class="info-item">
                                <label>工单编号</label>
                                <div class="info-value">
                                    <span id="ticketNumber"></span>
                                    <button class="btn-copy" onclick="copyToClipboard('ticketNumber')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="info-item">
                                <label>创建时间</label>
                                <span class="info-value" id="createTime"></span>
                            </div>
                            <div class="info-item">
                                <label>创建人</label>
                                <span class="info-value" id="creator"></span>
                            </div>
                            <div class="info-item">
                                <label>工单来源</label>
                                <span class="info-value" id="ticketSource"></span>
                            </div>
                            <div class="info-item">
                                <label>工单类型</label>
                                <span class="info-value" id="ticketType"></span>
                            </div>
                        </div>

                        <div class="content-section">
                            <div class="content-item">
                                <label>诉求标题</label>
                                <div class="content-value editable" id="ticketTitle" contenteditable="false">
                                </div>
                            </div>
                            <div class="content-item">
                                <label>详细描述</label>
                                <div class="content-value editable" id="ticketDescription" contenteditable="false">
                                </div>
                            </div>
                            <div class="content-item">
                                <label>涉及地点</label>
                                <div class="location-info">
                                    <span class="content-value" id="location"></span>
                                    <button class="btn btn-sm btn-outline" onclick="showMap()">
                                        <i class="fas fa-map-marker-alt"></i> 查看地图
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="tags-section">
                            <div class="tag-group">
                                <label>业务标签</label>
                                <div class="tags" id="businessTags">
                                    <!-- 业务标签将通过JavaScript动态生成 -->
                                </div>
                            </div>
                            <div class="tag-group">
                                <label>自定义标签</label>
                                <div class="tags" id="customTags">
                                    <!-- 自定义标签将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 基础信息附件 -->
                        <div class="basic-attachments" id="basicAttachments" style="display: none;">
                            <div class="attachments-header">
                                <i class="fas fa-paperclip"></i>
                                <span>相关附件</span>
                            </div>
                            <div class="attachments-list" id="basicAttachmentsList">
                                <!-- 附件列表将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </section>



                <!-- 工单办理结果 -->
                <section class="info-card">
                    <div class="card-header">
                        <h3><i class="fas fa-check-circle"></i> 工单办理结果</h3>
                        <div class="header-actions">
                            <button class="btn btn-success btn-sm" onclick="CompleteTicketComponent.showCompleteDialog()" id="completeTicketBtn">
                                <i class="fas fa-check"></i> 办结工单
                            </button>
                        </div>
                    </div>
                    <div class="card-content" id="completeInfo">
                        <!-- 办结信息内容将通过JavaScript动态生成 -->
                    </div>
                </section>

                <!-- 附件和证据材料 -->
                <section class="info-card">
                    <div class="card-header">
                        <h3><i class="fas fa-paperclip"></i> 附件和证据材料</h3>
                        <button class="btn btn-primary btn-sm" onclick="uploadAttachment()">
                            <i class="fas fa-upload"></i> 上传附件
                        </button>
                    </div>
                    <div class="card-content" id="attachmentContent">
                        <div class="attachment-grid" id="attachmentGrid">
                            <!-- 附件列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </section>

                
            </div>

            <!-- 右侧信息栏 -->
            <aside class="sidebar">
                <!-- 客户信息 -->
                <section class="info-card">
                    <div class="card-header">
                        <h3><i class="fas fa-user"></i> 客户信息</h3>
                    </div>
                    <div class="card-content" id="customerInfo">
                        <!-- 客户信息内容 -->
                    </div>
                </section>

                <!-- SLA状态 -->
                <section class="info-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> SLA状态</h3>
                    </div>
                    <div class="card-content" id="slaStatus">
                        <!-- SLA状态内容 -->
                    </div>
                </section>



            </aside>
                </div>
            </div>

            <!-- 补记内容页签 -->
            <div class="main-tab-panel" id="main-tab-notes">
                <div class="tab-panel-content">
                    <div class="notes-container">
                        <div class="notes-header">
                            <h2><i class="fas fa-sticky-note"></i> 处理记录与补记</h2>
                            <button class="btn btn-primary" onclick="NotesComponent.showAddDialog()">
                                <i class="fas fa-plus"></i> 添加补记
                            </button>
                        </div>
                        <div id="notesList">
                            <!-- 补记内容将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 相关工单页签 -->
            <div class="main-tab-panel" id="main-tab-related">
                <div class="tab-panel-content">
                    <div id="relatedTickets">
                        <!-- 相关工单内容 -->
                    </div>
                </div>
            </div>

            <!-- 统计信息页签 -->
            <div class="main-tab-panel" id="main-tab-statistics">
                <div class="tab-panel-content">
                    <div id="statisticsInfo">
                        <!-- 统计信息内容 -->
                    </div>
                </div>
            </div>

            <!-- 回访和满意度页签 -->
            <div class="main-tab-panel" id="main-tab-callback">
                <div class="tab-panel-content">
                    <div id="callbackInfo">
                        <!-- 回访信息内容 -->
                    </div>
                </div>
            </div>

            <!-- 协办信息页签 -->
            <div class="main-tab-panel" id="main-tab-collaboration">
                <div class="tab-panel-content">
                    <div id="collaborationInfo">
                        <!-- 协办信息内容 -->
                    </div>
                </div>
            </div>

            <!-- 工单流转路径图页签 -->
            <div class="main-tab-panel" id="main-tab-workflow">
                <div class="tab-panel-content">
                    <div id="workflowChart">
                        <!-- 流转路径图内容 -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框容器 -->
    <div id="modalContainer"></div>

    <!-- 脚本文件 -->
    <script src="data/mock-data.js"></script>
    <script src="script.js"></script>
    <script src="components/notes.js"></script>
    <script src="components/attachments.js"></script>
    <script src="components/customer.js"></script>
    <script src="components/sla.js"></script>
    <script src="components/collaboration.js"></script>
    <script src="components/statistics.js"></script>
    <script src="components/callback.js"></script>
    <script src="components/workflow.js"></script>
    <script src="components/related-tickets.js"></script>
    <script src="components/complete-ticket.js"></script>
    <script src="components/actions.js"></script>

</body>
</html>
