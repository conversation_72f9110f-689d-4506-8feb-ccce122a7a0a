/**
 * 12345政务服务热线系统 - 工单详情页面样式
 * @description 工单详情页面的完整样式定义
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根元素样式 */
:root {
    /* 颜色变量 */
    --primary-color: #007BFF;
    --secondary-color: #6C757D;
    --success-color: #28A745;
    --warning-color: #FFC107;
    --danger-color: #DC3545;
    --info-color: #17A2B8;
    --light-color: #F8F9FA;
    --dark-color: #343A40;
    
    /* 工单状态颜色 */
    --status-draft: #FFA500;
    --status-pending: #FF6B35;
    --status-processing: #007BFF;
    --status-reviewing: #6F42C1;
    --status-suspended: #FFC107;
    --status-callback: #28A745;
    --status-closed: #6C757D;
    --status-cancelled: #DC3545;
    
    /* 间距变量 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 字体变量 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    
    /* 边框半径 */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--dark-color);
    background-color: #f5f5f5;
}

/* 页面头部 */
.page-header {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: var(--spacing-md) var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.breadcrumb-item {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
}

.breadcrumb-item:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--dark-color);
    font-weight: 500;
}

.breadcrumb-separator {
    color: var(--secondary-color);
}

/* 头部操作按钮 */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* 测试控制面板样式 */
.test-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(0, 123, 255, 0.1);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.control-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.control-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    white-space: nowrap;
}

.control-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid #ced4da;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    background: white;
    min-width: 120px;
}

.control-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

.control-group input[type="checkbox"] {
    margin-right: var(--spacing-xs);
}

.action-divider {
    width: 1px;
    height: 30px;
    background-color: #dee2e6;
    margin: 0 var(--spacing-sm);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
}

.btn-outline {
    background-color: transparent;
    color: var(--secondary-color);
    border-color: #dee2e6;
}

.btn-outline:hover {
    background-color: var(--light-color);
    color: var(--dark-color);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

/* 主容器 */
.main-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg) var(--spacing-xl);
}

/* 顶部操作栏 */
.top-action-bar {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* 状态指示器 */
.status-indicators {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.status-badge,
.priority-badge,
.warning-badge,
.supervision-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: white;
}

/* 工单状态样式 */
.status-draft { background-color: var(--status-draft); }
.status-pending { background-color: var(--status-pending); }
.status-processing { background-color: var(--status-processing); }
.status-reviewing { background-color: var(--status-reviewing); }
.status-suspended { background-color: var(--status-suspended); color: var(--dark-color); }
.status-callback { background-color: var(--status-callback); }
.status-closed { background-color: var(--status-closed); }
.status-cancelled { background-color: var(--status-cancelled); }

/* 优先级样式 */
.priority-normal { background-color: var(--secondary-color); }
.priority-urgent { background-color: var(--warning-color); color: var(--dark-color); }
.priority-emergency { background-color: var(--danger-color); }

/* 预警和督办标识 */
.warning-badge {
    background-color: var(--warning-color);
    color: var(--dark-color);
    animation: pulse 2s infinite;
}

.supervision-badge {
    background-color: var(--danger-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* 无可用操作提示 */
.no-actions-tip {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
    font-style: italic;
}

/* 内容布局 */
.content-layout {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: var(--spacing-lg);
}

/* 主内容区 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 侧边栏 */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 信息卡片 */
.info-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.info-card:hover {
    box-shadow: var(--shadow-md);
}

/* 卡片头部 */
.card-header {
    background: var(--light-color);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-header h3 i {
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.info-tip {
    font-size: var(--font-size-xs);
    color: var(--info-color);
    background: rgba(23, 162, 184, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border: 1px solid rgba(23, 162, 184, 0.2);
}

/* 卡片内容 */
.card-content {
    padding: var(--spacing-lg);
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-item label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
}

.info-value {
    font-size: var(--font-size-base);
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* 复制按钮 */
.btn-copy {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.btn-copy:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* 内容区域 */
.content-section {
    margin-bottom: var(--spacing-lg);
}

.content-item {
    margin-bottom: var(--spacing-md);
}

.content-item label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.content-value {
    font-size: var(--font-size-base);
    color: var(--dark-color);
    line-height: 1.6;
    padding: var(--spacing-sm);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    min-height: 2.5rem;
}

.content-value.editable {
    border-color: #e9ecef;
    background-color: var(--light-color);
}

.content-value.editable:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: white;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 地点信息 */
.location-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 标签区域 */
.tags-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.tag-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.tag-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    align-items: center;
}

.tag {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.tag-business {
    background-color: var(--primary-color);
    color: white;
}

.tag-custom {
    background-color: var(--info-color);
    color: white;
}

.btn-add-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: 1px dashed var(--secondary-color);
    border-radius: var(--border-radius-md);
    background: none;
    color: var(--secondary-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-add-tag:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-layout {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .main-container {
        padding: var(--spacing-md);
    }

    .page-header {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .header-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .test-controls {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }

    .control-group {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xs);
    }

    .control-select {
        min-width: auto;
        width: 100%;
    }

    .action-divider {
        width: 100%;
        height: 1px;
        margin: var(--spacing-sm) 0;
    }

    .top-action-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .status-indicators {
        justify-content: center;
    }

    .action-buttons {
        justify-content: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .card-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .card-header h3 {
        font-size: var(--font-size-base);
    }

    .tags {
        justify-content: center;
    }
}

/* 时间轴组件样式 */
.timeline-wrapper {
    position: relative;
}

.timeline-event {
    display: flex;
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.timeline-event:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 60px;
    bottom: -24px;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.timeline-content {
    flex: 1;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.timeline-header {
    background: var(--light-color);
    padding: var(--spacing-md);
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timeline-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.event-type {
    font-weight: 600;
    color: var(--primary-color);
}

.event-time {
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.timeline-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-timeline-action {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.btn-timeline-action:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.timeline-body {
    padding: var(--spacing-md);
}

.event-content {
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin-bottom: var(--spacing-sm);
}

.event-actor {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.actor-label {
    font-weight: 500;
}

.actor-name {
    color: var(--dark-color);
    font-weight: 500;
}

.actor-department {
    color: var(--secondary-color);
}

.timeline-details {
    padding: var(--spacing-md);
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.details-content {
    margin-bottom: var(--spacing-md);
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-sm);
}

.details-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.details-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
}

.details-value {
    font-size: var(--font-size-base);
    color: var(--dark-color);
}

.btn-audio {
    background: var(--success-color);
    color: white;
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-audio:hover {
    background-color: #218838;
}

.event-attachments {
    margin-top: var(--spacing-md);
}

.attachments-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 500;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.attachments-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}



.btn-attachment-action {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.btn-attachment-action:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* 补记内容组件样式 */
.notes-empty {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--secondary-color);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-text {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-md);
}

.note-item {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.note-item:hover {
    box-shadow: var(--shadow-md);
}

.note-header {
    background: var(--light-color);
    padding: var(--spacing-md);
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.note-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.note-type {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 500;
    color: var(--primary-color);
}

.note-private {
    background: var(--warning-color);
    color: var(--dark-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    margin-left: var(--spacing-sm);
}

.note-time {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.note-author {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.author-name {
    font-weight: 500;
    color: var(--dark-color);
}

.author-department {
    color: var(--secondary-color);
}

.note-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-note-action {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.btn-note-action:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.note-content {
    padding: var(--spacing-md);
}

.note-text {
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--dark-color);
    margin-bottom: var(--spacing-md);
}

.note-attachments {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid #e9ecef;
}

.note-details {
    padding: var(--spacing-md);
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.note-details-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    gap: var(--spacing-xs);
}

.detail-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
    white-space: nowrap;
}

.detail-value {
    font-size: var(--font-size-sm);
    color: var(--dark-color);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-lg {
    max-width: 800px;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    color: var(--dark-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--secondary-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--light-color);
    color: var(--dark-color);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid #ced4da;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-text {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    margin-top: var(--spacing-xs);
}

/* 客户信息组件样式 */
.customer-basic-info,
.customer-history,
.customer-common-issues,
.customer-notes {
    margin-bottom: var(--spacing-lg);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.section-header h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
}

.section-header h4 i {
    color: var(--primary-color);
}

.vip-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: var(--dark-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: var(--font-size-sm);
    text-decoration: underline;
    transition: color 0.2s ease;
}

.btn-link:hover {
    color: #0056b3;
}

.customer-info-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
    min-width: 80px;
}

.info-value {
    font-size: var(--font-size-base);
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.btn-action {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.btn-action:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

.history-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    border: 1px solid #e9ecef;
}

.stat-primary { border-left: 4px solid var(--primary-color); }
.stat-success { border-left: 4px solid var(--success-color); }
.stat-warning { border-left: 4px solid var(--warning-color); }

.stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-sm);
}

.stat-primary .stat-icon { background-color: var(--primary-color); }
.stat-success .stat-icon { background-color: var(--success-color); }
.stat-warning .stat-icon { background-color: var(--warning-color); }

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.satisfaction-rating {
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    text-align: center;
}

.rating-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.rating-value {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
}

.rating-score {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--success-color);
}

.rating-stars {
    display: flex;
    gap: 2px;
    color: #ffc107;
}

.common-issues-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.issue-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--info-color);
    color: white;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
}

.notes-content {
    background: var(--light-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid #e9ecef;
}

.notes-text {
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--dark-color);
}

/* 历史记录模态框样式 */
.history-timeline {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.history-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
}

.history-date {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
    min-width: 80px;
}

.history-content {
    flex: 1;
}

.history-title {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.history-status {
    font-size: var(--font-size-sm);
    color: var(--success-color);
    margin-bottom: var(--spacing-xs);
}

.history-satisfaction {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

/* SLA状态组件样式 */
.sla-overall,
.sla-current-stage,
.sla-stages,
.sla-extensions,
.sla-suspensions {
    margin-bottom: var(--spacing-lg);
}

.sla-header,
.stage-header,
.stages-header,
.extensions-header,
.suspensions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.sla-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.sla-status.normal {
    background-color: var(--success-color);
    color: white;
}

.sla-status.warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.sla-status.overdue {
    background-color: var(--danger-color);
    color: white;
}

.sla-status.completed {
    background-color: var(--success-color);
    color: white;
}

.sla-progress,
.stage-progress {
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-xs);
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-fill.warning {
    background-color: var(--warning-color);
}

.progress-fill.overdue {
    background-color: var(--danger-color);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.sla-countdown {
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    text-align: center;
}

.countdown-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    margin-right: var(--spacing-sm);
}

.countdown-time {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
}

.countdown-overdue {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--danger-color);
}

.countdown-completed {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--success-color);
}

.stage-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stages-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stage-item {
    display: flex;
    gap: var(--spacing-md);
    position: relative;
}

.stage-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.stage-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.stage-item.stage-completed .stage-number {
    background-color: var(--success-color);
}

.stage-item.stage-current .stage-number {
    background-color: var(--primary-color);
}

.stage-line {
    width: 2px;
    height: 40px;
    background-color: #e9ecef;
    margin-top: var(--spacing-xs);
}

.stage-item:last-child .stage-line {
    display: none;
}

.stage-item.stage-completed .stage-line {
    background-color: var(--success-color);
}

.stage-content {
    flex: 1;
    padding-top: var(--spacing-xs);
}

.stage-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.stage-name {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--dark-color);
}

.stage-status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.stage-status-badge.completed {
    background-color: var(--success-color);
    color: white;
}

.stage-status-badge.current {
    background-color: var(--primary-color);
    color: white;
}

.stage-status-badge.pending {
    background-color: var(--secondary-color);
    color: white;
}

.stage-info {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.stage-time {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.extensions-list,
.suspensions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.extension-item {
    padding: var(--spacing-md);
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    background: white;
}

.extension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.extension-date {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.extension-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.extension-status.success {
    background-color: var(--success-color);
    color: white;
}

.extension-status.danger {
    background-color: var(--danger-color);
    color: white;
}

.extension-status.warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.extension-content {
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.extension-reason {
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.extension-details {
    color: var(--secondary-color);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* 附件管理组件样式 */
.attachments-empty {
    text-align: center;
    padding: 60px 20px;
    color: var(--secondary-color);
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    margin: 20px 0;
}

.attachments-empty .empty-icon {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 16px;
}

.attachments-empty .empty-text {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 20px;
    font-weight: 500;
}

.attachment-category {
    margin-bottom: var(--spacing-lg);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
}

.category-count {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    margin-left: var(--spacing-sm);
}

.category-content {
    padding: var(--spacing-sm);
}

.attachment-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md);
}

/* 音频附件使用更宽的布局 */
.attachment-item[data-type="audio"] {
    grid-column: span 2;
    min-width: 100%;
}

@media (max-width: 768px) {
    .attachment-item[data-type="audio"] {
        grid-column: span 1;
    }
}

.attachment-item {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    background: white;
    transition: box-shadow 0.2s ease;
}

.attachment-item:hover {
    box-shadow: var(--shadow-md);
}

.attachment-preview {
    height: 120px;
    background: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

/* 音频附件预览区域更高 */
.attachment-item[data-type="audio"] .attachment-preview {
    height: 160px;
    cursor: default;
}

.attachment-preview:hover {
    background-color: #e9ecef;
}

.attachment-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

.audio-player {
    width: 100%;
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.audio-player audio {
    width: 100%;
    max-width: 400px;
    height: 45px;
    border-radius: var(--border-radius-sm);
}

.audio-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.audio-info i {
    color: var(--primary-color);
}

.audio-player audio::-webkit-media-controls-panel {
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
}

.audio-player audio::-webkit-media-controls-play-button,
.audio-player audio::-webkit-media-controls-pause-button {
    background-color: var(--primary-color);
    border-radius: 50%;
}

.audio-player audio::-webkit-media-controls-timeline {
    background-color: var(--border-color);
    border-radius: 25px;
    margin-left: 10px;
    margin-right: 10px;
}

/* ==================== 办结信息记录样式 ==================== */
.completion-info {
    padding: var(--spacing-md);
}

.completion-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.status-badge.completed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.completion-time {
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.completion-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.detail-item label {
    font-weight: 500;
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.detail-value {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.staff-name {
    font-weight: 500;
    color: var(--text-color);
}

.staff-info {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.completion-method {
    padding: 4px 8px;
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    display: inline-block;
}

.quality-score {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.star-rating {
    display: flex;
    gap: 2px;
}

.star-filled {
    color: #ffc107;
}

.star-empty {
    color: #e9ecef;
}

.score-text {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    margin-left: var(--spacing-xs);
}

.completion-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.content-section label {
    font-weight: 500;
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.content-text {
    padding: var(--spacing-sm);
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
    line-height: 1.6;
    color: var(--text-color);
}

.completion-attachments {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.completion-attachments label {
    font-weight: 500;
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.attachment-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.attachment-item-small {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.attachment-item-small .attachment-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: var(--border-radius-sm);
    color: var(--primary-color);
}

.attachment-item-small .attachment-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.attachment-item-small .attachment-name {
    font-weight: 500;
    color: var(--text-color);
    font-size: var(--font-size-sm);
}

.attachment-item-small .attachment-meta {
    display: flex;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
}

.attachment-item-small .attachment-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-icon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    color: var(--secondary-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pending-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background-color: #f8f9fa;
    border-radius: var(--border-radius-md);
    color: var(--secondary-color);
}

.pending-message i {
    color: var(--primary-color);
    font-size: 1.2em;
}

.pending-message p {
    margin: 0;
    line-height: 1.5;
}

/* 附件提示样式 */
.attachment-hint {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: var(--border-radius-sm);
    color: #1976d2;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.attachment-hint i {
    color: #1976d2;
}

.attachment-icon {
    font-size: 2rem;
    color: var(--secondary-color);
}

.attachment-info {
    padding: var(--spacing-md);
}

.attachment-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.attachment-meta {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.attachment-uploader {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.attachment-description {
    font-size: var(--font-size-xs);
    color: var(--dark-color);
    line-height: 1.4;
    margin-bottom: var(--spacing-xs);
}

.attachment-private {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--warning-color);
}

.attachment-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border-top: 1px solid #e9ecef;
    background: var(--light-color);
    flex-wrap: wrap;
}

/* 上传对话框样式 */
.upload-area {
    border: 2px dashed #ced4da;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: var(--spacing-md);
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.05);
}

.upload-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
}

.upload-text {
    color: var(--secondary-color);
}

.upload-link {
    color: var(--primary-color);
    cursor: pointer;
    text-decoration: underline;
}

.upload-hint {
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.selected-files {
    margin-top: var(--spacing-md);
}

.selected-file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xs);
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.file-name {
    font-size: var(--font-size-sm);
    color: var(--dark-color);
}

.file-size {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
}

.btn-remove-file {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.btn-remove-file:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

/* 协办信息组件样式 */

.collaboration-structure,
.collaboration-progress,
.collaboration-list,
.collaboration-communications {
    margin-bottom: var(--spacing-lg);
}

.structure-header,
.progress-header,
.list-header,
.communications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.main-department {
    margin-bottom: var(--spacing-md);
}

.department-card {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    background: white;
}

.department-card.main {
    border-left: 4px solid var(--primary-color);
}

.department-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.department-role {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.department-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-processing {
    background-color: var(--primary-color);
    color: white;
}

.status-completed {
    background-color: var(--success-color);
    color: white;
}

.status-pending {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.department-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.department-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-color);
}

.department-contact {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.department-tasks {
    margin-top: var(--spacing-sm);
}

.tasks-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.tasks-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tasks-list li {
    font-size: var(--font-size-sm);
    color: var(--dark-color);
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: var(--spacing-md);
}

.tasks-list li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.progress-percentage {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.collaborators-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.collaborator-item {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    background: white;
}

.collaborator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.collaborator-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.collaborator-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-color);
}

.collaborator-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.collaborator-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.collaborator-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.collaboration-result {
    background: var(--light-color);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-style: italic;
}

.communications-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.communication-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    background: white;
}

.communication-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.communication-content {
    flex: 1;
}

.communication-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.communication-type {
    font-weight: 500;
    color: var(--primary-color);
}

.communication-time {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.communication-parties {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

.communication-text {
    font-size: var(--font-size-base);
    color: var(--dark-color);
    line-height: 1.5;
}

/* 统计信息组件样式 - 4个区域水平排列 */
.statistics-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.statistics-efficiency,
.statistics-performance,
.statistics-related,
.statistics-quality {
    margin-bottom: 0;
    padding: var(--spacing-md);
    background: var(--color-white);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

/* 统计区域标题紧凑化 */
.statistics-efficiency h4,
.statistics-performance h4,
.statistics-related h4,
.statistics-quality h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border);
}

/* 统计内容区域网格布局 */
.statistics-efficiency > div:not(h4),
.statistics-performance > div:not(h4),
.statistics-related > div:not(h4),
.statistics-quality > div:not(h4) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

/* 统计卡片样式 */
.stat-overview,
.efficiency-stages,
.efficiency-comparison,
.performance-breakdown,
.performance-details,
.similar-tickets,
.department-stats,
.customer-stats,
.quality-rating,
.quality-dimensions,
.quality-suggestions {
    background: var(--color-background);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border-left: 3px solid var(--color-primary);
}

/* 统计卡片标题 */
.stat-overview h5,
.efficiency-stages h5,
.efficiency-comparison h5,
.performance-breakdown h5,
.performance-details h5,
.similar-tickets h5,
.department-stats h5,
.customer-stats h5,
.quality-rating h5,
.quality-dimensions h5,
.quality-suggestions h5 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--color-text-secondary);
}

/* 主要数值显示 */
.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-sm);
}

/* 统计列表项 */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--color-border-light);
    font-size: 0.85rem;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item .label {
    color: var(--color-text-secondary);
}

.stat-item .value {
    font-weight: 600;
    color: var(--color-text);
}

.efficiency-overview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.efficiency-card {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
}

.card-value {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.card-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.efficiency-stages,
.efficiency-comparison {
    margin-bottom: var(--spacing-md);
}

.stages-header,
.comparison-header {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.stage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-xs);
}

.stage-progress {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    text-align: right;
}

.stage-times {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.stage-efficiency {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.comparison-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.comparison-item {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
}

.comparison-label {
    color: var(--secondary-color);
}

.comparison-value {
    color: var(--dark-color);
    font-weight: 500;
}

.efficiency-excellent {
    color: var(--success-color);
}

.efficiency-good {
    color: var(--info-color);
}

.efficiency-average {
    color: var(--warning-color);
}

.efficiency-poor {
    color: var(--danger-color);
}

.performance-overview {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.performance-score {
    display: flex;
    align-items: center;
    justify-content: center;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.score-value {
    font-size: 2rem;
    font-weight: 600;
}

.score-label {
    font-size: var(--font-size-sm);
}

.performance-breakdown {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
}

.breakdown-label {
    color: var(--secondary-color);
}

.breakdown-value {
    color: var(--dark-color);
    font-weight: 500;
}

.performance-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.performance-trend.up {
    color: var(--success-color);
}

.performance-trend.down {
    color: var(--danger-color);
}

.trend-value {
    font-weight: 600;
}

.performance-factors {
    margin-bottom: var(--spacing-md);
}

.factors-header {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.factors-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.factor-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    border: 1px solid #e9ecef;
}

.factor-item.positive {
    border-left: 4px solid var(--success-color);
}

.factor-item.bonus {
    border-left: 4px solid var(--warning-color);
}

.factor-impact {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--success-color);
    min-width: 40px;
}

.factor-content {
    flex: 1;
}

.factor-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.factor-description {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    line-height: 1.4;
}

/* 相关统计样式 */
.related-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.statistics-stat-group {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.statistics-stat-group-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.statistics-stat-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.statistics-stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    transition: transform 0.2s ease;
}

.statistics-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.statistics-stat-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.statistics-stat-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 500;
}

/* 质量评估样式 */
.quality-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.rating-value {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--warning-color);
}

.rating-stars {
    display: flex;
    gap: 2px;
    color: var(--warning-color);
}

.aspects-header {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-md);
}

.aspects-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.aspect-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
}

.aspect-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.aspect-name {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--dark-color);
}

.aspect-weight {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.aspect-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 120px;
}

.aspect-rating .rating-value {
    font-size: var(--font-size-lg);
    min-width: 30px;
    text-align: right;
}

.rating-bar {
    width: 60px;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.rating-fill {
    height: 100%;
    background-color: var(--warning-color);
    transition: width 0.3s ease;
}

.improvements-header {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.improvements-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.improvement-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 193, 7, 0.1);
    border-left: 4px solid var(--warning-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.improvement-item i {
    color: var(--warning-color);
    margin-top: 2px;
    flex-shrink: 0;
}

/* 无操作提示样式 */
.no-actions-message {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--secondary-color);
    font-style: italic;
}

/* Toast 提示样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast-success {
    background-color: var(--success-color);
}

.toast-error {
    background-color: var(--danger-color);
}

.toast-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.toast-info {
    background-color: var(--info-color);
}

/* 演示场景选择器样式 */
.scenario-selector {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.scenario-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(40, 167, 69, 0.05);
    cursor: pointer;
}

.scenario-header h4 {
    margin: 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--success-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.scenario-content {
    padding: var(--spacing-md);
}

.scenario-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.scenario-select {
    flex: 1;
    min-width: 200px;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid #ced4da;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    background: white;
}

.scenario-info {
    background: white;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid #e9ecef;
}

.scenario-desc-text {
    font-size: var(--font-size-base);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.scenario-config {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.config-item {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    background: var(--light-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.actions-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.actions-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.action-tag {
    font-size: var(--font-size-xs);
    background: var(--success-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
}

/* 演示场景响应式样式 */
@media (max-width: 768px) {
    .scenario-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .scenario-select {
        min-width: auto;
        width: 100%;
    }

    .scenario-config {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .actions-list {
        justify-content: center;
    }

    /* 统计信息响应式样式 */
    .statistics-stat-items {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: var(--spacing-sm);
    }

    .statistics-stat-item {
        padding: var(--spacing-sm);
    }

    .statistics-stat-value {
        font-size: var(--font-size-lg);
    }

    .aspect-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .aspect-rating {
        justify-content: space-between;
        min-width: auto;
    }

    .rating-bar {
        flex: 1;
        margin: 0 var(--spacing-sm);
    }

    .improvement-item {
        font-size: var(--font-size-xs);
    }
}

/* 回访和满意度组件样式 */
.callback-not-required {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-xl);
    color: var(--secondary-color);
}

.not-required-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.not-required-text {
    font-size: var(--font-size-base);
}

/* 回访计划样式 */
.callback-plan {
    margin-bottom: var(--spacing-lg);
}

.callback-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.callback-status.completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.callback-status.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.plan-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.plan-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.plan-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.plan-item.full-width {
    grid-column: 1 / -1;
}

.plan-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 500;
}

.plan-value {
    font-size: var(--font-size-base);
    color: var(--dark-color);
}

.plan-value.priority-high {
    color: var(--danger-color);
    font-weight: 600;
}

.plan-value.priority-normal {
    color: var(--primary-color);
}

.plan-value.priority-low {
    color: var(--secondary-color);
}

.plan-actions {
    display: flex;
    gap: var(--spacing-sm);
    padding-top: var(--spacing-md);
    border-top: 1px solid #e9ecef;
}

/* 回访记录样式 */
.callback-records {
    margin-bottom: var(--spacing-lg);
}

.records-count {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    background: var(--light-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.record-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.record-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: center;
}

.record-date {
    font-weight: 600;
    color: var(--dark-color);
}

.record-type {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
}

.record-duration {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.record-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.record-status.completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.record-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.record-basic {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
}

.record-staff {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--dark-color);
}

.record-result,
.record-response {
    font-size: var(--font-size-sm);
}

.result-label,
.response-label {
    color: var(--secondary-color);
}

.result-value.successful,
.response-value.satisfied {
    color: var(--success-color);
    font-weight: 500;
}

.result-value.failed,
.response-value.dissatisfied {
    color: var(--danger-color);
    font-weight: 500;
}

.record-notes {
    padding: var(--spacing-sm);
    background: var(--light-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

.notes-label {
    color: var(--secondary-color);
    font-weight: 500;
}

.notes-content {
    color: var(--dark-color);
    line-height: 1.5;
}

/* 满意度评价样式 */
.satisfaction-section {
    padding: var(--spacing-md);
    background: rgba(255, 193, 7, 0.05);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: var(--border-radius-md);
}

.satisfaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.satisfaction-header h5 {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin: 0;
    color: var(--warning-color);
}

.overall-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.rating-score {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--warning-color);
}

.rating-stars {
    display: flex;
    gap: 2px;
    color: var(--warning-color);
}

.satisfaction-aspects {
    margin-bottom: var(--spacing-md);
}

.aspects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.aspect-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.aspect-name {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 500;
}

.aspect-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.aspect-score {
    font-weight: 600;
    color: var(--warning-color);
    min-width: 20px;
}

.aspect-stars {
    display: flex;
    gap: 1px;
    color: var(--warning-color);
    font-size: var(--font-size-sm);
}

.satisfaction-feedback,
.satisfaction-suggestions {
    margin-top: var(--spacing-md);
}

.feedback-label,
.suggestions-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.feedback-content,
.suggestions-content {
    padding: var(--spacing-sm);
    background: white;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--dark-color);
}

/* 回访统计样式 */
.callback-statistics {
    margin-bottom: var(--spacing-lg);
}

.statistics-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
}

.stat-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-lg);
}

.stat-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stat-value {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--dark-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.satisfaction-distribution h5 {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--dark-color);
}

.distribution-chart {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.distribution-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.distribution-label {
    min-width: 80px;
    font-size: var(--font-size-sm);
    color: var(--dark-color);
}

.distribution-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.distribution-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.distribution-fill.very-satisfied {
    background-color: var(--success-color);
}

.distribution-fill.satisfied {
    background-color: #28a745;
}

.distribution-fill.neutral {
    background-color: var(--warning-color);
}

.distribution-fill.dissatisfied {
    background-color: #fd7e14;
}

.distribution-fill.very-dissatisfied {
    background-color: var(--danger-color);
}

.distribution-count {
    min-width: 30px;
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
}

/* 改进建议样式 */
.callback-improvements {
    margin-bottom: var(--spacing-lg);
}

.improvements-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.improvement-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.improvement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.improvement-category {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 500;
    color: var(--primary-color);
}

.improvement-priority {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.improvement-priority.priority-high {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.improvement-priority.priority-medium {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.improvement-priority.priority-low {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

.improvement-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.improvement-status.status-noted {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

.improvement-status.status-approved {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.improvement-status.status-implemented {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.improvement-content {
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--dark-color);
}

/* 回访表单样式 */
.satisfaction-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.overall-satisfaction {
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
}

.overall-satisfaction label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--dark-color);
}

.rating-input {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.rating-input input[type="range"] {
    flex: 1;
    height: 6px;
    background: #ddd;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.rating-input input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: var(--warning-color);
    border-radius: 50%;
    cursor: pointer;
}

.rating-input input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--warning-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

#overallRatingValue {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--warning-color);
    min-width: 30px;
    text-align: center;
}

.aspect-ratings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.aspect-rating-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.aspect-rating-item label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
}

.aspect-select {
    padding: var(--spacing-sm);
    border: 1px solid #ddd;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
}

/* 回访组件响应式样式 */
@media (max-width: 768px) {
    .plan-grid {
        grid-template-columns: 1fr;
    }

    .record-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .record-basic {
        grid-template-columns: 1fr;
    }

    .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .aspects-grid {
        grid-template-columns: 1fr;
    }

    .aspect-ratings {
        grid-template-columns: 1fr;
    }

    .improvement-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .distribution-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xs);
    }

    .distribution-label {
        min-width: auto;
    }
}

/* 工单流转路径图组件样式 */
.workflow-overview {
    margin-bottom: var(--spacing-lg);
}

.workflow-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.workflow-status.on-schedule {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.workflow-status.delayed {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.overview-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.progress-overview {
    margin-bottom: var(--spacing-md);
}

.progress-bar-container {
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    margin-top: var(--spacing-xs);
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-md);
}

.progress-stats .stat-item {
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
}

.progress-stats .stat-value {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.progress-stats .stat-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.risk-assessment {
    padding-top: var(--spacing-md);
    border-top: 1px solid #e9ecef;
}

.risk-level {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-weight: 500;
}

.risk-level.risk-low {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.risk-level.risk-medium {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.risk-level.risk-high {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* 流转路径样式 */
.workflow-path {
    margin-bottom: var(--spacing-lg);
}

.path-legend {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background: var(--light-color);
}

.legend-item.completed {
    color: var(--success-color);
}

.legend-item.current {
    color: var(--primary-color);
}

.legend-item.pending {
    color: var(--secondary-color);
}

.path-container {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.workflow-step {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.workflow-step.step-completed {
    background: rgba(40, 167, 69, 0.05);
    border-left: 4px solid var(--success-color);
}

.workflow-step.step-current {
    background: rgba(0, 123, 255, 0.05);
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.workflow-step.step-pending {
    background: rgba(108, 117, 125, 0.05);
    border-left: 4px solid var(--secondary-color);
}

.step-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    min-width: 60px;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: white;
}

.step-completed .step-icon {
    background-color: var(--success-color);
}

.step-current .step-icon {
    background-color: var(--primary-color);
    animation: pulse 2s infinite;
}

.step-pending .step-icon {
    background-color: var(--secondary-color);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

.step-number {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--dark-color);
}

.step-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.step-name {
    margin: 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
}

.step-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: center;
}

.step-department {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.step-actor {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.step-description {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    line-height: 1.5;
}

.step-timing {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.timing-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--dark-color);
}

.timing-item.sla-limit {
    color: var(--warning-color);
    font-weight: 500;
}

.step-conditions {
    background: var(--light-color);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.conditions-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.conditions-list {
    margin: 0;
    padding-left: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.step-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.workflow-connector {
    display: flex;
    justify-content: center;
    padding: var(--spacing-xs) 0;
}

.connector-line {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2px;
    height: 30px;
    position: relative;
}

.connector-line.active {
    background-color: var(--success-color);
}

.connector-line.inactive {
    background-color: var(--secondary-color);
}

.connector-arrow {
    position: absolute;
    bottom: -5px;
    color: inherit;
}

/* 里程碑样式 */
.workflow-milestones {
    margin-bottom: var(--spacing-lg);
}

.milestones-list {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.milestone-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.milestone-item.achieved {
    background: rgba(40, 167, 69, 0.05);
    border-left: 4px solid var(--success-color);
}

.milestone-item.pending {
    background: rgba(108, 117, 125, 0.05);
    border-left: 4px solid var(--secondary-color);
}

.milestone-item.importance-critical {
    border-left-color: var(--danger-color);
}

.milestone-item.importance-high {
    border-left-color: var(--warning-color);
}

.milestone-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: white;
}

.milestone-item.achieved .milestone-icon {
    background-color: var(--success-color);
}

.milestone-item.pending .milestone-icon {
    background-color: var(--secondary-color);
}

.milestone-item.importance-critical .milestone-icon {
    background-color: var(--danger-color);
}

.milestone-item.importance-high .milestone-icon {
    background-color: var(--warning-color);
}

.milestone-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.milestone-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
}

.milestone-time {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.milestone-importance {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.milestone-importance.importance-critical {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.milestone-importance.importance-high {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.milestone-importance.importance-low {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

/* 替代路径样式 */
.workflow-alternatives {
    margin-bottom: var(--spacing-lg);
}

.alternatives-list {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.alternative-path {
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--info-color);
}

.path-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.path-name {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--dark-color);
}

.path-type {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.path-type.available {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.path-type.unavailable {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

.path-description {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
}

.path-route {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.route-from,
.route-to {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: white;
    border-radius: var(--border-radius-sm);
    color: var(--primary-color);
}

.path-conditions {
    background: white;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.path-conditions .conditions-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.path-conditions .conditions-list {
    margin: 0;
    padding-left: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

/* 工单流转路径图响应式样式 */
@media (max-width: 768px) {
    .progress-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .path-legend {
        justify-content: center;
    }

    .workflow-step {
        flex-direction: column;
        align-items: flex-start;
    }

    .step-node {
        flex-direction: row;
        min-width: auto;
        align-self: stretch;
        justify-content: flex-start;
    }

    .step-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .step-timing {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .milestone-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .milestone-item .milestone-icon {
        align-self: center;
    }

    .path-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .path-route {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* 融合流转历史样式 */
.workflow-title-section {
    margin-bottom: var(--spacing-lg);
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.title-header h2 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.title-description {
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.view-controls {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--light-color);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-md);
}

.view-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-toggle:hover {
    color: var(--primary-color);
    background: white;
}

.view-toggle.active {
    color: var(--primary-color);
    background: white;
    box-shadow: var(--shadow-sm);
    font-weight: 500;
}

.fused-workflow-section {
    margin-bottom: var(--spacing-lg);
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.fused-workflow-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid #e9ecef;
}

.fused-workflow-section .section-header h4 {
    margin: 0;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.workflow-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.fused-step {
    display: flex;
    gap: var(--spacing-lg);
    position: relative;
}

.step-timeline {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
}

.fused-step .step-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    z-index: 2;
}

.fused-step .step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: white;
    box-shadow: var(--shadow-sm);
}

.fused-step.completed .step-icon {
    background-color: var(--success-color);
}

.fused-step.current .step-icon {
    background-color: var(--primary-color);
    animation: pulse 2s infinite;
}

.fused-step.pending .step-icon {
    background-color: var(--secondary-color);
}

.fused-step .step-number {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--dark-color);
    background: white;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
}

.step-connector {
    width: 3px;
    height: 60px;
    margin-top: var(--spacing-sm);
}

.step-connector.active {
    background: linear-gradient(to bottom, var(--success-color), var(--primary-color));
}

.step-connector.inactive {
    background: var(--secondary-color);
    opacity: 0.3;
}

.step-content-area {
    flex: 1;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.fused-step.current .step-content-area {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.step-main-info {
    margin-bottom: var(--spacing-lg);
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.step-name {
    margin: 0;
    color: var(--dark-color);
    font-size: var(--font-size-lg);
}

.step-status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.step-status-badge.completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.step-status-badge.current {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.step-status-badge.pending {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

.step-description {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.meta-item label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 500;
}

.meta-item span {
    color: var(--dark-color);
}

.meta-item .sla-limit {
    color: var(--warning-color);
    font-weight: 500;
}

.step-conditions {
    background: var(--light-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.step-conditions label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.step-conditions ul {
    margin: 0;
    padding-left: var(--spacing-md);
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
}

.step-history-section {
    background: rgba(0, 123, 255, 0.02);
    border: 1px solid rgba(0, 123, 255, 0.1);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.history-header h6 {
    margin: 0;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.history-count {
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
    background: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.history-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    background: white;
    border-radius: var(--border-radius-sm);
    border-left: 3px solid var(--primary-color);
}

.history-time {
    min-width: 140px;
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    font-weight: 500;
}

.history-content {
    flex: 1;
}

.history-actor {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.history-action {
    color: var(--dark-color);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.history-details {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    line-height: 1.4;
}

.no-history {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--secondary-color);
    font-style: italic;
}

.no-history i {
    margin-right: var(--spacing-xs);
}

.step-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* 时间轴视图样式 */
.timeline-view {
    padding: var(--spacing-md);
}

.timeline-container {
    position: relative;
    padding-left: var(--spacing-xl);
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--success-color));
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-sm);
    box-shadow: var(--shadow-sm);
}

.timeline-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-sm);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.timeline-time {
    font-weight: 600;
    color: var(--primary-color);
}

.timeline-actor {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.timeline-action {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-xs);
}

.timeline-details {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    line-height: 1.4;
    margin-bottom: var(--spacing-xs);
}

.timeline-department {
    font-size: var(--font-size-xs);
    color: var(--info-color);
    background: rgba(23, 162, 184, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    display: inline-block;
}

/* 流程图视图样式 */
.flowchart-view {
    padding: var(--spacing-md);
}

.flowchart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.flowchart-node {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    min-width: 250px;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
}

.flowchart-node.completed {
    border-color: var(--success-color);
    background: rgba(40, 167, 69, 0.05);
}

.flowchart-node.current {
    border-color: var(--primary-color);
    background: rgba(0, 123, 255, 0.05);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.flowchart-node.pending {
    border-color: var(--secondary-color);
    background: rgba(108, 117, 125, 0.05);
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.node-number {
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.node-status {
    font-size: var(--font-size-lg);
}

.flowchart-node.completed .node-status {
    color: var(--success-color);
}

.flowchart-node.current .node-status {
    color: var(--primary-color);
}

.flowchart-node.pending .node-status {
    color: var(--secondary-color);
}

.node-content {
    text-align: center;
}

.node-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.node-department {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.node-timing {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.node-arrow {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

/* 流转历史响应式样式 */
@media (max-width: 768px) {
    .workflow-title-section {
        padding: var(--spacing-md);
    }

    .view-controls {
        flex-direction: column;
    }

    .view-toggle {
        justify-content: center;
    }

    .workflow-step {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .step-timeline {
        flex-direction: row;
        justify-content: center;
        min-width: auto;
    }

    .step-connector {
        width: 60px;
        height: 3px;
        margin-top: 0;
        margin-left: var(--spacing-sm);
    }

    .meta-grid {
        grid-template-columns: 1fr;
    }

    .history-item {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .history-time {
        min-width: auto;
    }

    .step-actions {
        justify-content: stretch;
    }

    .step-actions .btn {
        flex: 1;
        text-align: center;
    }

    .timeline-container {
        padding-left: var(--spacing-lg);
    }

    .timeline-marker {
        left: -20px;
        width: 30px;
        height: 30px;
    }

    .flowchart-node {
        min-width: 200px;
        padding: var(--spacing-md);
    }
}

/* 主页签样式 */
.main-tab-navigation {
    display: flex;
    background: white;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: var(--spacing-lg);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.main-tab-navigation::-webkit-scrollbar {
    display: none;
}

.main-tab-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: none;
    color: var(--secondary-color);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
    position: relative;
    min-height: 60px;
}

.main-tab-button:hover {
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.05);
}

.main-tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
    font-weight: 600;
}

.main-tab-button i {
    font-size: var(--font-size-lg);
}

.main-tab-content {
    flex: 1;
    position: relative;
    min-height: calc(100vh - 200px);
}

.main-tab-panel {
    display: none;
    animation: fadeInUp 0.3s ease-in-out;
}

.main-tab-panel.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-panel-content {
    padding: var(--spacing-lg);
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    min-height: 500px;
}

/* 主页签响应式样式 */
@media (max-width: 768px) {
    .main-tab-navigation {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: var(--spacing-xs);
    }

    .main-tab-button {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        min-width: 100px;
        justify-content: center;
        flex-direction: column;
        gap: var(--spacing-xs);
        min-height: 50px;
    }

    .main-tab-button i {
        font-size: var(--font-size-base);
    }

    .tab-panel-content {
        padding: var(--spacing-md);
        margin: 0 var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .main-tab-button {
        min-width: 80px;
        padding: var(--spacing-sm);
    }

    .main-tab-button span {
        font-size: var(--font-size-xs);
    }
}

/* 相关工单组件样式 */
.related-search-section {
    margin-bottom: var(--spacing-lg);
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.search-header h4 {
    margin: 0;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.search-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.search-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.search-input-group {
    display: flex;
    gap: var(--spacing-sm);
}

.search-input-group .form-control {
    flex: 1;
}

.filter-controls {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filter-controls .form-control {
    min-width: 150px;
}

/* 紧凑搜索布局 */
.search-controls-compact {
    margin-top: var(--spacing-sm);
}

.search-row {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
    flex-wrap: wrap;
}

.search-input-group {
    flex: 1;
    min-width: 300px;
}

.filter-controls-inline {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filter-controls-inline .form-control {
    min-width: 120px;
    width: auto;
}

.related-stats-section {
    margin-bottom: var(--spacing-lg);
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.stats-header {
    margin-bottom: var(--spacing-md);
}

.stats-header h4 {
    margin: 0;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stats-grid .stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--primary-color);
}

.stats-grid .stat-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xl);
}

.stats-grid .stat-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stats-grid .stat-value {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--dark-color);
}

.stats-grid .stat-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.related-tickets-section {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.tickets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.tickets-header h4 {
    margin: 0;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.view-controls {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--light-color);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-md);
}

.view-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover {
    color: var(--primary-color);
    background: white;
}

.view-btn.active {
    color: var(--primary-color);
    background: white;
    box-shadow: var(--shadow-sm);
}

.ticket-group {
    margin-bottom: var(--spacing-lg);
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--light-color);
    border-bottom: 1px solid #e9ecef;
}

.group-header h5 {
    margin: 0;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.group-toggle {
    border: none;
    background: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.group-toggle:hover {
    color: var(--primary-color);
    background: white;
}

.group-content {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.ticket-card {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    transition: all 0.3s ease;
}

.ticket-card:hover {
    box-shadow: var(--shadow-sm);
    border-color: var(--primary-color);
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.ticket-id a {
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
}

.ticket-id a:hover {
    text-decoration: underline;
}

.ticket-badges {
    display: flex;
    gap: var(--spacing-sm);
}

.similarity-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.similarity-badge.similarity-high {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.similarity-badge.similarity-medium {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.similarity-badge.similarity-low {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--secondary-color);
}

.ticket-content {
    margin-bottom: var(--spacing-md);
}

.ticket-title {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.ticket-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.merge-info {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 193, 7, 0.1);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    color: var(--warning-color);
}

.merge-info a {
    color: var(--warning-color);
    font-weight: 500;
}

.ticket-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.tickets-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* 相关工单响应式样式 */
@media (max-width: 768px) {
    .search-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .search-input-group {
        flex-direction: column;
    }

    .filter-controls {
        flex-direction: column;
    }

    .filter-controls .form-control {
        min-width: auto;
    }

    /* 紧凑搜索布局响应式 */
    .search-row {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .search-input-group {
        min-width: auto;
    }

    .filter-controls-inline {
        flex-direction: column;
    }

    .filter-controls-inline .form-control {
        min-width: auto;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .tickets-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .ticket-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .ticket-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .ticket-actions {
        justify-content: stretch;
    }

    .ticket-actions .btn {
        flex: 1;
        text-align: center;
    }

    /* 统计信息响应式样式 */
    .statistics-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .statistics-efficiency > div:not(h4),
    .statistics-performance > div:not(h4),
    .statistics-related > div:not(h4),
    .statistics-quality > div:not(h4) {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .statistics-efficiency,
    .statistics-performance,
    .statistics-related,
    .statistics-quality {
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-sm);
    }

    .statistics-efficiency h4,
    .statistics-performance h4,
    .statistics-related h4,
    .statistics-quality h4 {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-xs);
    }
}

/* 错误处理样式 */
.initialization-error {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--danger-color);
    color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 9999;
    max-width: 500px;
    text-align: center;
}

.error-header {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.error-message {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
}

.error-actions {
    display: flex;
    justify-content: center;
}

.error-message {
    color: var(--danger-color);
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin: var(--spacing-md) 0;
}

/* 调试面板样式 */
.debug-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 400px;
    max-height: 300px;
    background: white;
    border: 1px solid #ccc;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 9999;
    font-family: monospace;
    font-size: var(--font-size-xs);
}

.debug-header {
    background: var(--dark-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-header button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: var(--font-size-lg);
}

.debug-content {
    padding: var(--spacing-sm);
    max-height: 250px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

/* 基础信息附件样式 */
.basic-attachments {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.basic-attachments .attachments-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 500;
    color: #495057;
}

.basic-attachments .attachments-header i {
    color: #6c757d;
}

.basic-attachments .attachments-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.basic-attachments .attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.basic-attachments .attachment-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.basic-attachments .attachment-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.basic-attachments .attachment-info i {
    font-size: 18px;
    color: #6c757d;
}

.basic-attachments .attachment-name {
    font-weight: 500;
    color: #495057;
}

.basic-attachments .attachment-size {
    font-size: 12px;
    color: #6c757d;
    margin-left: auto;
    margin-right: 12px;
}

.basic-attachments .attachment-actions {
    display: flex;
    gap: 4px;
}

.btn-attachment-action {
    padding: 6px 8px;
    border: none;
    background: #6c757d;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

.btn-attachment-action:hover {
    background: #5a6268;
}

.btn-attachment-action:active {
    transform: translateY(1px);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
}

.audio-player-modal {
    width: 500px;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #495057;
}

.modal-close {
    padding: 8px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-body {
    padding: 20px;
}

.audio-info {
    margin-bottom: 20px;
}

.audio-info .info-item {
    display: flex;
    margin-bottom: 8px;
}

.audio-info .label {
    font-weight: 500;
    color: #495057;
    min-width: 80px;
}

.audio-info .value {
    color: #6c757d;
}

.audio-player-container {
    text-align: center;
}

.audio-player {
    width: 100%;
    margin-bottom: 15px;
    outline: none;
}

.audio-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
}

/* 办结附件样式 */
.completion-attachments {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 8px;
}

.completion-attachments .attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.completion-attachments .attachment-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.completion-attachments .attachment-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.completion-attachments .attachment-info i {
    font-size: 18px;
    color: #6c757d;
}

.completion-attachments .attachment-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.completion-attachments .attachment-name {
    font-weight: 500;
    color: #495057;
}

.completion-attachments .attachment-meta {
    font-size: 12px;
    color: #6c757d;
}

.completion-attachments .attachment-desc {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.completion-attachments .attachment-actions {
    display: flex;
    gap: 4px;
}

/* 客户历史工单列表样式 */
.history-tickets {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.history-ticket-item {
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-ticket-item:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.ticket-number {
    font-weight: 600;
    color: #007bff;
    font-size: 13px;
}

.ticket-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-closed {
    background: #d4edda;
    color: #155724;
}

.status-completed {
    background: #d1ecf1;
    color: #0c5460;
}

.status-processing {
    background: #fff3cd;
    color: #856404;
}

.status-pending {
    background: #f8d7da;
    color: #721c24;
}

.ticket-title {
    font-size: 14px;
    color: #495057;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.ticket-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

.ticket-time {
    display: flex;
    align-items: center;
    gap: 4px;
}

.ticket-satisfaction {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #ffc107;
}

.no-history {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-history i {
    font-size: 32px;
    margin-bottom: 12px;
    color: #dee2e6;
}

.no-history p {
    margin: 0;
    font-size: 14px;
}

.more-tickets {
    text-align: center;
    margin-top: 8px;
}

.btn-more {
    background: none;
    border: 1px dashed #007bff;
    color: #007bff;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.btn-more:hover {
    background: #007bff;
    color: white;
}

/* 补记页签样式 */
.notes-container {
    padding: 20px;
}

.notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e9ecef;
}

.notes-header h2 {
    margin: 0;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notes-header h2 i {
    color: #6c757d;
}

#notesList {
    min-height: 400px;
}
