<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12345政务服务热线系统 - 核心角色可视化</title>
    
    <!-- 外部依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>12345政务服务热线系统</h1>
            <p>核心角色可视化展示（最终修订版）</p>
        </div>

        <div class="content">
            <!-- 搜索功能 -->
            <div class="search-container">
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="搜索角色名称、职责或描述..." onkeyup="handleSearch(event)">
                    <button class="search-btn" onclick="performSearch()">🔍</button>
                </div>
            </div>

            <!-- 角色分类导航 -->
            <div class="role-categories">
                <button class="category-btn active" onclick="showCategory('all', this)">全部角色</button>
                <button class="category-btn" onclick="showCategory('external', this)">外部角色</button>
                <button class="category-btn" onclick="showCategory('business', this)">核心业务角色</button>
                <button class="category-btn" onclick="showCategory('support', this)">管理支持角色</button>
            </div>

            <!-- 角色关系图 -->
            <div class="role-diagram-container">
                <h3>角色关系图</h3>
                <div class="diagram-wrapper">
                    <div id="role-diagram"></div>
                </div>
            </div>

            <!-- 角色卡片展示区 -->
            <div class="roles-grid" id="roles-grid">
                <!-- 外部角色 -->
                <div class="role-card external" data-role="customer">
                    <div class="role-header">
                        <div class="role-icon">👥</div>
                        <h3>市民</h3>
                        <span class="role-type">外部角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">服务的发起者与最终评价者</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li>提出诉求</li>
                                <li>配合处理过程</li>
                                <li>在回访环节提供真实的服务反馈和满意度评价</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 核心业务角色 -->
                <div class="role-card business" data-role="operator">
                    <div class="role-header">
                        <div class="role-icon">📞</div>
                        <h3>市级12345中心话务员/分派员</h3>
                        <span class="role-type">核心业务角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">总受理平台与一级分派/办结中心</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li><strong>受理与创建：</strong>作为全市工单的唯一入口，负责接听、创建工单，并进行标准化信息录入</li>
                                <li><strong>即时办结：</strong>对于无需流转的"政策咨询"、"信息查询"等简单诉求，利用知识库直接答复市民</li>
                                <li><strong>一级分派：</strong>对于需要流转的工单，严格按照地域属性，仅对各区/县级总承办单位进行规范化分派</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="role-card business" data-role="hierarchical-dispatcher">
                    <div class="role-header">
                        <div class="role-icon">🏢</div>
                        <h3>各层级工单管理员/分派员</h3>
                        <span class="role-type">核心业务角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">各层级枢纽的"分流阀"与"办结阀"</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li><strong>接收与分派：</strong>作为"总承办单位"（如区12345分中心、街道办事处、区职能部门、业务科室）的核心人员，负责接收上级工单，并根据事权在本级进行二次精准分派</li>
                                <li><strong>即时办结：</strong>在接收工单后，第一时间判断是否需要继续下派。如果本级能够独立解决，则果断即时办结，防止工单进一步下沉</li>
                                <li><strong>多部门协同：</strong>当工单需要多个部门协同处理时，负责指定主办部门和协办部门，统筹协调各部门间的协作</li>
                                <li><strong>协办执行：</strong>当本单位作为协办部门时，积极配合主办部门，按时完成分配的协办任务并及时反馈进度</li>
                            </ul>
                        </div>
                        <div class="role-includes">
                            <strong>分布：</strong>此岗位职能贯穿于区/县级分中心、区级职能部门、街/镇总承办单位、业务科室、社区/村等多个枢纽节点
                        </div>
                    </div>
                </div>

                <div class="role-card business" data-role="functional-department">
                    <div class="role-header">
                        <div class="role-icon">🏛️</div>
                        <h3>区级/街镇职能部门</h3>
                        <span class="role-type">核心业务角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">"条线"上的专业管理与分派平台</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li>接收来自上级"枢纽"的专业性工单，进行"即时办结"判断</li>
                                <li>或分派给下属的业务科室</li>
                            </ul>
                        </div>
                        <div class="role-includes">
                            <strong>包含：</strong>区市场监管局、区住建局、街道城管科等
                        </div>
                    </div>
                </div>

                <div class="role-card business" data-role="business-section">
                    <div class="role-header">
                        <div class="role-icon">📋</div>
                        <h3>业务科室</h3>
                        <span class="role-type">核心业务角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">专业执行的"最后一公里"枢纽</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li><strong>接收任务：</strong>接收来自上级职能部门的工单</li>
                                <li><strong>即时办结：</strong>基于科室的专业信息进行判断，若能解决则直接即时办结</li>
                                <li><strong>精准指派：</strong>将需要现场执行的任务，精准指派给本科室的工作人员</li>
                                <li><strong>审核与反馈：</strong>作为科室工作人员的直接管理者，负责对其提交的"办结"结果进行第一道审核</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="role-card business" data-role="community-committee">
                    <div class="role-header">
                        <div class="role-icon">🏘️</div>
                        <h3>社区/村委会</h3>
                        <span class="role-type">核心业务角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">属地管理的"最后一公里"枢纽与五级分派平台</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li><strong>接收任务：</strong>接收来自"街/镇"下派的、与本辖区居民直接相关的工单</li>
                                <li><strong>即时办结：</strong>如果问题是社区层面通过协调、解释就能解决的，则直接即时办结</li>
                                <li><strong>精准指派：</strong>对于必须由网格员现场处理的任务，负责将工单精准指派给负责该片区的网格员</li>
                                <li><strong>审核与反馈：</strong>作为网格员的直接管理者，负责对其提交的"办结"结果进行第一道审核</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="role-card business" data-role="field-agent">
                    <div class="role-header">
                        <div class="role-icon">🚶</div>
                        <h3>最终执行者</h3>
                        <span class="role-type">核心业务角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">深入一线的"手和脚"，所有任务的最终落点</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li><strong>现场执行：</strong>负责所有需要现场核实、处理、调解和信息上报的"最后一公里"任务</li>
                                <li><strong>办结上报：</strong>在完成现场任务后，点击"办结"，并提交详实的图文处理结果，触发"逐级审核"流程</li>
                            </ul>
                        </div>
                        <div class="role-includes">
                            <strong>包含：</strong>网格员（由社区/村委派）、科室工作人员（由各级业务科室委派）
                        </div>
                    </div>
                </div>



                <div class="role-card business" data-role="revisit-agent">
                    <div class="role-header">
                        <div class="role-icon">📋</div>
                        <h3>回访员</h3>
                        <span class="role-type">核心业务角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">独立的第三方服务质量监督员</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li>在所有内部流程（包括即时办结）结束后，对市民进行满意度回访</li>
                                <li>作为服务质量的最终把关人，并负责关闭或重启工单</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 管理支持角色 -->
                <div class="role-card support" data-role="manager">
                    <div class="role-header">
                        <div class="role-icon">👔</div>
                        <h3>管理者/主管</h3>
                        <span class="role-type">管理支持角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">流程的监督者与审批者</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li><strong>审核：</strong>对下级单位提交的"办结"或"即时办结"结果进行审核，确保处理质量</li>
                                <li><strong>审批：</strong>对下属提交的特殊申请（如延期、挂起）进行裁决</li>
                            </ul>
                        </div>
                        <div class="role-includes">
                            <strong>包含：</strong>各层级、各单位的负责人
                        </div>
                    </div>
                </div>

                <div class="role-card support" data-role="admin">
                    <div class="role-header">
                        <div class="role-icon">⚙️</div>
                        <h3>系统管理员</h3>
                        <span class="role-type">管理支持角色</span>
                    </div>
                    <div class="role-content">
                        <div class="role-position">系统的"总设计师"与维护者</div>
                        <div class="role-responsibilities">
                            <h4>核心职责：</h4>
                            <ul>
                                <li>负责在后台配置整个系统的复杂组织架构</li>
                                <li>设置自动化流转规则（包括对即时办结工单的特殊流转设定）</li>
                                <li>配置SLA策略、工单模板等</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 角色统计 -->
            <div class="role-statistics">
                <h3>角色统计</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">1</div>
                        <div class="stat-label">外部角色</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">7</div>
                        <div class="stat-label">核心业务角色</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">2</div>
                        <div class="stat-label">管理支持角色</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">10</div>
                        <div class="stat-label">总计角色</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 12345政务服务热线系统 - 核心角色可视化展示</p>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/roles-data.js"></script>
    <script src="js/role-diagram.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
