# 12345政务服务热线系统 - 核心角色可视化

## 项目简介

这是一个基于Web的可视化展示系统，用于展示12345政务服务热线系统中的核心角色及其关系。通过交互式的界面设计，帮助用户更好地理解系统中各个角色的职责、关系和工作流程。

## 功能特性

### 🎯 核心功能

1. **角色分类展示**
   - 外部角色：市民
   - 核心业务角色：话务员/分派员、各层级工单管理员/分派员、区级/街镇职能部门、业务科室、社区/村委会、最终执行者、协办人、回访员
   - 管理支持角色：管理者、系统管理员

2. **交互式角色关系图**
   - 层级图：展示角色的层级关系
   - 详细图：展示完整的角色关系和流程
   - 简化图：展示核心角色关系

3. **智能搜索功能**
   - 支持角色名称搜索
   - 支持职责内容搜索
   - 实时搜索结果展示

4. **响应式设计**
   - 支持桌面端、平板端、移动端
   - 自适应布局和交互优化

### 🎨 界面特色

- **现代化设计**：采用卡片式布局，清晰直观
- **颜色编码**：不同角色类别使用不同颜色标识
- **动画效果**：平滑的过渡动画和交互反馈
- **图表可视化**：使用Mermaid生成专业的关系图

## 技术架构

### 前端技术栈

- **HTML5**：语义化标记
- **CSS3**：现代化样式和动画
- **JavaScript (ES6+)**：交互逻辑和数据处理
- **Mermaid.js**：图表生成库

### 文件结构

```
核心角色/
├── index.html              # 主页面
├── css/
│   ├── styles.css          # 主样式文件
│   └── responsive.css      # 响应式样式
├── js/
│   ├── main.js            # 主程序逻辑
│   ├── roles-data.js      # 角色数据定义
│   └── role-diagram.js    # 图表生成器
└── README.md              # 项目说明
```

## 角色详细说明

### 外部角色

**市民 (Customer/Citizen)**
- 职责：服务的发起者与最终评价者
- 核心任务：提出诉求、配合处理、提供反馈

### 核心业务角色

**市级12345中心话务员/分派员 (Operator/Dispatcher)**
- 职责：总受理平台与一级分派/办结中心
- 核心任务：受理创建、即时办结、一级分派

**各层级工单管理员/分派员 (Hierarchical Dispatcher)**
- 职责：各层级枢纽的"分流阀"与"办结阀"
- 核心任务：接收分派、即时办结

**区级/街镇职能部门 (Functional Department)**
- 职责："条线"上的专业管理与分派平台
- 核心任务：专业工单处理、科室分派

**业务科室 (Business Section)**
- 职责：专业执行的"最后一公里"枢纽
- 核心任务：接收任务、即时办结、精准指派、审核反馈

**社区/村委会 (Community/Village Committee)**
- 职责：属地管理的"最后一公里"枢纽与五级分派平台
- 核心任务：接收任务、即时办结、精准指派、审核反馈

**最终执行者 (Field Agent)**
- 职责：深入一线的"手和脚"，所有任务的最终落点
- 核心任务：现场执行、办结上报

**协办人 (Collaborator)**
- 职责：协同作战的"专业友军"
- 核心任务：协办任务、进度反馈

**回访员 (Revisit Agent)**
- 职责：独立的第三方服务质量监督员
- 核心任务：满意度回访、工单关闭

### 管理支持角色

**管理者/主管 (Manager/Supervisor)**
- 职责：流程的监督者与审批者
- 核心任务：审核办结、审批决策

**系统管理员 (System Administrator)**
- 职责：系统的"总设计师"与维护者
- 核心任务：架构配置、规则设置、策略管理

## 使用指南

### 基本操作

1. **浏览角色**
   - 点击分类按钮查看不同类别的角色
   - 滚动浏览所有角色卡片

2. **搜索功能**
   - 在搜索框中输入关键词
   - 支持实时搜索和回车搜索
   - 点击"清除搜索"返回分类视图

3. **查看关系图**
   - 点击图表类型按钮切换不同视图
   - 层级图：查看角色层级关系
   - 详细图：查看完整流程关系
   - 简化图：查看核心关系

### 高级功能

- **响应式适配**：在不同设备上自动调整布局
- **打印支持**：支持打印整个页面内容
- **无障碍访问**：支持键盘导航和屏幕阅读器

## 数据来源

本可视化系统基于《12345政务服务热线系统核心角色》文档，准确反映了系统中各个角色的定义、职责和关系。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现角色分类展示
- 添加交互式关系图
- 支持搜索功能
- 响应式设计优化

## 联系信息

如有问题或建议，请联系系统管理员。

---

© 2024 12345政务服务热线系统 - 核心角色可视化展示
