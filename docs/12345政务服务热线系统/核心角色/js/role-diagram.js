/**
 * 角色关系图生成器
 * 使用Mermaid生成角色关系流程图
 */

/**
 * 生成角色关系图的Mermaid代码
 */
function generateRoleDiagram() {
    const diagram = `
graph TD
    %% 外部角色
    A[👥 市民<br/>Customer]:::external
    
    %% 核心业务角色
    B[📞 市级12345中心<br/>话务员/分派员]:::business
    C[🏢 各层级工单<br/>管理员/分派员]:::business
    D[🏛️ 区级/街镇<br/>职能部门]:::business
    E[📋 业务科室]:::business
    F[🏘️ 社区/村委会]:::business
    G[🚶 最终执行者]:::business
    H[🤝 协办人]:::business
    I[📋 回访员]:::business
    
    %% 管理支持角色
    J[👔 管理者/主管]:::support
    K[⚙️ 系统管理员]:::support
    
    %% 主要流程关系
    A -->|提出诉求| B
    B -->|一级分派| C
    B -->|即时办结| I
    C -->|专业分派| D
    C -->|属地分派| F
    C -->|即时办结| I
    D -->|科室分派| E
    D -->|即时办结| I
    E -->|任务指派| G
    E -->|即时办结| I
    F -->|网格指派| G
    F -->|即时办结| I
    C -->|协办任务| H
    G -->|办结上报| J
    H -->|协办完成| C
    J -->|审核通过| I
    I -->|回访反馈| A
    
    %% 管理关系
    K -.->|系统配置| B
    K -.->|流程配置| C
    K -.->|规则配置| D
    J -.->|监督管理| G
    J -.->|审批决策| E
    
    %% 样式定义
    classDef external fill:#ffebee,stroke:#e74c3c,stroke-width:3px,color:#2c3e50
    classDef business fill:#e3f2fd,stroke:#3498db,stroke-width:3px,color:#2c3e50
    classDef support fill:#fff3e0,stroke:#f39c12,stroke-width:3px,color:#2c3e50
    
    %% 节点样式
    class A external
    class B,C,D,E,F,G,H,I business
    class J,K support
    `;
    
    return diagram;
}

/**
 * 生成简化版角色关系图
 */
function generateSimplifiedRoleDiagram() {
    const diagram = `
graph LR
    %% 角色分类
    subgraph "外部角色"
        A[👥 市民]:::external
    end
    
    subgraph "核心业务角色"
        B[📞 话务员]:::business
        C[🏢 分派员]:::business
        D[🔧 处理人]:::business
        E[🚶 执行者]:::business
        F[🤝 协办人]:::business
        G[📋 回访员]:::business
    end
    
    subgraph "管理支持角色"
        H[👔 管理者]:::support
        I[⚙️ 系统管理员]:::support
    end
    
    %% 流程关系
    A --> B
    B --> C
    C --> D
    D --> E
    D --> F
    E --> H
    H --> G
    G --> A
    
    %% 样式定义
    classDef external fill:#ffebee,stroke:#e74c3c,stroke-width:2px
    classDef business fill:#e3f2fd,stroke:#3498db,stroke-width:2px
    classDef support fill:#fff3e0,stroke:#f39c12,stroke-width:2px
    `;
    
    return diagram;
}

/**
 * 生成角色层级图
 */
function generateRoleHierarchyDiagram() {
    const diagram = `
graph TD
    %% 顶层
    A[👥 市民<br/>服务发起者]:::external
    
    %% 第一层：接入层
    B[📞 市级12345中心<br/>话务员/分派员<br/>总受理平台]:::business
    
    %% 第二层：分派层
    C[🏢 各层级工单管理员<br/>分流阀与办结阀]:::business

    %% 第三层：专业处理层
    D[🏛️ 区级/街镇职能部门<br/>专业管理平台]:::business
    E[📋 业务科室<br/>专业执行枢纽]:::business
    F[🏘️ 社区/村委会<br/>属地管理枢纽]:::business

    %% 第四层：执行层
    G[🚶 最终执行者<br/>一线手和脚]:::business
    H[🤝 协办人<br/>专业友军]:::business
    
    %% 管理层
    J[👔 管理者/主管<br/>监督审批]:::support

    %% 质量层
    I[📋 回访员<br/>质量监督]:::business

    %% 支撑层
    K[⚙️ 系统管理员<br/>总设计师]:::support
    
    %% 流程关系
    A -->|诉求| B
    B -->|分派| C
    C -->|专业分派| D
    C -->|属地分派| F
    D -->|科室分派| E
    E -->|任务指派| G
    F -->|网格指派| G
    C -->|协办| H
    G -->|上报| J
    H -->|反馈| C
    J -->|审核| I
    I -->|回访| A

    %% 管理关系
    K -.->|配置| B
    K -.->|配置| C
    K -.->|配置| D
    J -.->|管理| G
    J -.->|管理| H
    
    %% 样式定义
    classDef external fill:#ffebee,stroke:#e74c3c,stroke-width:3px,color:#2c3e50
    classDef business fill:#e3f2fd,stroke:#3498db,stroke-width:3px,color:#2c3e50
    classDef support fill:#fff3e0,stroke:#f39c12,stroke-width:3px,color:#2c3e50
    `;
    
    return diagram;
}

/**
 * 渲染角色关系图
 */
function renderRoleDiagram(diagramType = 'detailed') {
    const diagramContainer = document.getElementById('role-diagram');
    if (!diagramContainer) return;
    
    // 清空容器
    diagramContainer.innerHTML = '';
    
    // 选择图表类型
    let diagramCode;
    switch (diagramType) {
        case 'simplified':
            diagramCode = generateSimplifiedRoleDiagram();
            break;
        case 'hierarchy':
            diagramCode = generateRoleHierarchyDiagram();
            break;
        default:
            diagramCode = generateRoleDiagram();
    }
    
    try {
        // 配置Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                primaryColor: '#3498db',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#2980b9',
                lineColor: '#7f8c8d',
                secondaryColor: '#ecf0f1',
                tertiaryColor: '#f8f9fa'
            }
        });
        
        // 渲染图表
        mermaid.render('role-diagram-svg', diagramCode).then(({ svg }) => {
            diagramContainer.innerHTML = svg;
            
            // 优化SVG显示
            const svgElement = diagramContainer.querySelector('svg');
            if (svgElement) {
                svgElement.style.width = '100%';
                svgElement.style.height = 'auto';
                svgElement.style.maxWidth = '100%';
            }
        }).catch(error => {
            console.error('Mermaid渲染错误:', error);
            diagramContainer.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #7f8c8d;">
                    <p>图表渲染失败，请刷新页面重试</p>
                    <button onclick="renderRoleDiagram()" style="margin-top: 10px; padding: 8px 16px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        });
        
    } catch (error) {
        console.error('Mermaid初始化错误:', error);
        diagramContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #e74c3c;">
                <p>图表加载失败，请检查网络连接</p>
            </div>
        `;
    }
}

/**
 * 切换图表类型
 */
function switchDiagramType(type) {
    renderRoleDiagram(type);
}

// 导出函数
window.renderRoleDiagram = renderRoleDiagram;
window.switchDiagramType = switchDiagramType;
window.generateRoleDiagram = generateRoleDiagram;
window.generateSimplifiedRoleDiagram = generateSimplifiedRoleDiagram;
window.generateRoleHierarchyDiagram = generateRoleHierarchyDiagram;
