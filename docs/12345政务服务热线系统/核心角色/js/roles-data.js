/**
 * 12345政务服务热线系统核心角色数据
 */

// 角色数据定义
const rolesData = {
    // 外部角色
    external: [
        {
            id: 'customer',
            name: '市民',
            englishName: 'Customer / Citizen',
            icon: '👥',
            position: '服务的发起者与最终评价者',
            responsibilities: [
                '提出诉求',
                '配合处理过程',
                '在回访环节提供真实的服务反馈和满意度评价'
            ],
            category: 'external',
            color: '#e74c3c'
        }
    ],
    
    // 核心业务角色
    business: [
        {
            id: 'operator',
            name: '市级12345中心话务员/分派员',
            englishName: 'Operator / Dispatcher',
            icon: '📞',
            position: '总受理平台与一级分派/办结中心',
            responsibilities: [
                {
                    title: '受理与创建',
                    description: '作为全市工单的唯一入口，负责接听、创建工单，并进行标准化信息录入'
                },
                {
                    title: '即时办结（第一道出口）',
                    description: '对于无需流转的"政策咨询"、"信息查询"等简单诉求，利用知识库直接答复市民，并即时办结工单，避免无效流转'
                },
                {
                    title: '一级分派',
                    description: '对于需要流转的工单，严格按照地域属性，仅对各区/县级总承办单位进行规范化分派'
                }
            ],
            category: 'business',
            color: '#3498db'
        },
        {
            id: 'hierarchical-dispatcher',
            name: '各层级工单管理员/分派员',
            englishName: 'Hierarchical Dispatcher',
            icon: '🏢',
            position: '各层级枢纽的"分流阀"与"办结阀"',
            responsibilities: [
                {
                    title: '接收与分派',
                    description: '作为"总承办单位"（如区12345分中心、街道办事处、区职能部门、业务科室）的核心人员，负责接收上级工单，并根据事权在本级进行二次精准分派'
                },
                {
                    title: '即时办结（关键效率节点）',
                    description: '在接收工单后，第一时间判断是否需要继续下派。如果本级能够独立解决，则果断即时办结，防止工单进一步下沉'
                },
                {
                    title: '多部门协同',
                    description: '当工单需要多个部门协同处理时，负责指定主办部门和协办部门，统筹协调各部门间的协作'
                },
                {
                    title: '协办执行',
                    description: '当本单位作为协办部门时，积极配合主办部门，按时完成分配的协办任务并及时反馈进度'
                }
            ],
            includes: '此岗位职能贯穿于区/县级分中心、区级职能部门、街/镇总承办单位、业务科室、社区/村等多个枢纽节点',
            category: 'business',
            color: '#3498db'
        },
        {
            id: 'functional-department',
            name: '区级/街镇职能部门',
            englishName: 'Functional Department',
            icon: '🏛️',
            position: '"条线"上的专业管理与分派平台',
            responsibilities: [
                '接收来自上级"枢纽"的专业性工单，进行"即时办结"判断',
                '或分派给下属的业务科室'
            ],
            includes: '区市场监管局、区住建局、街道城管科等',
            category: 'business',
            color: '#3498db'
        },
        {
            id: 'business-section',
            name: '业务科室',
            englishName: 'Business Section',
            icon: '📋',
            position: '专业执行的"最后一公里"枢纽',
            responsibilities: [
                {
                    title: '接收任务',
                    description: '接收来自上级职能部门的工单'
                },
                {
                    title: '即时办结',
                    description: '基于科室的专业信息进行判断，若能解决则直接即时办结'
                },
                {
                    title: '精准指派',
                    description: '将需要现场执行的任务，精准指派给本科室的工作人员'
                },
                {
                    title: '审核与反馈',
                    description: '作为科室工作人员的直接管理者，负责对其提交的"办结"结果进行第一道审核'
                }
            ],
            category: 'business',
            color: '#3498db'
        },
        {
            id: 'community-committee',
            name: '社区/村委会',
            englishName: 'Community/Village Committee',
            icon: '🏘️',
            position: '属地管理的"最后一公里"枢纽与五级分派平台',
            responsibilities: [
                {
                    title: '接收任务',
                    description: '接收来自"街/镇"下派的、与本辖区居民直接相关的工单'
                },
                {
                    title: '即时办结（基层智慧节点）',
                    description: '如果问题是社区层面通过协调、解释就能解决的，则直接即时办结'
                },
                {
                    title: '精准指派',
                    description: '对于必须由网格员现场处理的任务，负责将工单精准指派给负责该片区的网格员'
                },
                {
                    title: '审核与反馈',
                    description: '作为网格员的直接管理者，负责对其提交的"办结"结果进行第一道审核'
                }
            ],
            category: 'business',
            color: '#3498db'
        },

        {
            id: 'field-agent',
            name: '最终执行者',
            englishName: 'Field Agent',
            icon: '🚶',
            position: '深入一线的"手和脚"，所有任务的最终落点',
            responsibilities: [
                {
                    title: '现场执行',
                    description: '负责所有需要现场核实、处理、调解和信息上报的"最后一公里"任务'
                },
                {
                    title: '办结上报',
                    description: '在完成现场任务后，点击"办结"，并提交详实的图文处理结果，触发"逐级审核"流程'
                }
            ],
            includes: '网格员（由社区/村委派）、科室工作人员（由各级业务科室委派）',
            category: 'business',
            color: '#3498db'
        },

        {
            id: 'revisit-agent',
            name: '回访员',
            englishName: 'Revisit Agent',
            icon: '📋',
            position: '独立的第三方服务质量监督员',
            responsibilities: [
                '在所有内部流程（包括即时办结）结束后，对市民进行满意度回访',
                '作为服务质量的最终把关人，并负责关闭或重启工单'
            ],
            category: 'business',
            color: '#3498db'
        }
    ],
    
    // 管理支持角色
    support: [
        {
            id: 'manager',
            name: '管理者/主管',
            englishName: 'Manager / Supervisor',
            icon: '👔',
            position: '流程的监督者与审批者',
            responsibilities: [
                {
                    title: '审核',
                    description: '对下级单位提交的"办结"或"即时办结"结果进行审核，确保处理质量'
                },
                {
                    title: '审批',
                    description: '对下属提交的特殊申请（如延期、挂起）进行裁决'
                }
            ],
            includes: '各层级、各单位的负责人',
            category: 'support',
            color: '#f39c12'
        },
        {
            id: 'admin',
            name: '系统管理员',
            englishName: 'System Administrator',
            icon: '⚙️',
            position: '系统的"总设计师"与维护者',
            responsibilities: [
                '负责在后台配置整个系统的复杂组织架构',
                '设置自动化流转规则（包括对即时办结工单的特殊流转设定）',
                '配置SLA策略、工单模板等'
            ],
            category: 'support',
            color: '#f39c12'
        }
    ]
};

// 角色关系定义
const roleRelationships = [
    { from: 'customer', to: 'operator', label: '提出诉求' },
    { from: 'operator', to: 'hierarchical-dispatcher', label: '一级分派' },
    { from: 'hierarchical-dispatcher', to: 'functional-department', label: '专业分派' },
    { from: 'functional-department', to: 'business-section', label: '科室分派' },
    { from: 'hierarchical-dispatcher', to: 'community-committee', label: '属地分派' },
    { from: 'business-section', to: 'field-agent', label: '任务指派' },
    { from: 'community-committee', to: 'field-agent', label: '网格指派' },
    { from: 'hierarchical-dispatcher', to: 'hierarchical-dispatcher', label: '多部门协同' },
    { from: 'field-agent', to: 'manager', label: '办结上报' },
    { from: 'manager', to: 'revisit-agent', label: '审核通过' },
    { from: 'revisit-agent', to: 'customer', label: '回访反馈' },
    { from: 'admin', to: 'operator', label: '系统配置' },
    { from: 'admin', to: 'hierarchical-dispatcher', label: '流程配置' },
    { from: 'admin', to: 'functional-department', label: '规则配置' }
];

// 获取所有角色
function getAllRoles() {
    return [
        ...rolesData.external,
        ...rolesData.business,
        ...rolesData.support
    ];
}

// 根据类别获取角色
function getRolesByCategory(category) {
    if (category === 'all') {
        return getAllRoles();
    }
    return rolesData[category] || [];
}

// 根据ID获取角色
function getRoleById(id) {
    const allRoles = getAllRoles();
    return allRoles.find(role => role.id === id);
}

// 获取角色统计
function getRoleStatistics() {
    return {
        external: rolesData.external.length,
        business: rolesData.business.length,
        support: rolesData.support.length,
        total: getAllRoles().length
    };
}

// 角色统计
const roleStats = {
    total: 10,
    business: 7,
    support: 3
};

// 导出数据
window.rolesData = rolesData;
window.roleRelationships = roleRelationships;
window.getAllRoles = getAllRoles;
window.getRolesByCategory = getRolesByCategory;
window.getRoleById = getRoleById;
window.getRoleStatistics = getRoleStatistics;
