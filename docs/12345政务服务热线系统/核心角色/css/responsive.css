/* 响应式设计 */

/* 移动端样式 (768px以下) */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .header {
        padding: 30px 0;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .header p {
        font-size: 1em;
    }
    
    .content {
        padding: 0 10px 30px;
    }

    .search-container {
        margin-bottom: 20px;
    }

    .search-box {
        max-width: 100%;
        margin: 0 10px;
    }

    #search-input {
        font-size: 16px;
        padding: 10px 15px;
    }

    .search-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
    
    .role-categories {
        gap: 10px;
    }
    
    .category-btn {
        padding: 10px 18px;
        font-size: 14px;
    }
    
    .role-diagram-container {
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .role-diagram-container h3 {
        font-size: 1.3em;
    }
    
    .diagram-wrapper {
        min-height: 300px;
        padding: 15px;
    }
    
    .roles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .role-card {
        border-radius: 12px;
    }
    
    .role-header {
        padding: 15px;
    }
    
    .role-icon {
        font-size: 2em;
    }
    
    .role-header h3 {
        font-size: 1.2em;
    }
    
    .role-content {
        padding: 15px;
    }
    
    .role-position {
        font-size: 1em;
        padding: 8px;
    }
    
    .role-responsibilities li {
        font-size: 0.9em;
        margin-bottom: 6px;
    }
    
    .role-includes {
        font-size: 0.85em;
        padding: 8px;
    }
    
    .role-statistics {
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .role-statistics h3 {
        font-size: 1.3em;
        margin-bottom: 20px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .stat-item {
        padding: 15px;
    }
    
    .stat-number {
        font-size: 2em;
    }
    
    .stat-label {
        font-size: 0.9em;
    }
}

/* 超小屏幕 (480px以下) */
@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8em;
    }
    
    .role-categories {
        flex-direction: column;
        align-items: center;
    }
    
    .category-btn {
        width: 200px;
        text-align: center;
    }
    
    .role-diagram-container {
        padding: 15px;
    }
    
    .diagram-wrapper {
        min-height: 250px;
        padding: 10px;
    }
    
    .role-header {
        padding: 12px;
    }
    
    .role-icon {
        font-size: 1.8em;
    }
    
    .role-header h3 {
        font-size: 1.1em;
    }
    
    .role-content {
        padding: 12px;
    }
    
    .role-responsibilities li {
        font-size: 0.85em;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-item {
        padding: 12px;
    }
    
    .stat-number {
        font-size: 1.8em;
    }
}

/* 平板端样式 */
@media (min-width: 769px) and (max-width: 1199px) {
    .container {
        max-width: 96vw;
    }
    
    .roles-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .role-diagram-container {
        padding: 25px;
    }
    
    .diagram-wrapper {
        min-height: 350px;
    }
}

/* 大屏幕样式 */
@media (min-width: 1200px) {
    .container {
        max-width: 98vw;
    }
    
    .roles-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .role-diagram-container {
        padding: 35px;
    }
    
    .diagram-wrapper {
        min-height: 450px;
    }
    
    .header h1 {
        font-size: 3em;
    }
    
    .header p {
        font-size: 1.3em;
    }
}

/* 超大屏幕样式 */
@media (min-width: 1600px) {
    .container {
        max-width: 1600px;
    }
    
    .roles-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
    }
    
    .container {
        box-shadow: none;
        max-width: none;
    }
    
    .header {
        background: #2c3e50 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .role-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .role-statistics {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .category-btn {
        display: none;
    }
    
    .role-diagram-container {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}
