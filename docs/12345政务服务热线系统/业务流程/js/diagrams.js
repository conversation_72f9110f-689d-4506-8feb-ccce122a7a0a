/**
 * 流程图定义文件
 * 包含所有阶段的Mermaid流程图定义
 */

const diagrams = {
    // 总体流程概览
    overview: `
        flowchart TD
            A[市民诉求] --> B[市级12345中心话务员<br/>新建工单录入信息]
            B --> C{一级分派决策}

            C -->|A.即时办结| D[直接答复咨询类诉求<br/>跳至阶段四] --> E[待回访]
            C -->|B.市级事权| F[市级职能部门<br/>轨道一]
            C -->|C.区级事权| G[区县12345分中心<br/>轨道二]
            C -->|D.市级多部门协同| H[市级构建多部门协同结构<br/>一主多协同时派发]

            H --> H1[主办部门：市级职能部门]
    H --> H2[协办部门1：其他市级部门]
    H --> H3[协办部门2：相关区县分中心]
    H1 --> H4[主办部门统筹协调]
            H2 --> H4
            H3 --> H4
            H4 --> H5[联合处置完成] --> K
            
            F --> I{市级部门总口<br/>能否即时办结?}
            I -->|是| J[市级即时办结<br/>跳至阶段三] --> K[逐级审核]
            I -->|否| L[下派至业务处室/科室]
            L --> M{科室能否<br/>即时办结?}
            M -->|是| N[科室即时办结<br/>跳至阶段三] --> K
            M -->|否| O[指派具体工作人员]
            O --> P[工作人员执行任务<br/>补记过程] --> Q[点击办结<br/>进入阶段三] --> K
            
            G --> R{区级总口<br/>能否即时办结?}
            R -->|是| S[区级即时办结<br/>跳至阶段三] --> K
            R -->|否| T{区内二次分派<br/>路径选择}
            
            T -->|A.属地路线| U[街镇级总承办单位]
            T -->|B.职能路线| V[区级职能部门]
            T -->|C.区级多部门协同| W[区级构建多部门协同结构<br/>街镇+区职能部门协同]

            W --> W1[主办部门：街镇总承办单位]
    W --> W2[协办部门1：区级职能部门]
    W --> W3[协办部门2：其他相关单位]
    W1 --> W4[主办部门统筹协调]
            W2 --> W4
            W3 --> W4
            W4 --> W5[区级协同处置完成] --> K
            
            U --> X{街镇能否<br/>即时办结?}
            X -->|是| Y[街镇即时办结] --> K
            X -->|否| Z{街镇级分派决策}
            Z -->|单一下派| AA[下派至街道内设部门<br/>或社区/村委会]
            Z -->|街镇多部门协同| BB[街镇构建多部门协同结构<br/>多部门协同]

            AA --> CC{社区能否<br/>即时办结?}
            CC -->|是| DD[社区即时办结] --> K
            CC -->|否| EE[指派网格员]
            EE --> FF[网格员执行现场任务] --> GG[网格员办结] --> K

            BB --> BB1[主办部门：街道综治办]
    BB --> BB2[协办部门1：街道城管科]
    BB --> BB3[协办部门2：社区卫生中心]
    BB --> BB4[协办部门3：社区居委会]
            BB1 --> BB5[街镇协同处置]
            BB2 --> BB5
            BB3 --> BB5
            BB4 --> BB5
            BB5 --> GG
            
            V --> HH{区职能部门<br/>能否即时办结?}
            HH -->|是| II[区部门即时办结] --> K
            HH -->|否| JJ[下派至业务科室]
            JJ --> KK{科室能否<br/>即时办结?}
            KK -->|是| LL[科室即时办结] --> K
            KK -->|否| MM[指派一线工作人员]
            MM --> NN[工作人员执行任务] --> OO[工作人员办结] --> K
            
            K --> PP[阶段三：严格原路返回审核<br/>常规审核 vs 多部门协同审核] --> QQ[阶段四：审核通过汇总至市级平台<br/>常规工单 + 多部门协同工单统一汇入] --> E

            E --> RR[阶段五：市级回访中心统一回访]
            RR -->|满意| SS[工单已关闭]
            RR -->|不满意| TT[重启工单重办督办] --> B

            classDef immediate fill:#d5f4e6,stroke:#27ae60,stroke-width:2px
            classDef municipal fill:#fef9e7,stroke:#f39c12,stroke-width:2px
            classDef district fill:#ebf3fd,stroke:#3498db,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef review fill:#fff3cd,stroke:#ffc107,stroke-width:2px

            class D,Y,DD,II,LL immediate
            class F,I,L,M,O,P,Q municipal
            class G,R,T,U,V,X,Z,AA,CC,EE,HH,JJ,KK,MM,NN,OO district
            class H,H1,H2,H3,H4,H5,W,W1,W2,W3,W4,W5,BB,BB1,BB2,BB3,BB4,BB5,TT collaborative
            class K,PP,QQ review
    `,

    // 阶段一：市级统一受理与顶层战略分派
    stage1: `
        flowchart TD
            A[市民通过12345热线提出诉求] --> B[市级话务员接听并新建工单]
            B --> C[录入标准化信息]
            C --> D{一级分派决策<br/>条块战略选择}

            D -->|场景A| E[即时办结<br/>政策咨询/信息查询]
            E --> F[填写办结说明] --> G[跳过下派环节] --> H[待回访状态]

            D -->|场景B| I[派发至市级职能部门<br/>条线处理]
            I --> J[全市范围宏观政策<br/>跨区域重大事件<br/>市级垂直管理事项]
            J --> K[市级部门内部流转]

            D -->|场景C| L[派发至区县级总口<br/>块线处理]
            L --> M[具体地点民生诉求<br/>属地化管理事项]
            M --> N[区县12345分中心]

            D -->|场景D| O[市级多部门协同<br/>跨部门跨层级重大复杂问题]
            O --> P{构建多部门协同结构}
            P --> Q[确定主办部门<br/>如：市生态环境局]
    P --> R[确定协办部门1<br/>如：市水务局]
    P --> S[确定协办部门2<br/>如：A区12345分中心]
    P --> T[确定协办部门3<br/>如：B区12345分中心]

            Q --> U[主办部门接收工单<br/>负责总协调]
    R --> V[协办部门1接收工单<br/>提供专业支撑]
    S --> W[协办部门2接收工单<br/>负责属地配合]
    T --> X[协办部门3接收工单<br/>负责属地配合]

            U --> Y[一主多协同时开展工作]
            V --> Y
            W --> Y
            X --> Y
            Y --> Z[信息共享实时补记<br/>联合处置问题]

            classDef immediate fill:#d5f4e6,stroke:#27ae60,stroke-width:2px
            classDef municipal fill:#fef9e7,stroke:#f39c12,stroke-width:2px
            classDef district fill:#ebf3fd,stroke:#3498db,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef process fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px

            class E,F,G,H immediate
            class I,J,K municipal
            class L,M,N district
            class O,P,Q,R,S,T,U,V,W,X,Y,Z collaborative
    `,

    // 阶段二：工单进入不同轨道的二次分派与处理
    stage2: `
        flowchart TD
            A[阶段二：工单进入不同轨道] --> B{轨道选择}

            B -->|轨道一：市级职能部门| C[市级职能部门总口接收工单]
            C --> D{能否即时办结?}
            D -->|是| E[市级总口即时办结<br/>跳至阶段四]
            D -->|否| F[根据职责下派至<br/>内部具体业务处室/科室]

            F --> G[市级业务科室接收工单]
            G --> H{科室能否即时办结?}
            H -->|是| I[科室即时办结<br/>跳至阶段四]
            H -->|否| J[指派给具体工作人员进行处理]

            J --> K[市级工作人员执行任务]
            K --> L[调查/出具报告/联系企业等<br/>在系统中补记过程]
            L --> M[任务完成后点击办结<br/>进入阶段四]

            B -->|轨道二：区县分中心| N[区县12345分中心接收工单]
            N --> O{区级总口能否即时办结?}
            O -->|是| P[区级总口即时办结<br/>跳至阶段四]
            O -->|否| Q{区内二次分派路径选择}

            Q -->|A.属地路线| R[派发至街镇级总承办单位]
            Q -->|B.职能路线| S[派发至区级职能部门]
            Q -->|C.区级多部门协同| T[构建区级多部门协同结构<br/>条块结合协同处理]

            T --> T1[确定主办部门：街镇总承办单位<br/>（属地-块）]
    T --> T2[确定协办部门1：区住建局<br/>（职能-条）]
    T --> T3[确定协办部门2：区生态环境局<br/>（职能-条）]
            T1 --> T4[区级协同团队并行处理]
            T2 --> T4
            T3 --> T4

            R --> U[街镇总承办单位接收]
            U --> V{街镇能否即时办结?}
            V -->|是| W[街镇即时办结]
            V -->|否| X{街镇级分派决策}
            X -->|单一下派| Y[下派至街道内设部门<br/>或社区/村委会]
            X -->|街镇多部门协同| Z[构建街镇级多部门协同结构<br/>内部多单位协同]

            Y --> AA[社区/村委会接收]
            AA --> BB{社区能否即时办结?}
            BB -->|是| CC[社区即时办结]
            BB -->|否| DD[最终指派给网格员]

            Z --> Z1[确定主办部门：街道综治办<br/>（总协调兜底）]
    Z --> Z2[确定协办部门1：街道城管科<br/>（专业捕捉）]
    Z --> Z3[确定协办部门2：社区卫生中心<br/>（防疫评估）]
    Z --> Z4[确定协办部门3：社区居委会<br/>（现场协助）]
            Z1 --> Z5[街镇协同团队联合处理]
            Z2 --> Z5
            Z3 --> Z5
            Z4 --> Z5

            S --> EE[区级职能部门接收]
            EE --> FF{区部门能否即时办结?}
            FF -->|是| GG[区部门即时办结]
            FF -->|否| HH[下派至业务科室]

            HH --> II[区业务科室接收]
            II --> JJ{科室能否即时办结?}
            JJ -->|是| KK[科室即时办结]
            JJ -->|否| LL[最终指派给一线工作人员]

            classDef immediate fill:#d5f4e6,stroke:#27ae60,stroke-width:2px
            classDef municipal fill:#fef9e7,stroke:#f39c12,stroke-width:2px
            classDef district fill:#ebf3fd,stroke:#3498db,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef execution fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px

            class E,I,P,W,CC,GG,KK immediate
            class C,F,G,J,K,L,M municipal
            class N,R,S,U,X,Y,AA,EE,HH,II district
            class T,T1,T2,T3,T4,Z,Z1,Z2,Z3,Z4,Z5 collaborative
            class DD,LL execution
    `,

    // 阶段三：逐级审核反馈
    stage3: `
        flowchart TD
            A[阶段三：逐级审核反馈<br/>细化的返回路径] --> B{工单类型判断}

            B -->|常规单一派发工单| C[任何层级办结后<br/>进入待审核状态]
            B -->|多部门协同工单| D[多部门协同复杂审核流程]

            C --> E[严格原路返回审核<br/>核心原则：谁派来谁审核]

            E --> F{常规审核路径示例}

            F -->|属地路线| G[网格员办结]
            G --> H[社区审核]
            H --> I{社区审核结果}
            I -->|通过| J[街道审核]
            I -->|退回| K[返工或补充说明] --> G

            J --> L{街道审核结果}
            L -->|通过| M[区级分中心审核]
            L -->|退回| N[退回社区] --> H

            M --> O{区级分中心审核结果}
            O -->|通过| P[提交至市级平台<br/>进入阶段四]
            O -->|退回| Q[退回街道] --> J

            D --> R[多部门协同审核：先分后总，先内后外]
            R --> S[阶段一：各参与单位内部执行办结]
            S --> T[阶段二：各单位内部审核闭环]
            T --> U[阶段三：协办部门向主办部门提交确认]
    U --> V[阶段四：主办部门总办结]
            V --> W[阶段五：派单方最终审核]

            S --> S1[主办部门内部：网格员、城管科等办结]
    S --> S2[协办部门1内部：一线工作人员办结]
    S --> S3[协办部门2内部：监测人员办结]

            T --> T1[主办部门内部审核：社区→科室→街道]
    T --> T2[协办部门1内部审核：科室→部门]
    T --> T3[协办部门2内部审核：科室→部门]

            U --> U1[协办部门1：提交协办完成确认]
    U --> U2[协办部门2：提交协办完成确认]
    U1 --> V1[主办部门收到所有协办确认]
    U2 --> V1

            V1 --> V2[主办部门汇总形成最终办结报告]
    V2 --> V3[主办部门点击办结，工单状态变为待审核]

            W --> W1{派单方审核结果}
            W1 -->|通过| P
            W1 -->|退回| W2[退回主办部门重新协调] --> V

            F -->|职能路线| R[区工作人员办结]
            R --> S[区业务科室审核]
            S --> T{科室审核结果}
            T -->|通过| U[区职能部门审核]
            T -->|退回| V[返工] --> R

            U --> W{部门审核结果}
            W -->|通过| X[区级分中心审核]
            W -->|退回| Y[退回科室] --> S

            X --> Z{区级分中心审核结果}
            Z -->|通过| P
            Z -->|退回| AA[退回区职能部门] --> U

            F -->|市级路线| BB[市工作人员办结]
            BB --> CC[市业务处室审核]
            CC --> DD{处室审核结果}
            DD -->|通过| EE[市职能部门审核]
            DD -->|退回| FF[返工] --> BB

            EE --> GG{部门审核结果}
            GG -->|通过| HH[直接提交至市级平台<br/>进入阶段四]
            GG -->|退回| II[退回处室] --> CC

            classDef review fill:#fff3cd,stroke:#ffc107,stroke-width:2px
            classDef approval fill:#d5f4e6,stroke:#27ae60,stroke-width:2px
            classDef rejection fill:#ffebee,stroke:#f44336,stroke-width:2px
            classDef municipal fill:#fef9e7,stroke:#f39c12,stroke-width:2px
            classDef district fill:#ebf3fd,stroke:#3498db,stroke-width:2px
            classDef nextstage fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px

            class C,E,H,J,M,S,U,X,CC,EE review
            class I,L,O,T,W,Z,DD,GG approval
            class K,N,Q,V,Y,AA,FF,II rejection
            class BB,CC,EE,FF,II municipal
            class G,H,J,M,R,S,U,X district
            class P,HH nextstage
            class D,R,S1,S2,S3,T1,T2,T3,U1,U2,V1,V2,V3,W1,W2 collaborative
    `,

    // 阶段四：统一汇入回访流程
    stage4: `
        flowchart TD
            A[阶段四：统一汇入回访流程] --> B[各路径审核通过后汇总]

            B --> C{反馈来源分类}

            C -->|常规市级职能部门路径| D[市级部门审核通过]
            D --> E[市级部门向市级平台<br/>提交办结反馈]
            E --> F[市级平台接收反馈]

            C -->|常规区县分中心路径| G[区县分中心审核通过]
            G --> H[区县分中心向市级平台<br/>提交办结反馈]
            H --> F

            C -->|市级多部门协同路径| I[市级多部门协同审核通过]
            I --> J[主办部门（如市生态环境局）<br/>代表整个协同团队<br/>向市级平台提交反馈]
            J --> F

            C -->|区级多部门协同路径| K[区级多部门协同审核通过]
            K --> L[派单方（区12345分中心）<br/>审核主办部门提交的<br/>协同团队成果后<br/>向市级平台提交反馈]
            L --> F

            C -->|街镇多部门协同路径| M[街镇多部门协同审核通过]
            M --> N[经过逐级审核后<br/>最终由区级分中心<br/>向市级平台提交反馈]
            N --> F

            F --> O[市级平台汇总所有反馈<br/>包括常规工单和多部门协同工单]
            O --> P[系统自动更新工单状态]
            P --> Q[工单状态统一更新为<br/>【待回访】]

            Q --> R[进入阶段五：回访流程<br/>无论常规还是多部门协同<br/>都采用统一回访标准]

            classDef municipal fill:#fef9e7,stroke:#f39c12,stroke-width:2px
            classDef district fill:#ebf3fd,stroke:#3498db,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef feedback fill:#d1ecf1,stroke:#17a2b8,stroke-width:2px
            classDef system fill:#f8f9fa,stroke:#6c757d,stroke-width:2px

            class D,E,I,J municipal
            class G,H,K,L,M,N district
            class I,J,K,L,M,N collaborative
            class F,O,P,Q,R feedback
            class A,B,C system
    `,

    // 阶段五：市级统一回访与最终关闭
    stage5: `
        flowchart TD
            A[工单状态：待回访] --> B[市级回访中心]
            B --> C[统一回访所有待回访工单]
            C --> D[回访员联系市民]
            D --> E{市民满意度}

            E -->|满意| F[回访员将工单标记为已关闭]
            F --> G[工单完成闭环]

            E -->|不满意| H[回访员重启工单]
            H --> I[附上重办督办意见]
            I --> J[退回至市级12345中心]
            J --> K[启动新一轮更高级别督办处理]
            K --> L[重新进入分派流程]

            classDef callback fill:#e1f5fe,stroke:#0288d1,stroke-width:2px
            classDef closed fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px
            classDef restart fill:#ffebee,stroke:#f44336,stroke-width:2px

            class A,B,C,D callback
            class F,G closed
            class H,I,J,K,L restart
    `,

    // 市级多部门协同流程
    'municipal-collab': `
        flowchart TD
            A[市民集体投诉：跨区域河流污染] --> B[市级12345中心受理]
            B --> C[判断需要跨部门协同处理]
            C --> D[构建市级多部门协同结构]

            D --> E[确定主办部门：市生态环境局]
    D --> F[协办部门1：市水务局]
    D --> G[协办部门2：A区12345分中心]
    D --> H[协办部门3：B区12345分中心]

            E --> I[市生态环境局内部分派]
            I --> J[监测处：水质采样检测]
            I --> K[执法支队：企业排污调查]

            F --> L[市水务局配合调查]
            L --> M[河道管理员现场配合]
            L --> N[制定河道清理方案]

            G --> O[A区分中心下派]
            O --> P[A区街道社区网格员]
            P --> Q[A区河段巡查安抚]

            H --> R[B区分中心下派]
            R --> S[B区街道社区网格员]
            S --> T[B区河段巡查安抚]

            J --> U[检测报告：确认污染源]
            K --> V[执法结果：企业违法排污]
            M --> W[现场配合完成]
            N --> X[清理方案制定完成]
            Q --> Y[A区现场工作完成]
            T --> Z[B区现场工作完成]

            U --> AA[主办部门汇总所有信息]
            V --> AA
            W --> AA
            X --> AA
            Y --> AA
            Z --> AA

            AA --> BB[市生态环境局综合处置]
            BB --> CC[对企业开出罚单并责令整改]
            BB --> DD[启动环境修复程序]

            CC --> EE[主办部门办结工单]
            DD --> EE
            EE --> FF[直接进入市级回访流程]

            classDef municipal fill:#fef9e7,stroke:#f39c12,stroke-width:2px
            classDef district fill:#ebf3fd,stroke:#3498db,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef execution fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px
            classDef result fill:#f8f9fa,stroke:#6c757d,stroke-width:2px

            class A,B,C,D municipal
            class E,I,J,K,F,L,M,N municipal
            class G,O,P,Q,H,R,S,T district
            class AA,BB,CC,DD,EE collaborative
            class U,V,W,X,Y,Z execution
            class FF result
    `,

    // 区级多部门协同流程
    'district-collab': `
        flowchart TD
            A[建筑垃圾倾倒投诉] --> B[市级派发至区12345分中心]
            B --> C[区级判断需要条块结合处理]
            C --> D[构建区级多部门协同结构]

            D --> E[确定主办部门：XX街道办事处<br/>（属地-块）]
    D --> F[协办部门1：区住建局<br/>（职能-条）]
    D --> G[协办部门2：区生态环境局<br/>（职能-条）]

            E --> H[街道办事处内部分派]
            H --> I[YY社区居委会]
            H --> J[街道城管科]

            I --> K[社区网格员现场巡查]
            J --> L[街道城管现场管控]

            F --> M[区住建局内部分派]
            M --> N[建筑管理科调查]
            N --> O[追查垃圾源头和施工单位]

            G --> P[区生态环境局内部分派]
            P --> Q[环境监察科评估]
            Q --> R[评估环境风险等级]

            K --> S[收集现场证据和群众反映]
            L --> T[现场管控和初步清理]
            O --> U[确定责任施工单位]
            R --> V[出具环境风险评估报告]

            S --> W[主办部门（街道）汇总信息]
            T --> W
            U --> W
            V --> W

            W --> X[街道协调各方联合处置]
            X --> Y[责令施工单位清理垃圾]
            X --> Z[加强后续监管措施]

            Y --> AA[街道办结工单]
            Z --> AA
            AA --> BB[区级分中心审核]
            BB --> CC[市级平台回访]

            classDef district fill:#ebf3fd,stroke:#3498db,stroke-width:2px
            classDef street fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef execution fill:#fff3cd,stroke:#ffc107,stroke-width:2px
            classDef result fill:#f8f9fa,stroke:#6c757d,stroke-width:2px

            class A,B,C,D district
            class E,H,I,J,K,L street
            class F,M,N,O,G,P,Q,R district
            class W,X,Y,Z,AA collaborative
            class S,T,U,V execution
            class BB,CC result
    `,

    // 街镇级多部门协同流程
    'street-collab': `
        flowchart TD
            A[流浪犬伤人隐患投诉] --> B[市级→区级→派发至XX街道办事处]
            B --> C[街道判断需要内部多单位协同]
            C --> D[构建街道级多部门协同结构]

            D --> E[确定主办部门：街道综治办<br/>（总协调兜底）]
    D --> F[协办部门1：街道城管科<br/>（专业捕捉）]
    D --> G[协办部门2：社区卫生服务中心<br/>（防疫评估）]
    D --> H[协办部门3：YY社区居委会<br/>（现场协助）]

            H --> I[社区网格员行动]
            I --> J[张贴处理通知]
            I --> K[居民群安全提醒]
            I --> L[收集犬只出没信息]

            G --> M[卫生中心制作宣传材料]
            M --> N[狂犬病防疫宣传单]
            M --> O[人畜共患病预防指南]

            F --> P[城管科制定捕捉方案]
            P --> Q[准备专业工具设备]
            P --> R[确定最佳捕捉时间]

            E --> S[综治办协调各方]
            S --> T[统筹时间安排]
            S --> U[跟进各方进度]

            L --> V[现场联合执行]
            N --> V
            Q --> V
            T --> V

            V --> W[城管科专业人员捕捉犬只]
            V --> X[社区网格员现场指引协助]
            V --> Y[卫生中心消杀预防]
            V --> Z[居委会向居民通报结果]

            W --> AA[主办部门（综治办）汇总]
            X --> AA
            Y --> AA
            Z --> AA

            AA --> BB[街道办事处办结]
            BB --> CC[区级部门审核]
            CC --> DD[区12345分中心审核]
            DD --> EE[市级平台回访]

            classDef street fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px
            classDef community fill:#e1f5fe,stroke:#0288d1,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef execution fill:#fff3cd,stroke:#ffc107,stroke-width:2px
            classDef result fill:#f8f9fa,stroke:#6c757d,stroke-width:2px

            class A,B,C,D street
            class E,F,G,H,S,T,U street
            class I,J,K,L,H community
            class M,N,O,P,Q,R street
            class V,W,X,Y,Z,AA,BB collaborative
            class CC,DD,EE result
    `,

    // 多部门协同审核流程详解
    'collab-review': `
        flowchart TD
            A[多部门协同工单处理完成] --> B[进入多部门协同审核流程<br/>先分后总，先内后外]

            B --> C[阶段一：各参与单位内部执行办结]
            C --> C1[主办部门（街道）内部]
    C --> C2[协办部门1（区住建局）内部]
    C --> C3[协办部门2（区生态环境局）内部]

            C1 --> C11[网格员完成现场任务]
            C1 --> C12[街道城管科完成管控]
            C11 --> C13[网格员点击办结]
            C12 --> C14[城管科工作人员点击办结]

            C2 --> C21[一线工作人员完成源头追查]
            C21 --> C22[工作人员点击办结]

            C3 --> C31[监测人员完成环境评估]
            C31 --> C32[监测人员点击办结]

            C13 --> D[阶段二：各单位内部审核闭环]
            C14 --> D
            C22 --> D
            C32 --> D

            D --> D1[主办部门（街道）内部审核]
    D --> D2[协办部门1（区住建局）内部审核]
    D --> D3[协办部门2（区生态环境局）内部审核]

            D1 --> D11[社区负责人审核网格员报告]
            D1 --> D12[科室负责人审核城管科报告]
            D11 --> D13[汇总到街道工单管理员]
            D12 --> D13

            D2 --> D21[业务科室负责人审核]
            D21 --> D22[汇总到区住建局工单管理员]

            D3 --> D31[业务科室负责人审核]
            D31 --> D32[汇总到区生态环境局工单管理员]

            D13 --> E[阶段三：协办部门向主办部门提交确认]
            D22 --> E
            D32 --> E

            E --> E1[区住建局提交协办完成确认]
            E --> E2[区生态环境局提交协办完成确认]
            E1 --> E3[主办部门收到协办部门1确认]
    E2 --> E4[主办部门收到协办部门2确认]

            E3 --> F[阶段四：主办部门总办结]
            E4 --> F
            F --> F1[街道工单管理员确认：<br/>1.自身内部任务已完成<br/>2.所有协办部门已确认完成]
            F1 --> F2[汇总所有信息形成最终办结报告]
            F2 --> F3[主办部门点击办结<br/>工单状态变为待审核]

            F3 --> G[阶段五：派单方最终审核]
            G --> G1[区12345分中心接收完整办结报告]
            G1 --> G2{派单方审核判断}

            G2 -->|通过| G3[认可联合战队工作成果]
            G2 -->|退回| G4[退回主办部门重新协调<br/>注明具体问题]

            G3 --> G5[向市级平台提交反馈<br/>工单进入待回访]
            G4 --> F[要求重新协调处理]

            classDef execution fill:#e8f5e8,stroke:#2ecc71,stroke-width:2px
            classDef review fill:#fff3cd,stroke:#ffc107,stroke-width:2px
            classDef collaborative fill:#fdeaea,stroke:#e74c3c,stroke-width:2px
            classDef approval fill:#d5f4e6,stroke:#27ae60,stroke-width:2px
            classDef rejection fill:#ffebee,stroke:#f44336,stroke-width:2px
            classDef nextstage fill:#e1f5fe,stroke:#0288d1,stroke-width:2px

            class C11,C12,C13,C14,C21,C22,C31,C32 execution
            class D11,D12,D13,D21,D22,D31,D32 review
            class E1,E2,E3,E4,F1,F2,F3 collaborative
            class G3 approval
            class G4 rejection
            class G5 nextstage
    `
};
