/**
 * 主要功能函数文件
 * 包含页面交互、图表渲染等核心功能
 */

/**
 * 渲染图表的函数
 * @param {string} stage - 阶段标识
 */
async function renderDiagram(stage) {
    if (!diagrams[stage]) {
        console.warn(`未找到阶段 ${stage} 的图表定义`);
        return;
    }
    
    const diagramContainer = document.getElementById(APP_CONFIG.CONTAINERS.DIAGRAM);
    
    try {
        // 清空容器
        diagramContainer.innerHTML = '';
        
        // 生成唯一ID
        const diagramId = 'mermaid-' + Date.now();
        
        // 使用mermaid.render方法
        const { svg } = await mermaid.render(diagramId, diagrams[stage]);
        
        // 插入SVG并优化样式
        diagramContainer.innerHTML = svg;
        
        // 获取SVG元素并优化其样式
        const svgElement = diagramContainer.querySelector('svg');
        if (svgElement) {
            optimizeSVGDisplay(svgElement);

            // 重置缩放控制
            if (typeof resetDiagramControls === 'function') {
                resetDiagramControls();
            }

            // 延迟一点时间让SVG完全渲染后再应用控制
            setTimeout(() => {
                if (typeof initDiagramControls === 'function') {
                    initDiagramControls();
                }
            }, 100);
        }
        
    } catch (error) {
        console.error('Mermaid渲染错误:', error);
        // 降级处理：直接显示文本
        diagramContainer.innerHTML = `
            <pre style="background: #f8f9fa; padding: 20px; border-radius: 5px; overflow-x: auto;">
                ${diagrams[stage]}
            </pre>
        `;
    }
}

/**
 * 优化SVG显示样式
 * @param {SVGElement} svgElement - SVG元素
 */
function optimizeSVGDisplay(svgElement) {
    svgElement.style.width = '100%';
    svgElement.style.height = 'auto';
    svgElement.style.maxWidth = '100%';
    svgElement.removeAttribute('width');
    
    // 设置viewBox以确保响应式缩放
    const currentViewBox = svgElement.getAttribute('viewBox');
    if (!currentViewBox) {
        svgElement.setAttribute('viewBox', '0 0 800 600');
    }
    svgElement.setAttribute('preserveAspectRatio', 'xMidYMid meet');
}

/**
 * 显示阶段详情
 * @param {string} stage - 阶段标识
 */
function showStageDetails(stage) {
    const detailsContainer = document.getElementById(APP_CONFIG.CONTAINERS.STAGE_DETAILS);
    
    if (stageDetails[stage]) {
        detailsContainer.innerHTML = `
            <h3>${stageDetails[stage].title}</h3>
            ${stageDetails[stage].content}
        `;
        detailsContainer.style.display = 'block';
    } else {
        detailsContainer.style.display = 'none';
    }
}

/**
 * 更新按钮状态
 * @param {HTMLElement} activeButton - 当前激活的按钮
 */
function updateButtonStates(activeButton) {
    // 移除所有按钮的active类
    document.querySelectorAll('.stage-btn, .collab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 为当前按钮添加active类
    if (activeButton) {
        activeButton.classList.add('active');
    }
}

/**
 * 显示指定阶段
 * @param {string} stage - 阶段标识
 * @param {HTMLElement} buttonElement - 触发的按钮元素
 */
async function showStage(stage, buttonElement) {
    try {
        // 更新按钮状态
        updateButtonStates(buttonElement);
        
        // 渲染图表
        await renderDiagram(stage);
        
        // 显示阶段详情
        showStageDetails(stage);
        
        // 平滑滚动到图表区域
        const diagramContainer = document.querySelector('.mermaid-container');
        if (diagramContainer) {
            diagramContainer.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
        
    } catch (error) {
        console.error('显示阶段时发生错误:', error);
        
        // 错误处理：显示错误信息
        const diagramContainer = document.getElementById(APP_CONFIG.CONTAINERS.DIAGRAM);
        diagramContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #e74c3c;">
                <h3>加载失败</h3>
                <p>无法加载阶段 "${stage}" 的内容，请刷新页面重试。</p>
            </div>
        `;
    }
}

/**
 * 页面初始化函数
 */
function initializePage() {
    try {
        // 显示默认阶段
        const defaultButton = document.querySelector('.stage-btn.active');
        showStage(APP_CONFIG.DEFAULT_STAGE, defaultButton);
        
        console.log('页面初始化完成');
    } catch (error) {
        console.error('页面初始化失败:', error);
    }
}

/**
 * 错误处理函数
 * @param {Error} error - 错误对象
 * @param {string} context - 错误上下文
 */
function handleError(error, context = '未知操作') {
    console.error(`${context}时发生错误:`, error);
    
    // 可以在这里添加用户友好的错误提示
    // 例如显示toast消息或错误弹窗
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待Mermaid加载完成后再初始化页面
    if (typeof mermaid !== 'undefined') {
        initializePage();
    } else {
        // 如果Mermaid还未加载，等待一段时间后重试
        setTimeout(initializePage, 1000);
    }
});

// 全局错误处理
window.addEventListener('error', function(event) {
    handleError(event.error, '全局');
});

// 导出函数供全局使用
window.showStage = showStage;
window.renderDiagram = renderDiagram;
