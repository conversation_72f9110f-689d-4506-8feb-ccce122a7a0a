/**
 * Mermaid配置文件
 * 包含Mermaid的初始化配置和主题设置
 */

// 初始化Mermaid
mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis',
        padding: 20,
        nodeSpacing: 80,
        rankSpacing: 100
    },
    themeVariables: {
        primaryColor: '#3498db',
        primaryTextColor: '#2c3e50',
        primaryBorderColor: '#2980b9',
        lineColor: '#34495e',
        sectionBkgColor: '#ecf0f1',
        altSectionBkgColor: '#bdc3c7',
        gridColor: '#95a5a6',
        tertiaryColor: '#f8f9fa'
    }
});

/**
 * 应用配置常量
 */
const APP_CONFIG = {
    // 阶段映射
    STAGE_MAP: {
        'overview': 0,
        'stage1': 1,
        'stage2': 2,
        'stage3': 3,
        'stage4': 4,
        'stage5': 5
    },
    
    // 默认阶段
    DEFAULT_STAGE: 'overview',
    
    // 动画配置
    ANIMATION: {
        BUTTON_TRANSITION: '0.3s ease',
        DIAGRAM_FADE_IN: '0.5s ease-in-out'
    },
    
    // 容器配置
    CONTAINERS: {
        DIAGRAM: 'mermaid-diagram',
        STAGE_DETAILS: 'stage-details'
    }
};
