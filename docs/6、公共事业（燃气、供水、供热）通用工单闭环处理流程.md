
---

### **公共事业（燃气/供水/供热）通用工单闭环处理流程**

#### **第一部分：角色与层级定义**

我们将公共事业公司的典型组织架构映射到系统中：

*   **客服中心 (96XXX热线)**: 对应图中的【客服中心】。是所有服务的统一入口，负责受理客户请求、创建工单、提供咨询并进行一级分派。角色是“**客服人员**”。
*   **分公司/管理处**: 对应图中的【分公司、部门】。负责管理特定片区的运营，是二级调度中心和管理节点。角色是“**管理者/主管**”。
*   **区所/服务站**: 对应图中的【区所客服】。是更下一级的管理和调度单位，负责管辖片区内所有工单的接收与再分配。角色是“**管理者/主管**”。
*   **营业站厅/维修中心**: 对应图中的【营业站厅/维修中心】。这是最终的执行单位，包含了负责具体上门服务的维修工程师、安检员、抄表员等。角色是“**工单处理人**”。
*   **客户**: 服务的最终对象。
*   **质检/回访部门**: 独立的服务质量监督部门，角色是“**回访员**”。

---

#### **第二部分：工单运行流程详解**

##### **场景一：标准报修/安装流程 (逐级下派)**

1.  **受理与创建 (客服中心)**:
    *   客户拨打热线电话，报修“燃气灶无法点火”。
    *   客服人员在系统中**新建工单**，录入客户地址、联系方式、故障描述，并打上“**户内维修**”标签。
    *   根据客户地址，客服将工单**下派**给负责该区域的**【分公司/管理处】**。工单进入【待处理】状态，SLA开始计时。

2.  **二级调度 (分公司/管理处)**:
    *   分公司调度员在其“我的工单”中接收工单。
    *   他/她进一步将工单**下派**给管辖范围内的**【区所/服务站】**。

3.  **三级调度 (区所/服务站)**:
    *   服务站站长接收工单。他/她会查看当前所有维修工程师的忙闲状态（或系统根据**智能派单**推荐），将工单**下派**给具体的**【维修工程师】**。

4.  **上门处理 (维修工程师)**:
    *   维修工程师在手机端APP上接收工单，状态变为【处理中】。
    *   他/她通过“**联系客户**”功能，与客户确认上门时间。
    *   到达现场后，进行检查维修。如需更换零件，可通过APP查询备件信息并发起领用流程。
    *   维修完成后，请客户在APP上进行**电子签名**确认。
    *   工程师在系统中填写维修情况、使用了何种备件，然后点击“**办结**”。

5.  **逐级反馈与审核**:
    *   工程师办结后，工单进入【待审核】状态，并逐级返回。
    *   **服务站站长**审核维修记录和客户签名，确认无误后“审核通过”。
    *   **分公司**对服务站的已审核工单进行抽查或全部审核。

6.  **回访与关闭**:
    *   工单通过内部审核后，进入【待回访】状态。
    *   **质检/回访部门**联系客户，进行满意度回访。客户满意，则**【关闭工单】**；不满意，则**【重启工单】**，返回处理人返工。

##### **场景二：紧急抢险流程 (跨级下派)**

1.  **紧急受理 (客服中心)**:
    *   客户紧急来电，报告“闻到浓重燃气味，疑似泄漏”。
    *   客服人员立刻判断为**最高优先级（P0级）**，创建工单并打上“**紧急抢险**”标签。
    *   **关键操作**: 客服人员**跳过**分公司和区所，直接执行“**跨级下派**”，将工单指派给负责该片区的**【维修中心/抢险队】**，并可能同时**抄送**给分公司领导和安全部门。
    *   系统的SLA会以“分钟”为单位开始倒计时，并触发**超时预警**机制。

2.  **紧急出动与处理**:
    *   抢险队人员的手机会收到强制性的、高优先级的消息提醒。
    *   抢险队立即出动，并在系统中实时更新状态，如“已出发”、“已到场”。管理者可以通过**地图轨迹**功能监控其位置。
    *   现场处理完毕，排除险情后，在系统中“**办结**”工单，并附上详细的现场报告。

3.  **事后审核与关闭**:
    *   由于是紧急事件，流程可能会简化。办结后可能直接流转至**安全管理部门**和**分公司领导**进行审核。
    *   审核通过后，可根据情况决定是否需要回访，然后最终关闭。

##### **场景三：新用户开户/通气流程 (多部门协同)**

1.  **受理与创建 (营业站厅/客服中心)**:
    *   新用户在营业厅申请开户。营业厅工作人员创建工单。
    *   这是一个复杂流程，客服或工作人员在创建时就设定好**主协办**模式：
        *   **主办方**: **工程/安装部门** (负责现场勘探、管道施工)。
        *   **协办方1**: **设计部门** (负责出具施工图纸)。
        *   **协办方2**: **安检部门** (负责施工后和通气前的安全检查)。
        *   **协办方3**: **财务/营业部门** (负责费用收取)。

2.  **并行与串行处理**:
    *   工单创建后，各部门会根据预设的流程依赖关系，按顺序或并行地接收到自己的任务。
    *   例如：设计部门先完成图纸 -> 工程部门根据图纸施工 -> 施工完毕后，安检部门和财务部门可以并行地进行安检和收费 -> 所有环节都完成后，由工程部门执行最后的通气操作。
    *   每个部门都在同一个工单下**补记**自己的工作内容，信息共享。

3.  **办结与关闭**:
    *   所有子任务完成后，由**主办方（工程/安装部门）**最终确认并“**办结**”主工单，然后进入回访和关闭流程。