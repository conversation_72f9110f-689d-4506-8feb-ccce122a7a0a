
---

### **第一部分：组织架构与层级关系**

首先，我们必须清晰地定义这个“矩阵式”的层级与权责关系：

*   **第一层（市级）**: `市级12345中心` (总枢纽)
*   **第二层（区级）**: `区级12345分中心` (二级枢纽)
*   **第三层（区级下属单位 - 平级）**:
    *   **属地管理主体**: `街/镇` (街道办事处/镇政府)
    *   **职能管理主体**: `区级职能部门` (如区城管局、区住建局)
    *   *这两个单位平级，都直接接收来自“区级12345分中心”的派单。*
*   **第四层（第三层下属单位 - 平级）**:
    *   `街/镇` 下属的 `社区/村`
    *   `街/镇` 下属的 `街镇内设职能部门`
    *   `区级职能部门` 下属的 `业务科室`
    *   *这三个单位在功能层级上被视为平级，是任务的二级承接和三级分派单位。*
*   **第五层（第四层下属单位 - 职能等同）**:
    *   `街镇内设职能部门` 下属的 `业务科室`
    *   *这个“业务科室”与第四层的“社区/村”平级。*
*   **第六层（最终执行者 - 职能等同）**:
    *   `社区/村` 下属的 `网格员`
    *   `区/镇级业务科室` 下属的 `工作人员`
    *   *这两类人员是深入一线、解决问题的最终执行力量。*

---

### **第二部分：工单运行流程（双轨并行、条块结合）**

现在，工单的流转不再是单一的“下楼梯”，而是会在第二层出现**路径选择**，进入不同的轨道。

#### **场景A：属地管理路线（适用于大部分民生、社区类问题）**

此路线的核心是“网格化管理”，问题在地理区域内闭环。

1.  **市级 -> 区级总口**: 市民反映“xx区xx街道xx小区树木倒伏影响通行”。市级12345中心派单至**【xx区12345分中心】**。
2.  **区级总口 -> 街/镇 (路径选择)**: 区级分中心判断此事为典型的社区管理事务，将工单派发至**【xx街道办事处】**。
3.  **街/镇 -> 社区/村**: 街道办事处接收后，判断为具体小区事务，将工单下派至**【xx社区居委会】**。
4.  **社区/村 -> 网格员**: 社区将任务指派给负责该小区的**【网格员】**。
5.  **执行与办结**: 网格员联系或协调物业/绿化队处理树木，拍照上报后“**办结**”。
6.  **审核反馈**: 流程**严格原路返回**：`网格员` -> `社区`审核 -> `街道`审核 -> `区级分中心`审核 -> 反馈至`市级平台` -> 进入【待回访】。

#### **场景B：职能管理路线（适用于专业性强、跨区域的行业问题）**

此路线的核心是“专业对口”，由专业部门垂直处理。

1.  **市级 -> 区级总口**: 市民投诉“xx区内某家食品加工厂卫生不达标”。市级12345中心派单至**【xx区12345分中心】**。
2.  **区级总口 -> 区职能部门 (路径选择)**: 区级分中心判断此事为食品安全问题，事权属于市场监管，将工单派发至**【区市场监督管理局】**。
3.  **区职能部门 -> 业务科室**: 区市场监管局接收后，其内部工单管理员将任务指派给**【食品安全监督科】**。
4.  **业务科室 -> 工作人员**: 科室负责人将现场检查任务指派给具体的**【执法工作人员】**。
5.  **执行与办结**: 执法工作人员前往工厂进行突击检查，开具整改通知书，拍照取证后“**办结**”。
6.  **审核反馈**: 流程**严格原路返回**：`工作人员` -> `业务科室`审核 -> `区市场监管局`审核 -> `区级分中心`审核 -> 反馈至`市级平台` -> 进入【待回访】。

#### **场景C：混合路线（条块结合的复杂场景）**

1.  **市级 -> 区级总口 -> 街/镇**: 如场景A，一个“违章建筑”的工单被派到了**【xx街道办事处】**。
2.  **街/镇的二次分派**: 街道办事处接收后，发现这个违建需要专业的执法力量。于是，街道的工单管理员将任务派发给了**【本街道的城管科】**（街镇内设职能部门）。
3.  **街镇职能部门 -> 业务科室 -> 工作人员**: 街道城管科再将任务指派给具体的执法工作人员去现场处理。
4.  **审核反馈**: `工作人员` -> `街道城管科`审核 -> `街道办事处`审核 -> `区级分中心`审核 -> `市级平台`。

---

### **场景D：跨属地与职能管理的主协办协同场景**

**工单诉求**: 一位市民通过12345热线投诉：“我们是xx区xx街道yy社区的居民，我们小区旁边有一块闲置空地，最近突然变成了‘半夜施工’的建筑垃圾倾倒场，噪音巨大，尘土飞扬，严重影响生活，而且我们怀疑这些垃圾来源不明，可能存在环境污染风险。”

**问题分析**:
*   **属地责任**: 事件发生在“xx街道yy社区”辖区内，街道和社区对属地范围内的环境秩序和居民稳定负有直接管理责任（**块**）。
*   **职能责任**: “建筑垃圾”的源头追溯、运输管理和最终处置，属于**区住建局（或城管局，具体看城市职能划分）**的行业监管范畴；“环境污染风险”则涉及**区生态环境局**的专业职能（**条**）。
*   **协同必要性**: 单靠街道（块），他们有管理权但可能缺乏执法权和专业检测能力；单靠区级部门（条），他们有执法权但可能不了解现场具体情况，需要属地配合。因此，必须启动主协办协同。

---

### **工单运行流程详解 (场景D)**

1.  **市级 -> 区级总口**: 市级12345中心接收投诉后，根据地域，将工单派发至**【xx区12345分中心】**。

2.  **区级总口构建主协办协同结构 (核心步骤)**:
    *   区级分中心的工单分派专家看到这个复杂诉求后，判断需要“条块结合”处理。
    *   **确定主办方（块-属地）**: 考虑到现场管理、群众安抚和初步处置需要属地力量快速介入，将**【xx街道办事处】**设为此工单的**主办方**。主办方负责牵头协调，是解决问题的第一责任人。
    *   **添加协办方（条-职能）**: 同时，将两个需要提供专业执法和检测支持的区级部门添加为**协办方**:
        *   **协办方1: 区住建局** - 负责调查建筑垃圾的源头，追查违规运输车辆，并对施工单位进行执法。
        *   **协办方2: 区生态环境局** - 负责对现场进行空气和土壤采样，评估环境污染风险，并根据结果进行专业处置。
    *   工单被一次性派发给**一个主办方（街道）**和**两个协办方（区住建局、区生态环境局）**。

3.  **并行处理与信息互通**:
    *   **主办方-街道办事处**:
        *   立即将任务下派给**【yy社区】**和**【街道城管科】**。
        *   **社区网格员**迅速到达现场，安抚居民情绪，拍照取证，设立临时警示标志，并将现场情况实时**补记**到工单中。
        *   **街道城管科**的工作人员开始对现场进行24小时巡查，阻止新的垃圾倾倒行为。
    *   **协办方1-区住建局**:
        *   接收到协办任务后，其下属的**【建管科】**工作人员，根据街道上传的现场照片和位置信息，开始通过调阅监控、走访排查等方式，追查违规倾倒的施工单位和车辆。
        *   所有调查进展和线索，都**补记**到工单中，供所有参与方查看。
    *   **协办方2-区生态环境局**:
        *   其下属的**【监测科】**工作人员携带专业设备到达现场，进行采样检测。
        *   检测结果出来后，形成一份专业的评估报告，并作为附件上传到工单中。

4.  **联合执法与问题解决**:
    *   区住建局成功锁定了违规施工单位。
    *   主办方（街道）组织召开一个由**街道、区住建局、区生态环境局**三方参与的现场协调会。
    *   **区住建局**对违规单位开具罚单，责令其清理已倾倒的垃圾。
    *   **区生态环境局**根据检测报告，要求施工单位进行必要的环境修复。
    *   **街道**负责监督清理和修复工作的全过程，并做好周边居民的沟通解释工作。

5.  **汇总办结**:
    *   所有清理和修复工作完成，现场恢复原状。各协办方在工单中更新自己的任务状态为“完成”。
    *   **主办方（街道办事处）**在确认所有问题均已解决、各方均已完成工作后，汇总所有处理情况，形成最终的办结报告，然后点击“**办结**”。

6.  **审核与回访**:
    *   工单进入**逐级审核**流程（`街道` -> `区级分中心` -> `市级平台`），最终由**市级回访中心**进行回访并关闭。