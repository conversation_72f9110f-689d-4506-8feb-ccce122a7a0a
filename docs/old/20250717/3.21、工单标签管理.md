
---

### **“工单标签管理”页面内容详解**

#### **一、 页面核心目标**

1.  **统一标签词库**: 建立一个全公司统一的、规范的标签库，避免员工随意创建拼写错误、语义重复的标签。
2.  **管理标签生命周期**: 创建、编辑、合并和归档（或删除）标签。
3.  **理解标签使用情况**: 查看每个标签被多少工单使用，了解其热度和重要性。
4.  **赋能自动化与分析**: 为自动化规则和报表分析提供稳定、干净的分类维度。

---

### **二、 页面内容与布局**

这个页面通常设计得比较简洁，核心是一个功能丰富的列表，辅以创建和编辑功能。

#### **1. 页面顶部操作区**

*   **`[ + 新建标签 ]`** 按钮: 点击后弹出一个小窗口用于创建新标签。
*   **搜索框**: 按标签名称进行实时搜索。
*   **筛选器 (可选)**:
    *   `按标签组筛选`: 如果系统支持标签分组，可以按组筛选。
    *   `按状态筛选`: 筛选“已激活”或“已归档”的标签。

#### **2. 新建/编辑标签弹窗**

点击“新建标签”或列表中的“编辑”后，会出现一个配置弹窗。

*   **标签名称 (Tag Name)**: [必填] 输入标签的文字，如“功能BUG”、“服务态度表扬”。系统会检查是否重名。
*   **标签颜色 (Tag Color)**: **[核心功能]** 提供一个颜色选择器，让管理员为标签设定一个专属颜色。这使得标签在工单详情页和列表中展示时非常直观和醒目。
*   **标签描述 (Description)**: 解释这个标签的含义和使用场景，帮助员工正确使用。例如，“【功能BUG】标签仅在确认问题为产品缺陷时使用”。
*   **标签组 (Tag Group) (可选，高级功能)**:
    *   一个下拉菜单或输入框，允许将标签归类到某个组。例如，可以创建“问题类型”、“客户情绪”、“解决方案”等标签组。
    *   这对于管理大量标签非常有帮助。
*   **使用限制 (Usage Restrictions) (可选，高级功能)**:
    *   `仅管理员可添加`: 勾选后，普通员工只能看到和筛选这个标签，但不能主动为工单添加此标签。适用于一些需要严格控制的分类标签。
    *   `应用于特定工单模板`: 可以限制此标签只能在某些特定的工单模板上使用。

#### **3. 标签列表区**

以表格（Table）或卡片列表（Card List）的形式展示所有已创建的标签。

*   **列表表头 (Table Header)**:
    *   `标签预览 (Tag Preview)`: **[核心字段]** 直接展示带有背景颜色的标签本身，非常直观。
    *   `标签名称`:
    *   `标签描述`:
    *   `标签组`:
    *   `**使用次数 (Usage Count)**`: **[核心字段]** 显示当前有多少个工单使用了这个标签。这个数字是可点击的，点击后会跳转到“工单查询”页面，并自动筛选出所有使用该标签的工单。
    *   `创建人/时间`:
    *   `操作`:

*   **行末操作按钮 (Row Actions)**:
    *   **`[ 编辑 ]`**: 打开编辑弹窗。
    *   **`[ 合并 (Merge) ]`**: **[非常重要的管理功能]**
        *   场景：当发现系统中存在两个语义相近的标签时（如“BUG”和“产品缺陷”），管理员可以执行合并操作。
        *   流程：点击后，会要求选择一个“目标标签”。确认后，所有使用了当前标签的工单，都会被自动更新为使用“目标标签”，然后当前标签被删除。
        *   这确保了标签体系的整洁和数据统计的准确性。
    *   **`[ 归档/停用 (Archive) ]`**:
        *   对于一些过时或不再使用的标签，不建议直接删除（因为历史工单还在使用）。更好的做法是“归档”。
        *   归档后的标签将不再出现在新建/编辑工单时的可选列表中，但历史数据和报表仍然可以按此标签进行筛选。
    *   **`[ 删除 (Delete) ]`**: 仅对“使用次数”为0的标签提供此操作，防止破坏历史数据。

---