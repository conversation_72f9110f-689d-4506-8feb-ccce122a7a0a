
---

### **“意图识别与分类”页面内容详解**

#### **一、 页面核心目标**

1.  **定义业务意图**: 建立一个与企业业务场景紧密相关的、标准化的意图库。
2.  **训练识别模型**: 通过提供大量的样本语句（训练语料），让AI模型学会将用户的自然语言描述映射到正确的意图上。
3.  **配置自动化联动**: 将识别出的意图与后续的自动化动作（如设置工单类型、优先级、指派团队）进行绑定。
4.  **管理与优化模型**: 持续地对模型进行测试、数据标注和再训练，提升识别准确率。

---

### **二、 页面内容与布局**

页面通常采用**“意图库管理 + 意图详情与训练”**的左右两栏或上下两部分布局。

#### **区域一：意图库管理 (Intent Library Management)**

这是一个列表，用于管理所有已定义的业务意图。

*   **页面顶部**: 一个 `[ + 新建意图 ]` 按钮。
*   **搜索框**: 按意图名称搜索。
*   **意图列表**:
    *   **表头**: `意图名称`, `关联的工单模板`, `训练样本数`, `模型准确率`, `状态 (启用/停用)`, `操作`。
    *   `意图名称`: 如“重置密码”、“查询账单”、“报告Bug”、“咨询产品价格”。
    *   `关联的工单模板`: 显示此意图最常关联或被设置为自动填充的工单模板。
    *   `训练样本数`: 显示用于训练此意图的语料数量，是衡量模型可靠性的一个指标。
    *   `模型准确率`: **[核心指标]** 显示在最近一次评测中，模型识别此意图的准确率（如95%）。
    *   **行末操作**: `[ 编辑与训练 ]`, `[ 测试 ]`, `[ 停用 ]`, `[ 删除 ]`。

---

#### **区域二：意图构建器/编辑器 (Intent Builder/Editor)**

点击“新建意图”或列表中的“编辑与训练”后，会进入这个核心的功能区，通常由多个标签页或配置块组成。

**1. 基本信息**

*   **意图名称**: [必填] 如 `intent_reset_password` (系统名) 和 “重置密码” (显示名)。
*   **意图描述**: 解释此意图代表的业务场景。

**2. 训练语料 (Training Utterances) - [模型学习的“教材”]**

这是**最重要的区域**，用于“喂养”AI模型。

*   **输入框**: `[ 添加一条新的表达方式... ]`，管理员可以在这里不断地添加用户可能会说的、表达同一个意图的各种句子。
*   **语料列表**:
    *   “我忘了密码”
    *   “我的账号登不上了”
    *   “怎么修改登录密码？”
    *   “密码不对，进不去系统”
    *   “请帮我重设一下password”
    *   ... (至少需要20-30条，越多越好)
*   **批量导入**: 提供一个按钮，允许管理员通过上传TXT或CSV文件，一次性导入大量的训练语料。
*   **实体识别 (Entity Recognition) - [高级功能]**:
    *   在录入语料时，可以高亮某些词语，并将其标注为“**实体**”。
    *   例如，在“我要查询订单**12345**的物流状态”这句话中，可以将“**12345**”这个词标注为一个名为“**订单号**”的实体。
    *   **价值**: 这让AI不仅知道用户想“查订单”，还知道他想查的“具体是哪个订单”，可以将这个提取出的实体值用于后续的自动化操作。

**3. 自动化联动配置 (Automation Linking)**

这一步定义了**“当识别出此意图后，系统应该自动做什么”**。

*   **自动填充工单字段**:
    *   `如果意图是【重置密码】`, `则自动设置`:
        *   `工单模板`: 为 `IT服务请求`
        *   `优先级`: 为 `高`
        *   `工单标签`: 添加 `密码问题`
*   **自动指派**:
    *   `如果意图是【重置密码】`, `则自动指派给`: `IT身份认证组`
*   **触发特定回复 (可选)**:
    *   `如果意图是【重置密码】`, `则自动回复`: (调用一个自定义回复模板) “您好，我们已收到您的密码重置请求，请留意后续邮件中的重置链接。”

**4. 模型操作与测试**

*   **操作按钮**:
    *   **`[ 训练模型 ]`**: 每当添加或修改了大量训练语料后，需要点击此按钮。系统会在后台用最新的数据重新训练意图识别模型。
*   **快速测试区**:
    *   一个输入框：“**在此测试您的模型...**”
    *   管理员输入一句测试的话，如“我密码好像有问题”，然后点击“测试”。
    *   系统会立即返回识别结果，如：“**识别意图：重置密码 (置信度: 98%)**”。
    *   这提供了一个快速验证模型效果的便捷方式。

---