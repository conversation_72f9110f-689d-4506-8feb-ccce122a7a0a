
---

### **“知识库文章推荐设置”页面内容详解**

#### **一、 页面核心目标**

1.  **定义推荐模型**: 配置系统如何理解工单内容，并将其与最相关的知识库文章进行匹配。
2.  **设定推荐场景**: 决定在工单生命周期的哪些环节、向哪些角色展示知识库推荐。
3.  **优化推荐体验**: 配置推荐结果的展示方式，并提供便捷的“引用”或“发送给客户”等操作。
4.  **建立反馈闭环**: 收集用户对推荐结果的评价，用于持续优化推荐算法。

---

### **二、 页面内容与布局**

页面同样采用**多标签页 (Tabs)** 的布局，将各项配置进行逻辑分区。

#### **标签页一：推荐触发与展示 (Trigger & Display)**

这部分负责配置知识库推荐功能在前端的“表现”。

*   **触发场景配置**:
    *   一组开关，用于启用或停用在不同场景下的推荐功能。
    *   `[ ✓ ]` **在“新建工单”页面**: 当客服输入`标题`和`描述`时，实时推荐。**（核心场景：客服自助解决）**
    *   `[ ✓ ]` **在“工单详情”页面**: 处理人打开一个工单详情时，在侧边栏展示推荐文章。**（核心场景：赋能处理人）**
    *   `[   ]` **在客户自服务门户 (如果存在)**: 当客户在自助门户中输入问题时，向其推荐相关文章，引导自助解决。

*   **推荐结果配置**:
    *   **最大推荐数量**: 输入框，设置一次最多推荐 `3` 篇知识库文章。
    *   **推荐卡片显示内容**:
        *   一个多选勾选列表，让管理员选择推荐出的文章卡片上应该显示哪些信息。
        *   `[✓] 文章标题`
        *   `[✓] 文章摘要 (正文前50字)`
        *   `[✓] 关联标签`
        *   `[ ] 创建者/更新时间`
        *   `[ ] 浏览次数/点赞数`
    *   **快捷操作配置**:
        *   配置当用户点击推荐的文章后，可以执行哪些快捷操作。
        *   `[ ✓ ]` **`[ 引用到工单 ]`**: 将文章内容或链接一键插入到工单的“解决方案”或“补记”中。
        *   `[ ✓ ]` **`[ 发送给客户 ]`**: 将文章链接或内容格式化后，一键发送给工单关联的客户。
        *   `[   ]` **`[ 创建关联 ]`**: 将此文章与当前工单正式关联起来。

#### **标签页二：相似度算法配置 (Similarity Algorithm) - [核心技术配置]**

这部分定义了“如何将工单与知识库文章匹配”。

*   **知识库数据源配置**:
    *   `用于推荐的文章分类`: 树形多选框，可以选择只从某些特定的知识库分类中进行推荐。
    *   `排除特定标签的文章`: 可以排除掉一些不适合推荐的（如“内部流程”）文章。

*   **特征字段与权重**:
    *   一个列表，让管理员配置在进行“工单-文章”匹配时，双方的哪些字段参与计算以及各自的权重。
    *   **源端 (工单) 字段**:
        *   `[✓] 工单标题`: 权重 `80`
        *   `[✓] 工单描述`: 权重 `60`
        *   `[✓] 工单标签`: 权重 `50`
    *   **目标端 (知识库) 字段**:
        *   `[✓] 文章标题`: 权重 `90`
        *   `[✓] 文章正文`: 权重 `50`
        *   `[✓] 文章标签/关键词`: 权重 `70`
    *   **算法选择**: 与相似工单推荐类似，可以选择 `TF-IDF` 或更先进的 `Bert/Sentence-Transformers` 等语义匹配模型。

*   **相似度与推荐阈值**:
    *   `相似度得分阈值`: 如 `0.70`。只有匹配得分高于此值的文章才会被推荐。
    *   `推荐结果微调 (Result Boosting)`: **[高级功能]**
        *   允许添加一些规则来调整最终推荐列表的排序。
        *   **示例规则**:
            *   如果文章的 `[浏览次数]` `大于` `1000`，则其最终得分 `乘以 1.1`。
            *   如果文章的 `[最近更新时间]` `在30天内`，则其最终得分 `乘以 1.2`。
        *   **价值**: 这让系统不仅推荐“相关”的文章，还会优先推荐“**更受欢迎**”和“**更新鲜**”的文章。

#### **标签页三：反馈与优化闭环 (Feedback & Optimization Loop)**

这部分是让推荐系统能够自我进化的关键。

*   **用户反馈功能**:
    *   `[ ✓ ]` **启用推荐结果反馈**: 在前端推荐列表的每篇文章旁边，显示一组简单的反馈按钮，如 `[ 👍 有用 ]` 和 `[ 👎 无用 ]`。
*   **反馈数据利用**:
    *   `[ ✓ ]` **将用户反馈作为再排序信号**: 开启后，被“点赞”多的文章，在未来的推荐中会获得更高的权重。被“点踩”多的文章，则会降低权重。
*   **关联关系学习**:
    *   `[ ✓ ]` **学习手动关联数据**: 开启后，系统会自动学习处理人员“手动将某工单与某文章关联”的行为。这种人工确认的“强关联”数据，将作为高质量的训练样本，极大地提升未来推荐的准确性。
    *   例如，如果多位工程师在处理“打印机脱机”的工单时，都手动关联了“KB00123：重置打印机服务”这篇文章，那么未来系统在遇到“打印机”相关的工单时，会极高概率地首要推荐KB00123。

---