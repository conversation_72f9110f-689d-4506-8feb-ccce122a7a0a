
---

### **“新建工单”页面内容详解**

#### **一、 页面核心目标**

1.  **快速选择业务场景**: 用户能迅速确定这是什么类型的工单。
2.  **结构化信息录入**: 引导用户填写所有必要信息，避免遗漏。
3.  **关联正确的人与物**: 准确关联到客户、甚至具体的资产设备。
4.  **设定初始协同结构**: 在创建时就能指派主办、协办方，设定初始SLA。
5.  **提供辅助工具**: 利用模板、知识库等工具提升录入效率和准确性。

---

### **二、 页面内容与布局**

页面通常采用自上而下的表单布局，并进行逻辑分区，引导用户逐步完成。

#### **1. 模板选择区 (Template Selection)**

这是页面的**第一步**，也是最关键的一步。

*   **工单模板/类型选择器**:
    *   一个下拉菜单或一组醒目的卡片，让用户首先选择本次要创建的工单类型。例如：
        *   `IT服务请求`
        *   `客户投诉`
        *   `采购申请`
        *   `售后维修单`
    *   **核心机制**: **选择不同的模板，下方的表单内容会动态变化**。比如，选择“IT服务请求”会显示“影响的系统”、“设备编号”等字段；而选择“客户投诉”则会显示“投诉渠道”、“投诉等级”等字段。这是实现业务适应性的关键。

#### **2. 客户/发起人信息区 (Customer/Initiator Information)**

这部分用于确定“为谁服务”或“谁发起的请求”。

*   **客户/员工选择器**:
    *   一个智能搜索框。用户可以输入客户的姓名、电话、公司名称或员工的工号、姓名进行搜索。
    *   系统会自动匹配并带出客户/员工的详细信息（如联系方式、VIP等级、历史工单记录摘要等），避免重复录入。
*   **新客户/临时客户录入**: 如果是新客户，提供一个快速创建简要档案的入口。

#### **3. 工单核心信息区 (Core Work Order Details)**

这是表单的主体部分，由所选模板动态生成。

*   **工单标题 (Title)**: [必填项] 对问题的简明扼要的概括。例如，“销售部打印机无法连接网络”。
*   **问题/需求描述 (Description)**: [必填项] 一个富文本编辑器，允许用户详细描述问题现象、客户诉求、重现步骤等。支持**图片/附件上传**，这对于技术支持类工单至关重要。
*   **优先级 (Priority)**: 一个下拉菜单，供用户选择“紧急”、“高”、“中”、“低”。系统可以根据模板或关键词自动推荐一个优先级。
*   **工单标签 (Tags)**: 一个多选输入框，允许用户为工单打上多个标签，便于分类和搜索。
*   **服务级别协议 (SLA)**: 系统会根据模板和优先级，**自动计算并显示**该工单的SLA（例如，“需在4小时内响应，24小时内解决”）。通常不可手动修改，由规则驱动。
*   **预约时间 (Appointment Time)**: [特定模板可见] 一个日期时间选择器，用于需要上门服务的场景。
*   **其他自定义字段**: 所有由工单模板定义的字段都会在这里显示，例如：
    *   `影响的业务系统` (下拉菜单)
    *   `设备资产编号` (文本框)
    *   `投诉渠道` (下拉菜单)
    *   `期望的解决方案` (文本域)

#### **4. 协同与指派区 (Collaboration & Assignment)**

这部分用于设定工单的初始处理团队。

*   **主办方指派 (Assign to - Owner)**:
    *   一个智能搜索框，可以搜索并选择一个处理人或处理团队作为主要负责人。
    *   系统可能会根据模板或关键词，**自动推荐**最合适的处理人/团队。
*   **协办方添加 (Add Collaborators)**:
    *   一个多选框，允许在创建时就添加一个或多个协办方，以应对复杂问题。
*   **抄送 (CC)**:
    *   一个多选框，添加需要知晓此事，但无需处理的人员。

#### **5. 辅助与操作区 (Assistance & Actions)**

页面的底部或侧边栏，提供提升效率的工具。

*   **知识库推荐 (Knowledge Base Suggestions)**:
    *   **智能特性**: 当用户在“标题”或“描述”中输入内容时，系统会**实时分析关键词**，并在侧边栏动态推荐相关的知识库文章或历史解决方案。
    *   **价值**: 如果客服能通过知识库当场解决客户问题，甚至可以**无需创建工单**，或者能将解决方案直接附在工单中，极大提升效率。
*   **自定义回复模板 (Reply Templates)**:
    *   一个可搜索的模板列表，客服可以选择标准化的回复术语或步骤，快速填充到描述中。
*   **操作按钮 (Action Buttons)**:
    *   **`[ 创建并提交 ]`**: 保存工单并按照指派信息流转出去。
    *   **`[ 保存为草稿 ]`**: 如果信息不全，可以先保存，稍后再回来编辑。
    *   **`[ 取消 ]`**: 放弃创建。

---

---

### **评审：可补充的优化点**

#### **1. 客户信息的深度联动与洞察前置 (Deep Customer Context & Proactive Insights)**

*   **当前设计**: 在选择客户后，能带出其基本信息和历史工单摘要。
*   **缺失点**: 信息呈现是被动的，没有主动为客服提供决策支持。
*   **优化建议**:
    *   **`[ 动态客户健康度看板 (Dynamic Customer Health Dashboard) ]`**: 当客服选中一个客户后，页面侧边栏或一个醒目的区域会**立即浮现一个迷你仪表盘**，显示：
        *   **SLA达成率**: 最近对该客户的服务SLA达成率是多少？
        *   **近期满意度**: 最近3次服务的平均满意度是多少？
        *   **活跃工单**: 该客户当前还有多少个未关闭的工单？
        *   **关键标签**: 该客户的历史工单中最常出现的标签是？（如“频繁崩溃”、“VIP加急”）
*   **价值**: 这让客服在创建工单的**第一秒**就能对客户的“情绪”和“背景”有一个深刻的洞察。如果发现客户近期的SLA达成率很低，客服就能采取更主动、更安抚性的沟通策略，并可能手动提升工单的优先级。这是一种**主动服务 (Proactive Service)** 的体现。

#### **2. 自动化规则的实时预览 (Real-time Preview of Automation Rules)**

*   **当前设计**: 用户填完所有信息，点击提交后，后台的自动化规则才开始执行。
*   **缺失点**: 创建者对于点击“提交”后会发生什么，是“盲盒”状态。
*   **优化建议**:
    *   **`[ 自动化流程预览 (Automation Flow Preview) ]`**: 在页面的“辅助区”，增加一个“**流程预览**”模块。当用户填写或修改了关键字段（如模板、优先级、标签）后，这个模块会**实时刷新**并用自然语言或流程图的形式告知用户：
        *   “根据当前设置，提交后：”
        *   “✓ 此工单将被**自动指派**给‘IT网络组’。”
        *   “✓ 系统将**自动抄送**给‘张三(总监)’。”
        *   “✓ SLA策略‘高优IT服务’将被应用。”
*   **价值**: 极大地提升了流程的**透明度**。客服可以确认自己的操作会触发预期的自动化流程，避免了因信息填写错误导致的流程走向偏差，增强了对系统的信任感。

#### **3. 表单填写的智能引导与校验 (Intelligent Form Guidance & Validation)**

*   **当前设计**: 提供了必填项校验。
*   **缺失点**: 引导性和校验的智能化程度可以更高。
*   **优化建议**:
    *   **`[ 基于内容的字段推荐 ]`**: 当用户在“描述”中输入了关键词，如“无法打印”，系统除了推荐知识库文章，还可以**高亮或自动填充**“影响的系统”字段为“打印机服务”。
    *   **`[ 动态条件必填 ]`**: 支持更复杂的校验逻辑。例如，“只有当‘工单类型’为‘采购申请’，且‘申请金额’大于5000元时，‘需要总监审批’这个字段才变为必填。”
*   **价值**: 让表单从一个被动的信息容器，变成一个能与用户互动、主动引导用户准确填写的**智能助手**，进一步降低了出错率。

#### **4. 对“相似历史工单”的利用 (Leveraging Similar Historical Tickets)**

*   **当前设计**: 知识库推荐是主要的智能辅助。
*   **缺失点**: 未充分利用最有价值的数据——已经解决的历史工单。
*   **优化建议**:
    *   **`[ 相似工单推荐 ]`**: 在知识库推荐旁边，增加一个“**相似历史工单**”的推荐列表。系统根据当前输入的标题和描述，找出历史上已解决的、内容相似的工单。
    *   **一键复用**: 用户点击某个相似工单后，可以**一键复制**其“解决方案”、“处理人”、“标签”等信息到当前正在创建的工单中。
*   **价值**: 这是知识库的终极形态。对于重复性问题，客服无需重新思考指派给谁、打什么标签，可以直接复用历史上最成功的处理路径，这是**对组织经验的最佳传承和利用**。

---

---

### **修正后的智能派单交互流程 (人机协同模式)**

这个区域的设计将变为一个**两步式**的、高度透明的交互过程。

**第一步：系统智能推荐与解释**

*   **主办方指派 (Assign to - Owner)**:
    *   这个输入框**始终存在且可编辑**。
    *   当客服填完了上面的核心信息（特别是模板、优先级、描述）后，**系统会自动进行一次智能派单的运算**。
    *   然后，**运算出的最优人选会自动填充到这个输入框中**。
    *   **关键设计**：在输入框的下方，会出现一个清晰的、可展开的“**智能推荐理由**”区域。

*   **智能推荐理由 (Intelligent Recommendation Rationale) - [“玻璃盒”的核心]**:
    *   这个区域会用自然语言和数据，向客服解释**为什么系统会推荐这个人**。
    *   **示例**:
        > **智能推荐**: **李四** (IT支持部)
        >
        > **[ 展开推荐理由 ▼ ]**
        >
        > *   **技能匹配度 (92%)**: 工单所需的 `[数据库]` (专家级)、`[Python]` (熟练级) 技能与李四的技能画像高度匹配。
        > *   **当前负载 (低)**: 李四当前处理中工单为 **2** (团队平均为4.5)，负载较低。
        > *   **历史经验 (相关)**: 李四在过去3个月内成功解决了 **5** 个类似的数据库连接问题。
        > *   **客户评价 (优秀)**: 李四的历史平均服务评分为 **4.9/5.0**。

**第二步：客服的监督、验证与最终决策**

*   **客服的角色**:
    *   客服不再是盲目地从0到1去选择处理人，而是变成了**AI决策的“审核员”和“监督者”**。
    *   他会快速浏览系统的推荐和理由。

*   **客服的决策选项**:
    *   **选项A：信任并确认 (Trust & Confirm)**
        *   如果客服认为系统的推荐非常合理，他什么都不用做，直接在页面底部点击“创建并提交”即可。工单将按照推荐结果指派出去。这是最高效的路径。
    *   **选项B：修正与覆盖 (Correct & Override)**
        *   如果客服基于自己的经验或掌握的额外信息（比如他知道李四马上要去开一个重要的会），认为有更合适的人选（比如王五），他可以**直接点击“主办方指派”的输入框，清除掉系统推荐的“李四”，然后搜索并选择“王五”**。
        *   此时，“智能推荐理由”区域可能会消失或提示“已手动修改”。
    *   **选项C：需要更多信息 (Need More Info)**
        *   “智能推荐理由”中可能会提供链接，例如点击“当前负载”可以看到整个团队的实时负载面板，帮助客服做出更全面的判断。

---