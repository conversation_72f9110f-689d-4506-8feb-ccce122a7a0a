
---

### **“技能与负载模型”页面内容详解**

#### **一、 页面核心目标**

1.  **定义能力标尺**: 建立一个全公司统一、标准化的技能库，用于衡量员工的能力。
2.  **构建员工画像**: 为每一位员工打上其拥有的技能标签，并量化其熟练程度，形成多维度的能力画像。
3.  **量化工作负载**: 定义如何计算员工的当前工作量，并设定其可承受的上限。
4.  **管理员工状态**: 能够识别并应用员工的实时工作状态（如在线、休假），确保派单的有效性。

---

### **二、 页面内容与布局**

这个页面通常会采用**多标签页 (Tabs)** 的布局，将“技能管理”和“负载管理”这两个既相关又独立的功能模块分开。

#### **标签页一：技能模型管理 (Skills Model Management)**

这个部分负责定义“能力”本身。

**1. 技能标签库 (Skills Tag Library)**
*   **交互界面**: 与“工单标签管理”页面非常相似。
*   **页面顶部**: 一个 `[ + 新建技能标签 ]` 按钮。
*   **搜索框**: 按技能名称搜索。
*   **技能列表**:
    *   **表头**: `技能名称`, `技能描述`, `技能组`, `关联员工数`, `操作`。
    *   `关联员工数`: 显示当前有多少员工被打上了这个技能标签，点击后可以查看具体人员列表。
    *   **行末操作**: `[ 编辑 ]`, `[ 合并 ]` (处理语义重复的技能), `[ 归档 ]`, `[ 删除 ]` (仅对无人使用的技能)。
*   **新建/编辑技能弹窗**:
    *   `技能名称`: [必填] 如“Python编程”、“网络故障排查”、“合同审核”、“客户情绪安抚”。
    *   `技能描述`: 解释该技能的具体能力范围。
    *   `技能组 (可选)`: 可以将技能分组，如“技术类”、“商务类”、“沟通类”。

**2. 员工技能配置 (Agent Skills Assignment)**
*   **交互界面**: 这是一个以“员工”为维度的配置列表。
*   **筛选器**: 按部门/团队、员工姓名进行筛选。
*   **员工技能列表**:
    *   **表头**: `员工姓名`, `部门/团队`, `**已关联技能**`, `操作`。
    *   `**已关联技能**`: **[核心字段]** 以标签云的形式，直观地展示该员工拥有的所有技能标签。
    *   **行末操作**: `[ 编辑技能 ]`。
*   **点击“编辑技能”后的弹窗/页面**:
    *   **左侧**: 显示该员工的基本信息。
    *   **右侧**: 一个**双栏选择器**或**带搜索的多选框**。
        *   让管理者可以从“技能标签库”中，为该员工勾选他所具备的技能。
        *   **熟练度等级 (Proficiency Level) - [核心功能]**: 对于每一个已勾选的技能，旁边都有一个下拉菜单，可以选择其熟练度，如：
            *   `1 - 了解`
            *   `2 - 熟练`
            *   `3 - 精通`
            *   `4 - 专家`
        *   这个熟练度等级，会在“基于技能匹配”的派单算法中，作为非常重要的权重因子。

#### **标签页二：工作负载模型管理 (Workload Model Management)**

这个部分负责定义“忙闲”状态。

**1. 负载计算规则 (Workload Calculation Rules)**
*   **全局设置**:
    *   **`[ 计算负载时考虑的工单状态 ]`**: 一个多选框，通常会勾选 `[待处理]` 和 `[处理中]`。
    *   **`[ 是否启用基于工单权重的负载计算 ]`**: 一个开关。
        *   **关闭时 (简单模式)**: 员工的负载 = 其名下符合上述状态的**工单数量**。
        *   **开启时 (加权模式) - [高级功能]**: 员工的负载 = 其名下所有工单的**权重之和**。

*   **工单权重配置 (如果开启了加权模式)**:
    *   一个条件规则构建器，允许管理员为不同类型的工单设置不同的“负载权重值”。
    *   **示例规则**:
        *   如果 `[优先级]` `等于` `紧急`, 则 `权重` = `3`
        *   如果 `[优先级]` `等于` `高`, 则 `权重` = `2`
        *   如果 `[优先级]` `等于` `中` 或 `低`, 则 `权重` = `1`
    *   **价值**: 这种模式比简单地计算数量更公平。一个处理了3个紧急工单的员工（负载9），显然比一个处理了5个低优工单的员工（负载5）更忙。

**2. 员工负载上限与状态 (Agent Capacity & Status)**
*   **交互界面**: 同样是以“员工”为维度的配置列表。
*   **筛选器**: 按部门/团队、员工姓名筛选。
*   **员工负载列表**:
    *   **表头**: `员工姓名`, `部门/团队`, `**负载上限**`, `**当前工作状态**`, `操作`。
    *   `**负载上限**`: 一个数字输入框，管理员可以在此为每个员工设置其最大可承载的负载值（无论是数量还是权重和）。当员工的实时负载达到此上限时，智能派单将自动跳过他。可以提供一个“应用到整个团队”的快捷操作。
    *   `**当前工作状态**`: **[实时数据]** 这个字段通常是**只读**的，它会从外部系统（如排班系统、OA、钉钉/企业微信）同步或由员工手动设置其当前状态。
        *   `在线/空闲` (可接收派单)
        *   `会议中` (临时不可派单)
        *   `休假中` (长期不可派单)
        *   `离线`

---