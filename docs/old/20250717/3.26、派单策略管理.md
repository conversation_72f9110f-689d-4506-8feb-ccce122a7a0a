
---

### **“派单策略管理”页面内容详解**

#### **一、 页面核心目标**

1.  **创建多样化策略**: 能够定义多种派单策略，以应对不同业务场景的特定需求。
2.  **精确设定应用范围**: 将每种策略精确地应用到符合特定条件的工单上。
3.  **灵活选择派单算法**: 提供多种核心派单算法，并支持组合使用，以实现最优的资源匹配。
4.  **管理策略优先级**: 在一个工单可能满足多个策略条件时，有明确的规则决定应用哪一个。
5.  **提供回退与兜底机制**: 当智能派单找不到合适的处理人时，有明确的备用方案。

---

### **二、 页面内容与布局**

页面分为两个视图：
1.  **策略列表视图 (Strategy List View)**: 管理所有派单策略。
2.  **策略构建器/编辑器视图 (Strategy Builder/Editor View)**: 创建或编辑单个策略。

#### **模式一：策略列表视图**

*   **页面顶部**: 一个醒目的 `[ + 新建派单策略 ]` 按钮。
*   **搜索框**: 按策略名称搜索。
*   **策略列表**: 以表格形式展示所有已创建的派单策略。
    *   **表头**: `优先级/顺序`, `策略名称`, `应用条件摘要`, `派单算法`, `状态 (启用/停用)`, `最后修改时间`, `操作`。
    *   **优先级/顺序**: **[核心字段]** 这是一个可**拖拽排序**的列表。系统会从上到下（或按数字从小到大）依次检查一个新工单是否满足策略条件。**一旦满足第一个策略，就不会再继续检查后续策略**。这确保了规则的唯一性和确定性。
    *   **行末操作**: `[ 编辑 ]`, `[ 复制 ]`, `[ 启用/停用 ]`, `[ 删除 ]`。

---

#### **模式二：策略构建器/编辑器视图**

这是最核心的界面，清晰地分为多个逻辑配置块。

**1. 策略基本信息**

*   **策略名称**: [必填] 如“VIP客户紧急技术支持派单”。
*   **策略描述**: 解释此策略的目标和逻辑。
*   **状态**: `[ 启用 ]` / `[ 停用 ]` 的开关。

**2. “当工单满足以下条件时，应用此策略” - 应用条件**

*   使用强大的**条件构建器**，定义此策略的触发条件。
*   **示例**:
    *   `[客户级别]` `等于` `VIP`
    *   **AND** `[工单模板]` `等于` `技术支持`
    *   **AND** `[优先级]` `等于` `紧急`

**3. “从以下范围中选择处理人” - 候选人范围 (Candidate Pool)**

*   **团队/部门选择**: 一个树形多选框，定义从哪些团队中寻找合适的处理人。例如，可以选择“L2技术支持团队”和“L3核心研发团队”。

**4. “按照以下算法进行派单” - 派单算法配置 (Dispatch Algorithm)**

这是整个页面的核心与灵魂。

*   **算法模式选择 (单选按钮)**:
    *   **🔘 简单模式 (Simple Mode)**:
        *   `轮询派单 (Round-robin)`: 在候选人中依次轮流。
        *   `最少任务派单 (Least-loaded)`: 派给当前任务最少的人。
        *   `随机派单 (Random)`: 随机选择一个候选人。
    *   **🔘 智能模式 (Intelligent Mode)**:
        *   `基于技能匹配 (Skills-based)`: 根据工单所需技能与员工技能画像进行匹配。
        *   `基于历史经验 (History-based)`: 寻找历史上处理过最相似工单的员工。
    *   **🔘 混合加权模式 (Hybrid Weighted Mode) - [最强大的模式]**:
        *   当选中此模式，下方会展现一个**权重配置列表**。管理员可以添加多个评估维度，并为每个维度分配权重（总和为100%）。
        *   **可添加的评估维度**:
            *   `技能匹配度`: 权重 `50%`
            *   `当前负载 (任务数)`: 权重 `30%` (可设置为负载越低，得分越高)
            *   `历史客户评价`: 权重 `10%`
            *   `历史解决效率`: 权重 `10%`
            *   `语言能力` (如果涉及多语言支持)
            *   ...
        *   系统会根据这个加权公式，为每一个候选人计算出一个综合得分，然后将工单派发给得分最高的人。

**5. “如果找不到合适的处理人” - 回退与兜底机制 (Fallback Mechanism)**

这是保证系统健壮性的重要部分。

*   **回退操作选择 (下拉菜单)**:
    *   **`指派给指定人员/团队 (Assign to a specific person/team)`**:
        *   如果智能派单失败，自动将工单指派给一个预设的“负责人”或“主管”。这是最常见的做法。
    *   **`放入指定的工单池 (Move to a specific work order pool)`**:
        *   将工单放入一个“疑难工单池”，等待管理者或有经验的员工手动认领。
    *   **`发送通知给... (Send notification to...)`**:
        *   不改变工单状态，但向指定的管理者或渠道（如Slack频道）发送一条紧急通知，请求人工干预。

**页面底部操作按钮**: `[ 保存策略 ]`, `[ 取消 ]`。

---