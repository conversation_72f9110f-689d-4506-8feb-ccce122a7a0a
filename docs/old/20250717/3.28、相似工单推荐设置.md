
---

### **“相似工单推荐设置”页面内容详解**

#### **一、 页面核心目标**

1.  **定义相似度模型**: 让管理员能够配置和微调计算工单相似度的算法和参数。
2.  **设定推荐触发时机**: 决定在哪些页面的、什么操作下触发推荐。
3.  **配置推荐结果展示**: 定义推荐出的相似工单以何种形式、显示哪些信息。
4.  **管理数据源与索引**: 控制哪些历史工单可以被用作推荐源，并管理其数据索引。

---

### **二、 页面内容与布局**

页面通常采用**多标签页 (Tabs)** 的布局，将复杂的配置项进行逻辑分区。

#### **标签页一：推荐触发与展示 (Trigger & Display)**

这部分负责配置相似工单推荐功能在前端的“表现”。

*   **触发场景配置**:
    *   一组开关，用于启用或停用在不同场景下的推荐功能。
    *   `[ ✓ ]` **在“新建工单”页面**: 当用户输入`标题`和`描述`时，实时推荐。
    *   `[ ✓ ]` **在“工单详情”页面**: 打开一个工单详情时，在侧边栏或专门的区域展示相似工单。
    *   `[   ]` **在“工单池”页面**: 当用户预览一个待抢工单时，显示相似工单。

*   **推荐结果配置**:
    *   **最大推荐数量**: 输入框，设置一次最多推荐 `5` 条相似工单。
    *   **推荐卡片显示内容**:
        *   一个多选勾选列表，让管理员选择推荐出的相似工单卡片上应该显示哪些核心信息，以便用户快速判断其价值。
        *   `[✓] 工单ID`
        *   `[✓] 标题`
        *   `[✓] 最终解决方案` (如果已解决)
        *   `[✓] 处理人`
        *   `[ ] 客户满意度评分`
        *   `[✓] 创建时间`
        *   `[ ] 客户名称`
    *   **“一键复用”功能配置**:
        *   一个开关：`[ ✓ ] 启用“一键复用”功能`。
        *   当启用时，下方会出现一个多选框，让管理员配置点击“复用”时，可以复制哪些字段到当前工单中：
            *   `[✓] 处理人`
            *   `[✓] 标签`
            *   `[ ] 优先级`
            *   `[✓] 解决方案` (复制到描述区或专门的解决方案区)

#### **标签页二：相似度算法配置 (Similarity Algorithm) - [核心技术配置]**

这部分是整个功能的大脑，定义了“如何计算相似”。

*   **数据源范围 (Data Source Scope)**:
    *   `用于推荐的历史工单状态`: 多选框，通常会选择 `[已关闭]`、`[已解决]`。
    *   `排除特定工单类型`: 可以选择某些不具有参考价值的工单模板（如“行政申请”）不参与推荐计算。
    *   `仅使用高满意度工单`: 一个开关，勾选后，只有客户评价高于4星的工单才会被作为推荐源。

*   **特征字段与权重 (Feature Fields & Weights)**:
    *   这是**最重要的配置区域**。一个列表，展示了所有可用于计算相似度的工单字段，管理员可以为每个字段**启用/停用**并**设定权重**（通常用滑块或0-100的输入框）。
    *   **文本字段 (Text Fields)**:
        *   `[✓] 标题`: 权重 `80`
        *   `[✓] 描述`: 权重 `60`
        *   `[ ] 解决方案`: 权重 `(可配置)`
        *   **算法选择**: 对于文本字段，可以选择使用的算法，如 `TF-IDF` (传统，快) 或 `Bert/Sentence-Transformers` (先进，更理解语义，慢)。
    *   **分类字段 (Categorical Fields)**:
        *   `[✓] 工单类型/模板`: 权重 `50`
        *   `[✓] 工单标签`: 权重 `70`
        *   `[ ] 优先级`: 权重 `(可配置)`
    *   **最终相似度得分 = (标题相似分 * 80%) + (描述相似分 * 60%) + ...**

*   **相似度阈值 (Similarity Threshold)**:
    *   一个0-1之间的数字输入框（如 `0.75`）。只有计算出的综合相似度得分高于此阈值的历史工单，才会被认为是“相似”并出现在推荐列表中。

#### **标签页三：索引管理 (Index Management)**

这部分负责数据后台的处理。

*   **索引状态**: 显示当前索引库中的工单数量、最后一次更新时间等信息。
*   **手动操作**:
    *   `[ 立即重建完整索引 ]`: 当算法或权重发生重大变更后，需要点击此按钮，系统会在后台对所有符合条件的历史工单重新提取特征并计算索引。这是一个耗时较长的后台任务。
    *   `[ 更新增量索引 ]`: 系统默认会定期自动运行，但这里提供一个手动触发的按钮。
*   **自动更新计划**:
    *   设置增量索引的自动更新频率，如“每小时一次”。

---