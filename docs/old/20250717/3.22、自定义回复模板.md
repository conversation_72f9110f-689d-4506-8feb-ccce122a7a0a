
---

### **“自定义回复模板”页面内容详解**

#### **一、 页面核心目标**

1.  **创建和管理标准回复**: 建立一个标准化的回复内容库。
2.  **支持动态变量**: 模板不是一成不变的，能够自动填充工单中的具体信息（如客户姓名、工单ID）。
3.  **分类与共享**: 对模板进行分类，并能控制模板的可见范围（个人专用或团队/全局共享）。
4.  **提升效率与专业度**: 确保一线员工的对外沟通既快速又专业，保持品牌形象的一致性。

---

### **二、 页面内容与布局**

页面通常采用**“分类树 + 模板列表 + 编辑区”**的三栏式经典布局，或者**“列表 + 弹窗编辑”**的模式。这里我们以功能更强大的三栏式布局来描述。

#### **区域一：模板分类/文件夹区 (Category/Folder Tree) - [左侧栏]**

*   **树形结构**: 以文件夹的形式对模板进行分类，方便管理和查找。
*   **默认分类**:
    *   `我的模板` (个人私有)
    *   `共享模板` (团队或全局可见)
*   **自定义分类**: 管理员或用户可以创建自己的分类文件夹，例如：
    *   `售前咨询类`
    *   `技术排错步骤`
    *   `安抚话术`
    *   `服务满意度调查`
*   **交互**: 点击不同的文件夹，右侧的模板列表会相应刷新。提供对文件夹的“新建”、“重命名”、“删除”操作。

#### **区域二：模板列表区 (Template List) - [中间主栏]**

*   **页面顶部**: 一个 `[ + 新建模板 ]` 按钮（在此文件夹下）。
*   **搜索框**: 在当前分类下，按模板标题或内容关键词搜索。
*   **模板列表**: 以简化的列表或卡片形式展示当前分类下的所有模板。
    *   **列表项内容**:
        *   `模板标题`: 清晰地表明此模板的用途。
        *   `内容摘要`: 显示模板内容的前几十个字。
        *   `类型`: 标明是“个人”还是“共享”。
        *   `使用频率 (可选)`: 显示此模板被调用的次数，帮助识别最受欢迎的模板。
    *   **交互**: 点击列表中的某一项，右侧的编辑区会加载该模板的完整内容进行查看或编辑。

#### **区域三：模板编辑器/详情区 (Template Editor/Details) - [右侧栏]**

这是创建和编辑模板的核心区域。

*   **模板标题 (Template Title)**: [必填] 一个简洁明了的名称，方便在工单页面中快速搜索和选择。例如，“首次响应确认模板”。

*   **模板内容 (Template Content)**: [核心]
    *   一个功能丰富的**富文本编辑器**，支持加粗、斜体、列表、链接、插入图片等。
    *   用户可以在这里编写标准化的回复文本。

*   **动态变量/占位符插入器 (Dynamic Variables / Placeholders)**:
    *   **[本页面最智能的功能]** 编辑器旁边或上方会有一个“**插入变量**”的按钮或下拉菜单。
    *   点击后，会列出所有可以动态插入的变量，例如：
        *   **工单信息**: `{{work_order.id}}` (工单ID), `{{work_order.title}}` (工单标题)
        *   **客户信息**: `{{customer.name}}` (客户姓名), `{{customer.company}}` (客户公司)
        *   **当前用户信息**: `{{current_user.name}}` (当前操作员姓名), `{{current_user.signature}}` (签名档)
        *   **处理人信息**: `{{agent.name}}` (工单处理人姓名)
    *   用户在编辑器中点击某个变量，它就会以 `{{...}}` 的形式插入到文本中。当一线员工在工单中实际使用此模板时，系统会自动将这些变量替换为真实的数据。

*   **模板共享设置 (Sharing Settings)**:
    *   一组单选按钮或下拉菜单，用于定义模板的可见范围。
    *   `仅自己可见 (Personal)`
    *   `共享给我的团队 (Team)`
    *   `共享给所有人 (Global)` (此选项通常仅管理员可见)

*   **操作按钮 (Action Buttons)**:
    *   **`[ 保 存 ]`**: 保存对模板的修改。
    *   **`[ 另存为... ]`**: 将当前模板复制为一个新的个人模板。
    *   **`[ 删 除 ]`**: 删除此模板。

---

### **使用场景示例**

**模板内容示例 - “首次响应确认模板”**:
> 您好 `{{customer.name}}`，
>
> 感谢您的联系！我们已经收到了您关于“`{{work_order.title}}`”的问题，并创建了工单，编号为 **`{{work_order.id}}`**。
>
> 我们的技术专家 **`{{agent.name}}`** 正在处理您的问题，他/她会尽快与您联系。
>
> 祝好，
> `{{current_user.name}}`
> `{{current_user.signature}}`

当客服在工单详情页一键调用此模板时，所有 `{{...}}` 都会被自动替换，生成一封专业且个性化的邮件或回复。

---