<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12345政务服务热线系统完整业务流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .description {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 5px 5px 0;
        }
        
        .description h2 {
            color: #333;
            margin-top: 0;
            font-size: 1.5em;
        }
        
        .description ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .description li {
            margin: 8px 0;
            color: #555;
        }
        
        .diagram-container {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e0e0e0;
        }
        
        /* Mermaid 图表样式优化 */
        .mermaid {
            text-align: center;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .btn {
                display: block;
                margin: 10px auto;
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>12345政务服务热线系统</h1>
            <p>完整业务流程图</p>
        </div>
        
        <div class="content">
            <div class="description">
                <h2>系统流程特点</h2>
                <ul>
                    <li><strong>"条块结合"的分派策略</strong>：市级分派时判断走"条线"（市级职能部门）还是"块线"（区级属地管理）</li>
                    <li><strong>多层级即时办结机会</strong>：每个接收层级都可以判断是否能即时办结，提高处理效率</li>
                    <li><strong>严格的原路返回审核</strong>：审核路径是派发路径的精确逆过程，确保责任可追溯</li>
                    <li><strong>多部门协同处理机制</strong>：支持主办部门和协办部门的协同工作模式</li>
                    <li><strong>统一的回访关闭流程</strong>：所有工单最终都要经过市级回访，保证服务质量</li>
                </ul>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="zoomIn()">放大图表</button>
                <button class="btn" onclick="zoomOut()">缩小图表</button>
                <button class="btn" onclick="resetZoom()">重置大小</button>
                <button class="btn" onclick="downloadSVG()">下载图表</button>
            </div>
            
            <div class="diagram-container">
                <div class="mermaid" id="mermaid-diagram">
sequenceDiagram
    participant 市民 as 市民
    participant 市级话务员 as 市级12345中心话务员/分派员
    participant 市级部门 as 市级职能部门
    participant 市级科室 as 市级业务科室
    participant 市级工作人员 as 市级工作人员
    participant 区级中心 as 区/县级12345分中心
    participant 区级部门 as 区级职能部门
    participant 区级科室 as 区级业务科室
    participant 区级工作人员 as 区级工作人员
    participant 街镇中心 as 街/镇级总承办单位
    participant 社区 as 社区/村委会
    participant 网格员 as 网格员
    participant 回访员 as 市级回访中心

    Note over 市民, 回访员: 阶段一：市级统一受理与顶层战略分派

    市民->>市级话务员: 1. 通过12345热线提出诉求
    市级话务员->>市级话务员: 2. 新建工单，录入标准化信息
    
    alt 场景A：即时办结
        市级话务员->>市级话务员: 判断：政策咨询/信息查询等
        市级话务员->>市民: 利用知识库直接答复
        市级话务员->>市级话务员: 即时办结，工单状态→【已关闭】
        Note over 市级话务员: 跳过所有下派环节
    else 场景B：派发至市级职能部门（条线处理）
        市级话务员->>市级部门: 3. 派发工单（全市范围/跨区域/市级垂直管理事项）
        
        Note over 市级部门, 市级工作人员: 市级职能部门内部流转
        
        alt 市级部门即时办结
            市级部门->>市级部门: 判断能否即时办结
            市级部门->>市级部门: 即时办结
        else 市级部门内部分派
            市级部门->>市级科室: 4. 下派至内部业务处室/科室
            
            alt 市级科室即时办结
                市级科室->>市级科室: 判断能否即时办结
                市级科室->>市级科室: 即时办结
            else 市级科室指派工作人员
                市级科室->>市级工作人员: 5. 指派给具体工作人员
                市级工作人员->>市级工作人员: 执行任务（调查、报告等）
                市级工作人员->>市级工作人员: 补记过程，点击办结
            end
        end
        
        Note over 市级工作人员, 市级部门: 市级审核流程（原路返回）
        
        opt 如果有工作人员办结
            市级工作人员->>市级科室: 办结结果提交审核
            市级科室->>市级科室: 审核（可退回）
            市级科室->>市级部门: 审核通过，提交上级审核
        end
        市级部门->>市级部门: 最终审核
        市级部门->>回访员: 向市级平台提交办结反馈
        
    else 场景C：派发至区/县级总口（块线处理）
        市级话务员->>区级中心: 3. 根据地域属性派发工单
        
        Note over 区级中心, 网格员: 区级下沉后的复杂流转
        
        alt 区级中心即时办结
            区级中心->>区级中心: 判断能否即时办结
            区级中心->>区级中心: 即时办结
        else 区级中心二次分派
            alt 派发至街/镇（属地路线）
                区级中心->>街镇中心: 4A. 派发至街/镇级总承办单位
                
                alt 街镇即时办结
                    街镇中心->>街镇中心: 判断能否即时办结
                    街镇中心->>街镇中心: 即时办结
                else 街镇下派
                    街镇中心->>社区: 下派至社区/村委会
                    
                    alt 社区即时办结
                        社区->>社区: 判断能否即时办结
                        社区->>社区: 即时办结
                    else 社区指派网格员
                        社区->>网格员: 最终指派给网格员
                        网格员->>网格员: 执行现场任务
                        网格员->>网格员: 完成后点击办结
                    end
                end
                
                Note over 网格员, 区级中心: 属地路线审核流程（原路返回）
                
                opt 如果有网格员办结
                    网格员->>社区: 办结结果提交审核
                    社区->>社区: 审核（可退回）
                    社区->>街镇中心: 审核通过，提交上级审核
                    街镇中心->>街镇中心: 审核（可退回）
                    街镇中心->>区级中心: 审核通过，提交上级审核
                end
                
            else 派发至区职能部门（职能路线）
                区级中心->>区级部门: 4B. 派发至区级职能部门
                
                alt 区级部门即时办结
                    区级部门->>区级部门: 判断能否即时办结
                    区级部门->>区级部门: 即时办结
                else 区级部门内部分派
                    区级部门->>区级科室: 下派至业务科室
                    
                    alt 区级科室即时办结
                        区级科室->>区级科室: 判断能否即时办结
                        区级科室->>区级科室: 即时办结
                    else 区级科室指派工作人员
                        区级科室->>区级工作人员: 最终指派给一线工作人员
                        区级工作人员->>区级工作人员: 执行任务
                        区级工作人员->>区级工作人员: 完成后点击办结
                    end
                end
                
                Note over 区级工作人员, 区级中心: 职能路线审核流程（原路返回）
                
                opt 如果有工作人员办结
                    区级工作人员->>区级科室: 办结结果提交审核
                    区级科室->>区级科室: 审核（可退回）
                    区级科室->>区级部门: 审核通过，提交上级审核
                    区级部门->>区级部门: 审核（可退回）
                    区级部门->>区级中心: 审核通过，提交上级审核
                end
            end
        end
        
        区级中心->>区级中心: 最终审核
        区级中心->>回访员: 向市级平台提交办结反馈
        
    else 场景D：市级多部门协同
        Note over 市级话务员: 指定主办部门和协办部门
        市级话务员->>市级部门: 派发给主办部门（总协调）
        市级话务员->>区级中心: 同时派发给协办部门
        
        par 主办部门处理
            市级部门->>市级部门: 专业调查、技术鉴定
        and 协办部门处理
            区级中心->>区级中心: 提供专业支持或配合处理
        end
        
        区级中心->>市级部门: 协办部门完成，更新任务状态
        市级部门->>市级部门: 汇总所有处理情况，形成最终办结报告
        市级部门->>回访员: 主办部门办结，提交反馈
    end

    Note over 回访员: 阶段五：市级统一回访与最终关闭

    回访员->>回访员: 工单状态更新为【待回访】
    回访员->>市民: 10. 独立回访，询问满意度
    
    alt 市民满意
        市民->>回访员: 满意反馈
        回访员->>回访员: 11. 工单状态→【已关闭】
    else 市民不满意
        市民->>回访员: 不满意反馈
        回访员->>市级话务员: 重启工单，附上"重办督办"意见
        Note over 市级话务员: 启动新一轮更高级别督办处理流程
    end
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 12345政务服务热线系统 | 业务流程图展示</p>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });

        let currentZoom = 1;
        
        function zoomIn() {
            currentZoom += 0.1;
            applyZoom();
        }
        
        function zoomOut() {
            if (currentZoom > 0.3) {
                currentZoom -= 0.1;
                applyZoom();
            }
        }
        
        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }
        
        function applyZoom() {
            const diagram = document.querySelector('.mermaid svg');
            if (diagram) {
                diagram.style.transform = `scale(${currentZoom})`;
                diagram.style.transformOrigin = 'top left';
            }
        }
        
        function downloadSVG() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                const svgUrl = URL.createObjectURL(svgBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = svgUrl;
                downloadLink.download = '12345政务服务热线系统流程图.svg';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(svgUrl);
            }
        }
        
        // 等待图表渲染完成后应用初始缩放
        setTimeout(() => {
            applyZoom();
        }, 1000);
    </script>
</body>
</html>
